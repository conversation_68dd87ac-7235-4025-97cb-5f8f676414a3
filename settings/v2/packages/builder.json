{"__version__": "1.3.9", "textureCompressConfig": {"genMipmaps": false}, "bundleConfig": {"custom": {"auto_d7hsevSKBLJIM7rorkrQc/": {"displayName": "Lobby", "configs": {"native": {"preferredOptions": {"compressionType": "merge_dep", "isRemote": false}, "overwriteSettings": {"android": {"compressionType": "merge_all_json", "isRemote": true}, "ios": {"compressionType": "merge_dep", "isRemote": true}}}, "miniGame": {"configMode": "overwrite", "overwriteSettings": {"alipay-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "taobao-creative-app": {"compressionType": "merge_dep", "isRemote": false}, "taobao-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "bytedance-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "oppo-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "huawei-quick-game": {"compressionType": "merge_dep", "isRemote": false}, "vivo-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "xiaomi-quick-game": {"compressionType": "merge_dep", "isRemote": false}, "baidu-mini-game": {"compressionType": "merge_dep", "isRemote": false}, "wechatgame": {"compressionType": "merge_dep", "isRemote": false}, "link-sure": {"compressionType": "merge_dep", "isRemote": false}, "qtt": {"compressionType": "merge_dep", "isRemote": false}, "cocos-play": {"compressionType": "merge_dep", "isRemote": false}}}, "web": {"preferredOptions": {"compressionType": "merge_all_json", "isRemote": false}, "overwriteSettings": {}}}}}}}