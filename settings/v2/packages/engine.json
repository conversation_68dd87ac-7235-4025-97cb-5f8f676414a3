{"__version__": "1.0.12", "modules": {"graphics": {"pipeline": "legacy-pipeline"}, "configs": {"migrationsConfig": {"cache": {"base": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": true}, "gfx-webgpu": {"_value": false}, "animation": {"_value": true}, "skeletal-animation": {"_value": true}, "3d": {"_value": true}, "meshopt": {"_value": false}, "2d": {"_value": true}, "xr": {"_value": true}, "rich-text": {"_value": true}, "mask": {"_value": true}, "graphics": {"_value": true}, "ui-skew": {"_value": false}, "affine-transform": {"_value": true}, "ui": {"_value": true}, "particle": {"_value": true}, "physics": {"_value": true, "_option": "physics-ammo"}, "physics-ammo": {"_value": true, "_flags": {"LOAD_BULLET_MANUALLY": false}}, "physics-cannon": {"_value": true}, "physics-physx": {"_value": true, "_flags": {"LOAD_PHYSX_MANUALLY": false}}, "physics-builtin": {"_value": true}, "physics-2d": {"_value": true, "_option": "physics-2d-builtin"}, "physics-2d-box2d": {"_value": true}, "physics-2d-box2d-wasm": {"_value": false, "_flags": {"LOAD_BOX2D_MANUALLY": false}}, "physics-2d-builtin": {"_value": true}, "physics-2d-box2d-jsb": {"_value": false}, "intersection-2d": {"_value": true}, "primitive": {"_value": true}, "profiler": {"_value": true}, "occlusion-query": {"_value": true}, "geometry-renderer": {"_value": true}, "debug-renderer": {"_value": true}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": true}, "webview": {"_value": true}, "tween": {"_value": true}, "websocket": {"_value": true}, "websocket-server": {"_value": false}, "terrain": {"_value": true}, "light-probe": {"_value": true}, "tiled-map": {"_value": true}, "vendor-google": {"_value": false}, "spine": {"_value": true, "_option": "spine-4.2"}, "spine-3.8": {"_value": true, "_flags": {"LOAD_SPINE_MANUALLY": false}}, "spine-4.2": {"_value": false, "_flags": {"LOAD_SPINE_MANUALLY": false}}, "dragon-bones": {"_value": true}, "marionette": {"_value": true}, "procedural-animation": {"_value": false}, "custom-pipeline-post-process": {"_value": false}, "render-pipeline": {"_value": true, "_option": "legacy-pipeline"}, "custom-pipeline": {"_value": true}, "legacy-pipeline": {"_value": false}}, "flags": {"LOAD_SPINE_MANUALLY": false, "LOAD_BULLET_MANUALLY": false}, "name": "Migrated Configuration", "includeModules": ["2d", "3d", "affine-transform", "animation", "audio", "base", "debug-renderer", "dragon-bones", "geometry-renderer", "gfx-webgl", "gfx-webgl2", "graphics", "intersection-2d", "legacy-pipeline", "light-probe", "marionette", "mask", "occlusion-query", "particle", "particle-2d", "physics-2d-builtin", "physics-ammo", "primitive", "profiler", "rich-text", "skeletal-animation", "spine-4.2", "terrain", "tiled-map", "tween", "ui", "video", "websocket", "webview", "xr"], "noDeprecatedFeatures": {"value": false, "version": ""}}}, "globalConfigKey": "migrationsConfig"}, "macroConfig": {"ENABLE_TRANSPARENT_CANVAS": true}}