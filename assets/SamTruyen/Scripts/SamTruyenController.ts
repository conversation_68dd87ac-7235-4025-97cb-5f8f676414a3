import { _decorator, Animation, BlockInputEvents, Button, Component, easing, Label, math, Node, ParticleSystem2D, Sprite, Toggle, tween, UIOpacity, UIRenderer, Vec3 } from "cc";
import App from "../../Lobby/scripts/common/App";
import { Utils } from "../../Lobby/scripts/common/Utils";
import Configs from "../../Lobby/scripts/common/Config";
import { BonusGame, SamTruyenGameData, SOUND_ID, X2Game } from "./SamTruyen.schema";
import { SamTruyenData } from "./SamTruyenData";
import { SamTruyenItemController } from "./SamTruyenItemController";
import BundleControl from "../../Loading/scripts/BundleControl";
import { formatTimeSpanToCountdown, splitNumberFromString } from "./SamTruyen.Utils";
import { SamTruyenPopUpController } from "./SamTruyenPopUpController";
import { SamTruyenScatterContrller } from "./SamTruyenScatterContrller";
import { SamTruyenX2Controller } from "./SamTruyenX2Controller";
import { SamTruyenAudioController } from "./SamTruyenAudioController";
import { spinData } from "./SamTruyen.const";
import Http from "../../Lobby/scripts/common/Http";
import { SamTruyenNetWorkSignalR } from "../../Lobby/scripts/common/networks/SamTruyenSignalRClient";
import { SpawnCoinEffect } from "./SpawnCoinEffect";
const { ccclass, property } = _decorator;

@ccclass("SamTruyenController")
export class SamTruyenController extends Component {
  public static Instance: SamTruyenController;

  @property(Sprite)
  AvatarNode: Sprite;

  @property(Label)
  NameLabel: Label;

  @property(Label)
  GoldLabel: Label;

  @property(Label)
  BetLabel: Label;

  @property(Label)
  TotalBetLabel: Label;

  @property(Label)
  TotalWinLabel: Label;

  @property(Label)
  JackpotLabel: Label;

  @property(Label)
  LineLabel: Label;

  @property(Node)
  Content: Node;

  @property(Node)
  Line: Node;

  @property(Node)
  LineLayerTop: Node;

  @property(Node)
  BtnPlus: Node;

  @property(Node)
  BtnStopAuto: Node;

  @property(Node)
  LayoutBtns: Node;

  @property(Node)
  Loading: Node;

  @property(Node)
  GameNode: Node;

  @property(Node)
  ScatterNode: Node;

  @property(Node)
  X2Node: Node;

  @property(Label)
  SessionLabel: Label;

  @property(Button)
  X2Button: Button;

  @property(Button)
  SpinBtn: Button;

  @property(Button)
  UIBtns: Button[] = [];

  @property(Animation)
  Hades: Animation;

  @property(Animation)
  Athena: Animation;

  @property(Toggle)
  PlayingGameModeToggle: Toggle;

  @property(Node)
  AutoSpinInfo: Node;

  @property(Node)
  IsSpinningNode: Node;

  @property(Node)
  LightTreasure: Node;

  @property(Label)
  WinPrizeLabel: Label;

  @property(Label)
  FreeSpinLabel: Label;

  @property(Label)
  TimeSpanLabel: Label;

  @property(Label)
  RevenueLabel: Label;

  @property(Label)
  CalculateLabel: Label;

  @property(Label)
  FreeTicketLabel: Label;

  @property(Node)
  MoneyParticle: Node;

  @property(SamTruyenPopUpController)
  PopUp: SamTruyenPopUpController;

  @property(SpawnCoinEffect)
  MoneyRain: SpawnCoinEffect;

  @property(SamTruyenItemController)
  Items: SamTruyenItemController[] = [];

  private _gameData: SamTruyenGameData = null;
  private roomId: number = 1;
  private delayBetween: number = 0.25;

  private spinDuration: number = 2;

  private columnHeight: number = 542;
  private isSpinning: boolean = false;

  private isFastSpinMode: boolean = false;

  private isClickingPlus: boolean = false;

  private currentJackPot: number = 0;
  private jackpotChange: number = 0;
  private jackpotSpeed: number = 100000;
  private isFreeSpin: boolean = false;
  private accumulateItemCount: number = 0;
  private isPlayingMiniGame: boolean = false;
  private isLocking: boolean = false;
  private isAutoSpin: boolean = false;
  private getFreeSpinPay: number = 0;

  private currentAutoCount: number = 0;

  private currentLine: number = 0;
  private isTryGameMode: boolean = false;
  private readonly minLine: number = 1;
  private readonly maxLine: number = 25;

  private currentValue: number = 0;
  private targetValue: number = 0;
  private isShowValue: boolean = false;
  private remainingTime: number = 0;
  private freeTicket: number = 0;
  private x2GameWin: number = 0;
  private trialGoldBalace: number = 0;
  private autoSpinWinCount: number = 0;
  private autoSpinWinToAmount: number = 0;
  private autoSpinLoseToAmount: number = 0;
  public get GameData(): SamTruyenGameData {
    return this._gameData;
  }

  public get IsTryGameMode(): boolean {
    return this.isTryGameMode;
  }

  protected onLoad(): void {
    SamTruyenController.Instance = this;
    BundleControl.loadBundle("SamTruyen", () => {});
  }

  protected start(): void {
    if (this.Loading) {
      this.Loading.active = true;
    }
    if (this.GameNode) {
      this.GameNode.active = false;
    }
    this.roomId = 1;

    this.checkFreeTicket();

    SamTruyenNetWorkSignalR.getInstance().receive("JoinGame", (data: any) => {
      console.log("Sam Truyen: ", data);
      if (data) {
        this._gameData = data;
        this.setInfoPlayer();
        this.setDataService();
        this.setAccumulateData();
        this.switchToMiniGame(this._gameData.SpinData.GameStatus);
        if (this.SessionLabel) {
          this.SessionLabel.string = `#${this._gameData.SpinData.SpinID}`;
        }

        if (this._gameData.AccumulateGame.Items) {
          this.accumulateItemCount = this._gameData.AccumulateGame.Items;
          this.addAccumulateItemOnAthena(this._gameData.AccumulateGame.Items);
        }

        if (this.TotalWinLabel) {
          this.TotalWinLabel.string = this._gameData.SlotInfo.LastPrizeValue.toLocaleString("vi");
        }
        if (this.X2Button) {
          this.X2Button.interactable = false;
        }
        this.stopX2Game();

        // if (this.PopUp) {
        //   this.PopUp.pushPopup({ key: "Treasure", balance: 0 });
        //   this.PopUp.pushPopup({ key: "TreasureWin", balance: 10000 });
        //   this.PopUp.pushPopup({ key: "BigWin", balance: 20000 });
        //   this.PopUp.pushPopup({ key: "Jackpot", balance: 30000 });
        //   this.PopUp.pushPopup({ key: "FreeSpin", balance: 4 });
        //   this.PopUp.pushPopup({ key: "Bonus", balance: 0 });
        // }
      }
    });

    SamTruyenNetWorkSignalR.getInstance().receive("UpdateJackPot", (data: any) => {
      console.log("Update Jackpot: ", data);
      if (data) {
        this.jackpotChange = data;
        if (data > this.jackpotSpeed) this.jackpotSpeed = data;
      }
    });

    SamTruyenNetWorkSignalR.getInstance().receive("ResultSpin", (data: any) => {
      console.log("Spin Result: ", data);
      SamTruyenAudioController.Instance.playSfx(SOUND_ID.SPIN_REEL);
      this.__onHandleResultSpin(data);
      if (this.X2Button) {
        this.X2Button.interactable = false;
      }
    });

    SamTruyenNetWorkSignalR.getInstance().receive("MessageError", (code: number) => {
      console.error("Error Sam Truyen: ", code);
    });

    SamTruyenNetWorkSignalR.getInstance().receive("resultBonusGame", (data: any) => {
      console.log("Bonus game: ", data);
      this.__onHandleResultBonusGame(data);
    });

    SamTruyenNetWorkSignalR.getInstance().receive("ResultX2Game", (data: any) => {
      console.log("X2 Game: ", data);
      this.__onHandleResultX2Game(data);
    });
  }

  private __onHandleResultBonusGame(data: any) {
    if (data) {
      if (!this.isPlayingMiniGame) {
        this.__openScatterGame(this._gameData.BonusGame);
        let comp = this.ScatterNode.getComponent(SamTruyenScatterContrller);
        if (comp) {
          comp.setPrizeBonusID(data.BonusPrizeID, this.roomId);
        }
      } else {
        let comp = this.ScatterNode.getComponent(SamTruyenScatterContrller);
        if (comp) {
          comp.onCheckTouchScopion(data);
        }
      }
    }
  }

  private __onHandleResultX2Game(data: X2Game) {
    if (data) {
      if (this.X2Button) {
        this.X2Button.interactable = false;
      }
      if (data.IsStop) {
        if (this.X2Node) this.X2Node.active = false;
      } else {
        this.__openX2Game(data);
      }
    }
  }

  private exitClick() {
    if (this.isAutoSpin) {
      App.instance.confirmDialog.showMsg(App.instance.getTextLang("sl74"), () => {
        this.goBack();
      });
    } else {
      this.goBack();
    }
  }

  private __onHandleResultSpin(data: SamTruyenGameData) {
    if (data) {
      this._gameData = data;
      if (this.SessionLabel) {
        this.SessionLabel.string = `#${this._gameData.SpinData.SpinID}`;
      }
      this.__setActiveMenuBtns(false);
      this.spinAllColumns();
      this.scheduleOnce(
        () => {
          if (this._gameData.SpinData.SlotsData) {
            const ids = splitNumberFromString(this._gameData.SpinData.SlotsData);
            for (let i = 0; i <= this.Items.length; i++) {
              if (this.Items[i]) {
                let id = ids[i];
                this.Items[i].setData(id);
              }
            }
          }
        },
        this.isFastSpinMode ? 0.75 : 1.75
      );
    }
  }

  private addAccumulateItemOnAthena(itemCount: number) {
    if (this.Athena) {
      const items = this.Athena.node.children;
      SamTruyenAudioController.Instance.playSfx(SOUND_ID.GAME_TREASURE_END);
      items.forEach((e, i) => {
        e.active = i < itemCount;
      });

      if (this.LightTreasure) {
        this.Athena.node.addChild(this.LightTreasure);
        this.LightTreasure.setPosition(items[itemCount - 1].getPosition().clone());
        this.LightTreasure.setSiblingIndex(0);
        this.LightTreasure.active = true;
        this.scheduleOnce(() => {
          this.LightTreasure.active = false;
          this.LightTreasure.removeFromParent();
        }, 1);
      }
      if (itemCount == 9) {
        const balance = this._gameData.AccumulateGame.TotalPrizeValue;
        if (this.PopUp) this.PopUp.pushPopup({ key: "TreasureWin", balance: balance });
        this.scheduleOnce(() => {
          items.forEach((e) => (e.active = false));

          const state = this.Athena.getState("Athena");
          if (state) {
            this.Athena.play("Athena");
            SamTruyenAudioController.Instance.playSfx(SOUND_ID.TREASURE_COMPLETE);
          }
          this.scheduleOnce(() => {
            const state = this.Athena.getState("AthenaNormal");
            if (state) {
              this.Athena.play("AthenaNormal");
              this.accumulateItemCount = 0;
            }
            this.remainingTime = 0;

            if (this.RevenueLabel) {
              this.RevenueLabel.string = "";
            }

            if (this.CalculateLabel) {
              this.CalculateLabel.string = "";
            }
          }, 5);
        }, 1.5);
      }
    }
  }

  public onStartGame() {
    if (this.GameNode) {
      this.GameNode.active = true;
    }
    if (this.Loading) {
      this.Loading.active = false;
    }
    this.checkFreeTicket();
    SamTruyenNetWorkSignalR.getInstance().send("PlayNow", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId }], () => {});
  }

  setDataService() {
    const betValue = Number.parseInt(this._gameData.BetValue);

    if (this.BetLabel) {
      this.BetLabel.string = betValue.toLocaleString("vi");
    }

    if (this.TotalWinLabel) {
      this.TotalWinLabel.string = this._gameData.SpinData.PayLinePrizeValue.toLocaleString("vi");
    }

    if (this.LineLabel) {
      this.currentLine = this._gameData.TotalLine == 0 ? 25 : this._gameData.TotalLine;
      this.LineLabel.string = `${this.currentLine}`;
    }

    if (this.TotalBetLabel) {
      let totalBetValue = this.currentLine * betValue;
      if (totalBetValue) this.TotalBetLabel.string = totalBetValue.toLocaleString("vi");
    }
  }

  setAutoValue(value: number) {
    SamTruyenData.CurrentAutoValue = value;
    this.currentAutoCount = value;
  }

  increaseLine() {
    if (this.isSpinning || this.isPlayingMiniGame) return;
    if (this.isTryGameMode) {
      if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me35"));
      return;
    }
    if (this.Line) this.Line.active = true;
    if (this.currentLine >= this.maxLine) {
      this.currentLine = this.minLine;
    } else {
      this.currentLine++;
      this.currentLine = Math.min(this.currentLine, this.maxLine);
    }
    if (this.TotalBetLabel) {
      let totalBetValue = this.currentLine * Number.parseInt(this._gameData.BetValue);
      this.TotalBetLabel.string = totalBetValue.toLocaleString("vi");
    }
    if (this.Line) {
      const lines = this.Line.children;
      const lineLayer = this.LineLayerTop.children;
      lines.forEach((e, i) => {
        e.active = i <= this.currentLine;
        if (lineLayer[i]) lineLayer[i].active = i <= this.currentLine;
      });
    }
    SamTruyenAudioController.Instance.playSfx(SOUND_ID.BUTTON_LINE);
    this.updateLineDisplay();
  }

  decreaseLine() {
    if (this.isSpinning || this.isPlayingMiniGame) return;
    if (this.isTryGameMode) {
      if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me35"));
      return;
    }

    if (this.Line) this.Line.active = true;
    if (this.currentLine <= this.minLine) {
      this.currentLine = this.maxLine;
    } else {
      this.currentLine--;
      this.currentLine = Math.max(this.currentLine, this.minLine);
    }
    if (this.TotalBetLabel) {
      let totalBetValue = this.currentLine * Number.parseInt(this._gameData.BetValue);
      this.TotalBetLabel.string = totalBetValue.toLocaleString("vi");
    }
    if (this.Line) {
      const lines = this.Line.children;
      lines.forEach((e, i) => {
        e.active = i <= this.currentLine;
      });
    }
    SamTruyenAudioController.Instance.playSfx(SOUND_ID.BUTTON_LINE);
    this.updateLineDisplay();
  }

  private updateLineDisplay() {
    if (this.LineLabel) {
      this.LineLabel.string = `${this.currentLine}`;
    }
  }

  private setInfoPlayer() {
    if (this._gameData) {
      const data = this._gameData.Account;
      if (data) {
        if (this.AvatarNode) {
          this.AvatarNode.getComponent(Sprite).spriteFrame = App.instance.sprFrameAvatars[data.Avatar];
        }

        if (this.NameLabel) {
          this.NameLabel.string = data.OriginNickname;
        }

        if (this.GoldLabel) {
          const balance = this._gameData.CurrencyID ? data.GoldBalance : data.CoinBalance;
          if (balance) {
            this.GoldLabel.string = balance.toLocaleString("vi");
          } else {
            const goldBalance = Configs.Login.GoldBalance;
            this._gameData.Account.GoldBalance = goldBalance;
            this.GoldLabel.string = goldBalance.toLocaleString("vi");
          }
        }
      }
    }
  }

  private goBack() {
    SamTruyenNetWorkSignalR.getInstance().dontReceive();
    Utils.setStorageValue("last_open_game_id", "");
    App.instance.slotGame[Configs.InGameIds.SamTruyen] = null;
    App.instance.gotoLobby();
  }

  private spinAllColumns() {
    if (this.Line) {
      this.Line.active = false;
    }
    const columns = this.Content.children;
    if (columns && columns.length) {
      for (let i = 0; i < columns.length; i++) {
        columns[i].setPosition(new Vec3(columns[i].position.x, -853, columns[i].position.z));
        this.scheduleOnce(() => {
          this.spinColumn(columns[i], i === 4);
        }, i * this.delayBetween);
      }
    }
  }

  spinColumn(column: Node, isShowLine: boolean) {
    if (!column) return;
    const startPos = column.position.clone();
    const startY = column.position.y;
    const endY = -5035;

    tween(column)
      .to(this.spinDuration, { position: new Vec3(column.position.x, endY, column.position.z) }, { easing: "linear" })
      .call(() => {
        column.setPosition(startPos.add(new Vec3(0, 578, 0)));
        tween(column)
          .to(0.5, { position: new Vec3(column.position.x, startY, column.position.z) }, { easing: "linear" })
          .call(() => {
            SamTruyenAudioController.Instance.playSfx(SOUND_ID.SPIN_END);
            if (isShowLine) {
              this.onFinsishedSpin(this._gameData.SpinData.PrizesData, this._gameData.SpinData.PositionData);
            }
          })
          .start();
      })
      .start();
  }

  private onFinsishedSpin(lines: string, posData: string) {
    if (!this.isTryGameMode) {
      this.setInfoPlayer();
    } else {
      this.trialGoldBalace += this._gameData.SpinData.PayLinePrizeValue;
      if (this.GoldLabel) this.GoldLabel.string = this.trialGoldBalace.toLocaleString("vi");
    }
    this.setDataService();
    this.setAccumulateData();
    this.__setActiveMenuBtns(true);

    this.showLine(lines);
    this.showEffect(posData);

    SamTruyenAudioController.Instance.playSfx(SOUND_ID.RESULT);

    if (this.IsSpinningNode) this.IsSpinningNode.active = false;
    if (this.SpinBtn) this.SpinBtn.interactable = true;

    this.scheduleOnce(() => {
      if (this._gameData.SpinData.PayLinePrizeValue) {
        if (this._gameData.SpinData.PayLinePrizeValue / Number.parseInt(this._gameData.BetValue) < 100) {
          this.currentValue = 0;
          this.targetValue = this._gameData.SpinData.PayLinePrizeValue;
          this.isShowValue = true;

          if (this._gameData.SpinData.PayLinePrizeValue > this._gameData.SpinData.TotalBetValue) {
            if (this.MoneyParticle) {
              this.MoneyParticle.active = true;
              this.scheduleOnce(() => {
                this.MoneyParticle.active = false;
              }, 3);
            }
          }
        }

        if (this.X2Button) {
          if (this._gameData.SpinData.IsJackpot) {
            this.X2Button.interactable = false;
          } else if (this.isFreeSpin) {
            this.X2Button.interactable = false;
          } else if (this._gameData.SpinData.GameStatus === 3) {
            this.X2Button.interactable = false;
          } else {
            this.X2Button.interactable = true;
          }
        }
      }
    }, 2);
  }

  private showLine(lineNum: string) {
    if (this.Line) {
      this.Line.active = true;
      const lineData = lineNum.split(";");
      const lineIDs = [];
      if (lineData && lineData.length) {
        lineData.forEach((e) => {
          let id = e.split(",")[0];
          lineIDs.push(Number.parseInt(id));
        });
      }

      const lines = this.Line.children;
      const lineLayer = this.LineLayerTop.children;
      let time = 0;
      if (lines && lines.length) {
        lines.forEach((e, i) => {
          lineIDs.forEach((index) => {
            let id = index === i + 1;
            if (id) {
              this.scheduleOnce(() => {
                e.active = true;
                if (lineLayer[i]) lineLayer[i].active = true;
              }, time);
              time += 0.5;
            }
          });
        });
      }

      if (this._gameData.SpinData.AccumulateItems > this.accumulateItemCount) {
        this.accumulateItemCount = this._gameData.SpinData.AccumulateItems;

        if (this.PopUp) {
          this.PopUp.pushPopup({ key: "Treasure", balance: this._gameData.SpinData.AccumulateItems });
        }
        this.scheduleOnce(() => {
          this.addAccumulateItemOnAthena(this._gameData.SpinData.AccumulateItems);
        }, 4);
        time += this.accumulateItemCount == 9 ? 6 : 3;
      }

      if (this._gameData.SpinData.PayLinePrizeValue >= this._gameData.SpinData.Jackpot && this._gameData.SpinData.IsJackpot) {
        if (this.Hades) {
          const state = this.Hades.getState("FreeSpin");
          if (state) {
            this.Hades.play("FreeSpin");
            this.Hades.once(
              Animation.EventType.FINISHED,
              () => {
                this.Hades.play("Hades");
              },
              this
            );
          }
        }
        if (this.PopUp) {
          this.PopUp.pushPopup({ key: "Jackpot", balance: this._gameData.SpinData.PayLinePrizeValue });
        }
        if (this.MoneyParticle) {
          this.MoneyParticle.active = true;
          this.scheduleOnce(() => {
            this.MoneyParticle.active = false;
          }, 3);
        }
        if (this.MoneyRain) {
          this.MoneyRain.spawnCoins(3);
        }

        if (this.isAutoSpin) {
          this.stopAutoSpin();
        }
        time += 5;
      }

      if (this._gameData.SpinData.PayLinePrizeValue / Number.parseInt(this._gameData.BetValue) >= 100 && !this._gameData.SpinData.IsJackpot) {
        if (this.PopUp) {
          this.PopUp.pushPopup({ key: "BigWin", balance: this._gameData.SpinData.PayLinePrizeValue });
          if (this.MoneyParticle) {
            this.MoneyParticle.active = true;
            this.scheduleOnce(() => {
              this.MoneyParticle.active = false;
            }, 3);
          }
          if (this.MoneyRain) {
            this.MoneyRain.spawnCoins(2);
          }
          if (this.Hades) {
            const state = this.Hades.getState("FreeSpin");
            if (state) {
              this.Hades.play("FreeSpin");
              this.Hades.once(
                Animation.EventType.FINISHED,
                () => {
                  this.Hades.play("Hades");
                },
                this
              );
            }
          }
        }
        time += 5;
      }

      if (this._gameData.SpinData.IsFreeSpin) {
        if (!this.isFreeSpin) {
          this.isFreeSpin = true;
          this.getFreeSpinPay = 0;
          if (this.PopUp) {
            this.scheduleOnce(() => {
              this.PopUp.pushPopup({ key: "FreeSpin", balance: this._gameData.SlotInfo.FreeSpins });
              if (this.FreeSpinLabel) {
                this.FreeSpinLabel.string = `${App.instance.getTextLang("sl20")} ${this._gameData.SlotInfo.FreeSpins}`;
              }
            }, 1.5);

            time += 3;
          }
        } else {
          this.getFreeSpinPay += this._gameData.SpinData.PayLinePrizeValue;
        }
      }

      if (this._gameData.SpinData.GameStatus === 3) {
        this.scheduleOnce(() => {
          if (this.PopUp) this.PopUp.pushPopup({ key: "Bonus", balance: 0 });
        }, 1);

        time += 1.5;
      }

      this.scheduleOnce(() => {
        this.isSpinning = false;
        this.Line.active = false;
        lineLayer.forEach((e) => (e.active = false));
        if (this._gameData.SpinData.IsLastFreeSpin) {
          this.isFreeSpin = false;
          if (this.PopUp) {
            this.PopUp.openWinPopUp(this._gameData.SlotInfo.TotalFreeSpinPrizeValue, App.instance.getTextLang("sl89"));
            this.scheduleOnce(() => {
              this.PopUp.WinNode.active = false;
            }, 3);
            if (this.FreeSpinLabel) {
              this.FreeSpinLabel.string = "";
            }
          }
        }

        if (this.isFreeSpin && !this._gameData.SpinData.IsLastFreeSpin) {
          SamTruyenAudioController.Instance.playSfx(SOUND_ID.FREE_SPIN);
          this.spin();
          if (this.FreeSpinLabel) {
            this.FreeSpinLabel.string = `${App.instance.getTextLang("sl20")} ${this._gameData.SlotInfo.FreeSpins - 1}`;
          }
        }

        this.checkAutoSpin();
        if (!this.isAutoSpin) {
          if (this.SpinBtn) this.SpinBtn.interactable = true;
        }
        this.switchToMiniGame(this._gameData.SpinData.GameStatus);
      }, 1.75 + time);
    }
  }

  private checkAutoSpin() {
    if (this.isAutoSpin) {
      if (this.currentAutoCount > 0) {
        if (this.freeTicket > 0) {
          this.spin();
        } else {
          this.currentAutoCount--;
          if (this.AutoSpinInfo) {
            this.AutoSpinInfo.active = true;
            const label = this.AutoSpinInfo.getChildByName("Label").getComponent(Label);
            if (label) {
              label.string = `${this.currentAutoCount}`;
            }
          }
          this.spin();
        }
      } else {
        this.stopAutoSpin();
        if (this.AutoSpinInfo) {
          this.AutoSpinInfo.active = false;
        }
      }

      if (this._gameData.SpinData.PayLinePrizeValue > 0 && this.autoSpinWinCount) {
        this.autoSpinWinCount--;
        if (this.autoSpinWinCount <= 0) {
          this.stopAutoSpin();
          if (this.AutoSpinInfo) {
            this.AutoSpinInfo.active = false;
          }
        }
      }

      if (this._gameData.Account.GoldBalance < this.autoSpinLoseToAmount && this.autoSpinLoseToAmount) {
        this.stopAutoSpin();
        if (this.AutoSpinInfo) {
          this.AutoSpinInfo.active = false;
        }
      }

      if (this._gameData.Account.GoldBalance > this.autoSpinWinToAmount && this.autoSpinWinToAmount) {
        this.stopAutoSpin();
        if (this.AutoSpinInfo) {
          this.AutoSpinInfo.active = false;
        }
      }
    }
  }

  showEffect(posData: string) {
    const itemsHighlight = splitNumberFromString(posData);
    if (itemsHighlight && itemsHighlight.length) {
      if (this.Items && this.Items.length) {
        this.Items.forEach((e, i) => {
          let index = i + 1;
          if (index) {
            if (itemsHighlight.indexOf(index) >= 0) {
              e.activeEffect();
            }
          }
        });
      }
    }
  }

  private setFastMode() {
    this.isFastSpinMode = !this.isFastSpinMode;
    this.delayBetween = this.isFastSpinMode ? 0.125 : 0.25;
    this.spinDuration = this.isFastSpinMode ? 1 : 2;
  }

  private __setActiveMenuBtns(enable: boolean) {
    if (this.LayoutBtns) {
      const btnsNode = this.LayoutBtns.children;
      btnsNode.forEach((e) => {
        let comp = e.getComponent(Button);
        if (comp) {
          comp.interactable = enable;
        }
      });

      this.UIBtns.forEach((e) => {
        e.interactable = enable;
      });
    }
  }

  private onClickPlus() {
    this.isClickingPlus = !this.isClickingPlus;
    if (this.LayoutBtns) {
      this.LayoutBtns.active = this.isClickingPlus;
    }
    if (this.BtnPlus) {
      this.BtnPlus.getChildByName("Add").active! = !this.isClickingPlus;
      this.BtnPlus.getChildByName("Close").active! = this.isClickingPlus;
    }
  }

  private spin() {
    if (this.isSpinning) return;
    if (this.isPlayingMiniGame && !this.isAutoSpin) return;

    if (this.WinPrizeLabel) {
      this.isShowValue = false;
      this.WinPrizeLabel.string = "";
    }
    if (this.IsSpinningNode) this.IsSpinningNode.active = true;
    SamTruyenAudioController.Instance.playSfx(SOUND_ID.SPIN_START);
    if (!this.isTryGameMode) {
      if (this.freeTicket > 0) {
        this._spinWithTicket();
      } else {
        const checkGold = Number.parseInt(this._gameData.BetValue) * 12 > this._gameData.Account.GoldBalance;
        if (checkGold && !this.isFreeSpin) {
          if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me-20016"));
          return;
        }
        this.isSpinning = true;
        SamTruyenNetWorkSignalR.getInstance().send("Spin", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId, TotalLine: this.currentLine }], () => {});
      }
    } else {
      this.isSpinning = true;
      this.trialGoldBalace -= 2500;
      if (this.GoldLabel) this.GoldLabel.string = this.trialGoldBalace.toLocaleString("vi");
      SamTruyenAudioController.Instance.playSfx(SOUND_ID.SPIN_REEL);
      const index = Math.floor(Math.random() * spinData.length);
      this._gameData.SpinData = spinData[index];
      this.__onHandleResultSpin(this._gameData);
      if (this.X2Button) {
        this.X2Button.interactable = false;
      }
    }

    if (this.Items && this.Items.length) {
      this.Items.forEach((e) => {
        e.reset();
      });
    }

    if (this.Line) {
      const lines = this.Line.children;
      const linesTop = this.LineLayerTop.children;
      lines.forEach((e, i) => {
        e.active = false;
        if (linesTop[i]) linesTop[i].active = false;
      });
    }
  }

  update(deltaTime: number) {
    if (this.currentJackPot !== this.jackpotChange) {
      const distance = this.jackpotChange - this.currentJackPot;
      const direction = Math.sign(distance); // +1 hoặc -1
      const step = this.jackpotSpeed * deltaTime * direction;

      if (Math.abs(distance) <= Math.abs(step)) {
        this.currentJackPot = this.jackpotChange;
      } else {
        this.currentJackPot += step;
      }

      this.updateJackpotDisplay();
    }

    if (this.isShowValue) {
      this.currentValue += deltaTime * this.targetValue;
      if (this.WinPrizeLabel) {
        this.WinPrizeLabel.string = `${Math.floor(this.currentValue)}`;
      }
      if (this.currentValue >= this.targetValue) {
        this.isShowValue = false;
        if (this.WinPrizeLabel) {
          this.WinPrizeLabel.string = `${this.targetValue}`;
        }
        this.scheduleOnce(() => {
          if (this.WinPrizeLabel) {
            this.WinPrizeLabel.string = "";
          }
        }, 3);
      }
    }

    if (this.remainingTime > 0) {
      this.remainingTime -= deltaTime;

      const displaySeconds = Math.floor(this.remainingTime);

      if (this.TimeSpanLabel) {
        this.TimeSpanLabel.string = formatTimeSpanToCountdown(displaySeconds);
      }
    } else {
      if (this.TimeSpanLabel) {
        this.TimeSpanLabel.string = "";
      }
    }
  }

  private startBonusGame() {
    SamTruyenNetWorkSignalR.getInstance().send("PlayBonusGameAll", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId, moneyType: this._gameData.moneyType }], () => {});
  }

  private playBonusGame(itemID: number) {
    SamTruyenNetWorkSignalR.getInstance().send("PlayBonusGame", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId, ItemID: itemID }], () => {});
  }

  private startX2Game() {
    if (this.isLocking) return;
    this.isLocking = true;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1.5);
    SamTruyenNetWorkSignalR.getInstance().send("PlayX2Game", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId }], () => {});
  }

  private stopX2Game() {
    if (this.isLocking) return;
    this.isLocking = true;
    this.isPlayingMiniGame = false;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1.5);
    if (this.PopUp) {
      if (this.x2GameWin) {
        if (this.TotalWinLabel) this.TotalWinLabel.string = this.x2GameWin.toLocaleString("vi");
        this.PopUp.openWinPopUp(this.x2GameWin, "Tipzo");
        this.scheduleOnce(() => {
          this.PopUp.closeCurrentPopUp();
        }, 5);
      }
    }
    if (this.X2Node) this.X2Node.active = false;
    if (this.SpinBtn) this.SpinBtn.interactable = true;
    SamTruyenNetWorkSignalR.getInstance().send("StopX2Game", [{ CurrencyID: Configs.Login.CurrencyID, RoomID: this.roomId }], () => {});
  }
  setAutoSpin(winCount: number, winToAmount: number, loseToAmount: number) {
    if (this.isTryGameMode) {
      if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me35"));
      return;
    }
    if (this.currentAutoCount) {
      this.isAutoSpin = true;
      this.spin();
      if (this.BtnStopAuto) {
        this.BtnStopAuto.active = true;
      }

      if (this.AutoSpinInfo) {
        this.AutoSpinInfo.active = true;
        const label = this.AutoSpinInfo.getChildByName("Label").getComponent(Label);
        if (label) {
          label.string = `${this.currentAutoCount}`;
        }
      }

      this.autoSpinWinCount = winCount;
      this.autoSpinWinToAmount = winToAmount;
      this.autoSpinLoseToAmount = loseToAmount;
    }
  }

  private stopAutoSpin() {
    this.isAutoSpin = false;
    this.currentAutoCount = 0;
    if (this.BtnStopAuto) {
      this.BtnStopAuto.active = false;
      this.isPlayingMiniGame = false;
    }
    if (this.AutoSpinInfo) {
      this.AutoSpinInfo.active = false;
    }
    if (this.SpinBtn) this.SpinBtn.interactable = false;
    if (this.IsSpinningNode) this.IsSpinningNode.active = false;
  }

  private __openScatterGame(data: BonusGame) {
    this.__setActiveMenuBtns(false);
    SamTruyenAudioController.Instance.playSfx(SOUND_ID.BONUS);

    this.isPlayingMiniGame = true;
    this.scheduleOnce(() => {
      if (this.ScatterNode) {
        this.ScatterNode.active = true;
        let comp = this.ScatterNode.getComponent(SamTruyenScatterContrller);
        if (comp) {
          comp.setData(data, () => {
            this.isPlayingMiniGame = false;
            if (this.PopUp) {
              this.PopUp.openWinPopUp(comp.Score, App.instance.getTextLang("sl88"));
              if (this.TotalWinLabel) {
                this.TotalWinLabel.string = comp.Score.toLocaleString("vi");
              }
              if (this.GoldLabel) this.GoldLabel.string = (this._gameData.Account.GoldBalance + comp.Score).toLocaleString("vi");
              this.scheduleOnce(this.closeMinigame, 3);
              if (this.X2Button) {
                this.X2Button.interactable = true;
              }
            }
          });
        }
      }
    }, 1.5);
  }
  private closeMinigame() {
    this.PopUp.closeCurrentPopUp();
    this.__setActiveMenuBtns(true);
    if (this.SpinBtn) this.SpinBtn.interactable = true;
    if (this.isAutoSpin) {
      this.isSpinning = false;
      this.spin();
    }
  }

  private __openX2Game(data: X2Game) {
    if (this.X2Node) {
      SamTruyenAudioController.Instance.playSfx(SOUND_ID.BUTTON_DOUBLE);
      this.X2Node.active = true;
      this.isShowValue = false;
      this.isPlayingMiniGame = true;
      this.x2GameWin = 0;
      if (this.SpinBtn) this.SpinBtn.interactable = false;
      if (this.WinPrizeLabel) this.WinPrizeLabel.string = "";
      let comp = this.X2Node.getComponent(SamTruyenX2Controller);
      if (comp) {
        this.__setActiveMenuBtns(false);
        comp.setData(
          data,
          (val: number) => {
            if (this.GoldLabel) this.GoldLabel.string = (this._gameData.Account.GoldBalance + val).toLocaleString("vi");
            this.__setActiveMenuBtns(true);
            this.x2GameWin = val;
          },
          () => {
            if (this.TotalWinLabel) this.TotalWinLabel.string = "0";
            this.stopX2Game();
          }
        );
      }
    }
  }

  private updateJackpotDisplay() {
    if (this.JackpotLabel) {
      this.JackpotLabel.string = Math.floor(this.currentJackPot).toLocaleString("vi");
    }
  }

  private switchToMiniGame(gameStatus: number) {
    switch (gameStatus) {
      case 1:
        if (this.ScatterNode && !this.isPlayingMiniGame) this.ScatterNode.active = false;
        break;
      case 2:
        if (this.ScatterNode) this.ScatterNode.active = false;
        this.startX2Game();
        break;
      case 3:
        if (this.X2Node) this.X2Node.active = false;
        this.playBonusGame(1);
        break;
      default:
        if (this.ScatterNode) this.ScatterNode.active = false;
        if (this.X2Node) this.X2Node.active = false;
        break;
    }
  }

  onTogglePlayMode() {
    this.isTryGameMode = !this.isTryGameMode;
    if (this.PlayingGameModeToggle) {
      this.PlayingGameModeToggle.isChecked = this.isTryGameMode;
    }
    if (this.isTryGameMode) {
      this.__setTryGameMode();
    } else {
      this.__setRealPlayGameMode();
    }
  }

  private __setTryGameMode() {
    if (this.GoldLabel) {
      const balance = 50000000;
      this.trialGoldBalace = balance;
      this.GoldLabel.string = balance.toLocaleString("vi");
      this.currentLine = this.maxLine;
      this.jackpotChange = 500000;
      this.updateLineDisplay();
      const betValue = 100;
      if (this.BetLabel) {
        this.BetLabel.string = `${betValue}`;
      }

      if (this.TotalWinLabel) {
        this.TotalWinLabel.string = "0";
      }

      if (this.TotalBetLabel) {
        let totalBetValue = this.currentLine * betValue;
        this.TotalBetLabel.string = totalBetValue.toLocaleString("vi");
      }

      if (this.FreeTicketLabel) {
        this.FreeTicketLabel.string = "";
      }
    }
  }

  private __setRealPlayGameMode() {
    this.onStartGame();
  }

  private async openHistory() {
    if (this.isLocking) return;
    this.isLocking = true;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1.5);
    const historyData = await this.fetchHistory();
    console.log("History: ", historyData);
    if (historyData) {
      if (this.PopUp) {
        this.PopUp.openHistoryNode(historyData);
      }
    }
  }

  private async openTopAccount() {
    if (this.isLocking) return;
    this.isLocking = true;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1.5);
    const topData = await this.fetchTopAccount();
    console.log("Top Account: ", topData);
    if (topData) {
      if (this.PopUp) {
        this.PopUp.openRankNode(topData);
      }
    }
  }

  private fetchHistory(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      Http.get(
        Configs.App.DOMAIN_CONFIG["OLP_GetTransactionLog"],
        {
          CurrencyID: Configs.Login.CurrencyID,
          type: 1,
          Page: 1,
          PageSize: 10,
        },
        (status, response) => {
          if (status === 200) {
            resolve(response["d"]);
          } else {
            reject(new Error("Error fetching Jackpot History"));
          }
        }
      );
    });
  }

  private async cheatSlotData() {
    const slotData = "11,1,3,3,3,3,5,4,5,6,6,4,7,4,2";
    let data = await this.__cheats(slotData);
  }

  private async uncheatSlotData() {
    const slotData = "";
    let data = await this.__cheats(slotData);
  }

  private __cheats(data: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      Http.get(
        "https://slot-alpha.bavenoth.com/oracle/api/Game/ToolSetData",
        {
          slotData: data.toString(),
          userName: "ginko",
        },
        (status, response) => {
          if (status === 200) {
            resolve(response["d"]);
          } else {
            reject(new Error("Error fetching Jackpot History"));
          }
        }
      );
    });
  }

  private fetchTopAccount(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      Http.get(
        Configs.App.DOMAIN_CONFIG["OLP_GetTransactionLog"],
        {
          CurrencyID: Configs.Login.CurrencyID,
          Page: 1,
          type: 2,
          PageSize: 10,
        },
        (status, response) => {
          if (status === 200) {
            resolve(response["d"]);
          } else {
            reject(new Error("Error fetching Top Account"));
          }
        }
      );
    });
  }

  private actHidden() {
    if (this.currentAutoCount > 0) {
      App.instance.confirmDialog.showMsg(App.instance.getTextLang("sl74"), () => {
        SamTruyenAudioController.Instance.pauseAll();
        let root = App.instance.slotGame[Configs.InGameIds.SamTruyen];
        if (root) {
          let uio = root.getComponent(UIOpacity);
          if (uio) {
            uio.opacity = 0;
          }

          let blocksInput = root.getComponent(BlockInputEvents);
          if (blocksInput) {
            blocksInput.enabled = false;
          }
          App.instance.slotGameNode.addChild(root);
          this.scheduleOnce(() => {
            App.instance.ShowAlertDialog(App.instance.getTextLang("me28"));
          }, 1);
        }
      });
    } else {
      App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
    }
  }

  private openAutoSpin() {
    if (this.isSpinning || this.isPlayingMiniGame) return;
    if (this.X2Node.active) return;
    if (this.isTryGameMode) {
      if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me35"));
      return;
    }
    if (this.PopUp) this.PopUp.openAutoSpinNode();
  }

  private setAccumulateData() {
    if (this._gameData) {
      if (this._gameData.AccumulateGame && this._gameData.AccumulateGame.AccumulateRate && this._gameData.AccumulateGame.Revenue) {
        this.remainingTime = this._gameData.AccumulateGame.TimeRemain;

        if (this.RevenueLabel) {
          this.RevenueLabel.string = `${this._gameData.AccumulateGame.Revenue}`;
        }

        if (this.CalculateLabel) {
          const value = (this._gameData.AccumulateGame.Revenue / this._gameData.AccumulateGame.AccumulateRate).toFixed(2);
          this.CalculateLabel.string = `(${Number.parseFloat(value)} * ${this._gameData.AccumulateGame.AccumulateRate})`;
        }
      } else {
        this.remainingTime = this._gameData.AccumulateGame.TimeRemain;

        if (this.RevenueLabel) {
          this.RevenueLabel.string = "";
        }

        if (this.CalculateLabel) {
          this.CalculateLabel.string = "";
        }
      }
    }
  }

  private changeRoomClick() {
    if (this.X2Node.active) return;
    if (this.isSpinning || this.isPlayingMiniGame) return;
    if (this.isLocking) return;
    this.isLocking = true;
    this.scheduleOnce(() => {
      this.isLocking = false;
    }, 1.5);
    if (this.roomId > 0 && this.roomId < 4) {
      this.roomId++;
      this.roomId = this.roomId > 3 ? 1 : this.roomId;
      this.onStartGame();
    }
  }

  private checkFreeTicket() {
    Http.get(Configs.App.DOMAIN_CONFIG["GetAccountTicket"], { CurrencyID: Configs.Login.CurrencyID, GameID: Configs.InGameIds.SamTruyen }, (status, res) => {
      if (status === 200) {
        const data = res.d.filter((item) => item.roomID === this.roomId);
        const count = data.reduce((sum, item) => sum + item.balance, 0);
        this.freeTicket = count;
      }
      if (this.freeTicket > 0) {
        if (this.FreeTicketLabel) this.FreeTicketLabel.string = `${App.instance.getTextLang("sl61")} ${this.freeTicket}`;
      } else {
        if (this.FreeTicketLabel) this.FreeTicketLabel.string = "";
      }
    });
  }

  private _spinWithTicket() {
    SamTruyenNetWorkSignalR.getInstance().send(
      "SpinForTicket",
      [
        {
          CurrencyID: Configs.Login.CurrencyID,
          RoomID: this.roomId,
          TotalLine: this.currentLine,
        },
      ],
      (data) => {
        if (data.c < 0) {
          if (this.PopUp) this.PopUp.openMessageNode(App.instance.getTextLang("me" + data.c));
        } else {
          this.freeTicket--;
          if (this.freeTicket > 0) {
            if (this.FreeTicketLabel) this.FreeTicketLabel.string = `${App.instance.getTextLang("sl61")} ${this.freeTicket}`;
          } else {
            if (this.FreeTicketLabel) this.FreeTicketLabel.string = "";
          }
        }
      }
    );
  }
}
