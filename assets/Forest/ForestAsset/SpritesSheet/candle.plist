<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>candle_00001.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,0},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00002.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,120},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00003.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,0},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00004.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,240},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00005.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,120},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00006.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,0},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00007.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,360},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00008.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,240},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00009.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,120},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00010.png</key>
      <dict>
        <key>frame</key>
        <string>{{300,0},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00011.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,480},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00012.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,360},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00013.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,240},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00014.png</key>
      <dict>
        <key>frame</key>
        <string>{{300,120},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00015.png</key>
      <dict>
        <key>frame</key>
        <string>{{400,0},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00016.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,600},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00017.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,480},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00018.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,360},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00019.png</key>
      <dict>
        <key>frame</key>
        <string>{{300,240},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00020.png</key>
      <dict>
        <key>frame</key>
        <string>{{400,120},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00021.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,720},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00022.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,600},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00023.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,480},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00024.png</key>
      <dict>
        <key>frame</key>
        <string>{{300,360},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00025.png</key>
      <dict>
        <key>frame</key>
        <string>{{400,240},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00026.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,840},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00027.png</key>
      <dict>
        <key>frame</key>
        <string>{{100,720},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00028.png</key>
      <dict>
        <key>frame</key>
        <string>{{200,600},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
      <key>candle_00029.png</key>
      <dict>
        <key>frame</key>
        <string>{{300,480},{100,120}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{100,120}}</string>
        <key>sourceSize</key>
        <string>{100,120}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>pixelFormat</key>
      <string>RGBA8888</string>
      <key>premultiplyAlpha</key>
      <false/>
      <key>realTextureFileName</key>
      <string>candle.png</string>
      <key>size</key>
      <string>{500,960}</string>
      <key>textureFileName</key>
      <string>candle</string>
    </dict>
  </dict>
</plist>
