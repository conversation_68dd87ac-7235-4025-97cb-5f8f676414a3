import { _decorator, Component, Node, Animation, UIOpacity, tween } from "cc";

const { ccclass, property } = _decorator;

@ccclass
export default class DQWildColumn extends Component {

    private wildCols: Node[] = [];

    protected onLoad() {
        for (let i = 0; i < this.node.children.length; i++) {
            this.wildCols.push(this.node.children[i]);
            this.wildCols[i].active = false;
        }
    }

    public async showWildColumnAnimation(wildColumns: number[]): Promise<void> {
        await Promise.all(wildColumns.map(async (col) => {
            this.wildCols[col].active = true;
            await this.playWildOn(col);
        }));
    }

    private playWildOn(col: number): Promise<void> {
        this.wildCols[col].getComponent(Animation).play('wild_column');
        return new Promise<void>((resolve) => {
            this.wildCols[col].getComponent(Animation).once(Animation.EventType.FINISHED, () => {
                let o = this.wildCols[col].getComponent(UIOpacity);
                tween(o)
                    .to(0.5, { opacity: 0 }, {
                        onComplete: () => {
                            this.wildCols[col].active = false;
                            o.opacity = 255;
                            resolve();
                        }
                    })
                    .start();
            });
        });
    }
}
