import { _decorator, Component, Node } from "cc";

const { ccclass, property } = _decorator;

@ccclass
export default class DQPayLines extends Component {
    protected onLoad() {
        this.node.children.forEach(child => child.active = false);
    }

    public async showPayLinesAnimation(prizeLines: string[]): Promise<void> {
        let activePayLines: Node[] = [];
        for (let p of prizeLines) {
            activePayLines.push(this.node.getChildByName(`Line${p.split(",")[0]}`));
        }
        activePayLines.forEach(line => {
            if (line) {
                line.active = true;
            }
        });
    }

    public showLinesSelected(lineCount: number) {
        this.resetAllPayLines();
        for (let i = 1; i <= lineCount; i++) {
            this.node.getChildByName(`Line${i}`).active = true;
        }
    }

    public resetAllPayLines() {
        for (let line of this.node.children) {
            line.active = false;
        }
    }
}
