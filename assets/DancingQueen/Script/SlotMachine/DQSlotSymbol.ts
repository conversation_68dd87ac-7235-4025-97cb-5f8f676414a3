import { _decorator } from "cc";
import { BaseSlotSymbol, SymbolState } from "db://assets/Lobby/scripts/common/slot/BaseSlotSymbol";

const {ccclass, property} = _decorator;

@ccclass("DQSlotSymbol")
export class DQSlotSymbol extends BaseSlotSymbol {
    protected onLoad(): void {
        this.setStateHandler({
            [SymbolState.INIT]: async () => {
                this.symbol.node.active = true;
                this.defaultAnimation.node.active = false;
                return Promise.resolve();
            },
            [SymbolState.HIDE]: async () => {
                return Promise.resolve();
            },
            [SymbolState.HIGHLIGHT]: async () => {
                // this.symbol.node.color = cc.Color.BLACK.fromHEX('#ffffff');
                return new Promise((resolve) => {
                    this.symbol.node.active = false;
                    this.defaultAnimation.node.active = true;
                    const animationName = "Item" + this.getId();
                    this.defaultAnimation.play(animationName);
                    this.scheduleOnce(() => {
                        resolve();
                    }, 1.5);
                });
            }
        })
    }
}
