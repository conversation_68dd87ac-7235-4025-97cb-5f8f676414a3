import { _decorator, Component, error, instantiate, Label, Node, ScrollView } from "cc";
import Configs from "../../Lobby/scripts/common/Config";
import Http from "../../Lobby/scripts/common/Http";
import App from "../../Lobby/scripts/common/App";
import { Utils } from "../../Lobby/scripts/common/Utils";
const { ccclass, property, menu } = _decorator;

@ccclass
@menu("DancingQueen/DQPopupLSH")
export default class DQPopupLSH extends Component {
    @property(Node)
    itemTemplate: Node = null;

    @property(Node)
    itemContainer: Node = null;

    private currentPage: number = 1;
    private readonly pageSize: number = 20;
    private isLoading: boolean = false;
    private hasMoreData: boolean = true;

    show(){
        this.node.active = true;
        this.itemContainer.removeAllChildren();
        this.resetPagination();
        this.loadData();

        this.getComponentInChildren(ScrollView).node.on('scroll-to-bottom', this.onScrollToBottom, this);
    }

    dismiss(){
        this.node.active = false;
        this.getComponentInChildren(ScrollView).node.off('scroll-to-bottom', this.onScrollToBottom, this);
    }

    private resetPagination() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.isLoading = false;
    }

    private onScrollToBottom() {
        if (!this.isLoading && this.hasMoreData) {
            this.loadData().then();
        }
    }

    private async loadData() {
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            const result = await this.fetchHistory(this.currentPage);
            this.createItems(result);
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            console.error("Error fetching Jackpot History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private fetchHistory(page: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Dancing_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 2,
                "Page": page,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    resolve(response["d"]);
                } else {
                    reject(new Error("Error fetching Jackpot History"));
                }
            })
        })
    }

    private createItems(result: any[]) {
        for (let i = 0; i < result.length; i++) {
            let itemRow = instantiate(this.itemTemplate);
            itemRow.active = true;
            this.itemContainer.addChild(itemRow);
            itemRow.children[0].getComponent(Label).string = Utils.formatDatetime(result[i].createTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(Label).string = Utils.formatNumberMin(result[i].betValue).toString();
            itemRow.children[2].getComponent(Label).string = `${result[i].nickname}`;
            itemRow.children[3].getComponent(Label).string = result[i].jackPotNum;
            itemRow.children[4].getComponent(Label).string = Utils.formatNumber(result[i].paylinePrizeValue);
        }
    }

}
