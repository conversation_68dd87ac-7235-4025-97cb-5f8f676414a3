import { _decorator, Component, Label, Node } from "cc";
import { GameStatus } from "../DQTypes";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("DancingQueen/BottumHudController")
export class BottumHudController extends Component {
    @property(Node) 
    spinBar: Node = null;

    @property(Node)
    normalSpinNode: Node = null;

    @property(Node)
    x2SpinNode : Node = null;

    @property(Node) 
    crazyBar: Node = null;

    @property(Node) 
    btnBack: Node = null;

    @property(Label)
    lblFreespinMultiplier: Label = null;

    public activeBackButton(){
        this.node.children.forEach(child => {
            child.active = false;
        })
        this.btnBack.active = true;
    }

    public setState(state: GameStatus){
        this.btnBack.active = false;
        switch (state) {
            case GameStatus.SPIN:
                this.spinBar.active = true;
                this.normalSpinNode.active = true;
                this.x2SpinNode.active = false;
                this.crazyBar.active = false;
                break;
            case GameStatus.BONUS:
                break;
            case GameStatus.CRAZY:
                this.spinBar.active = false;
                this.normalSpinNode.active = false;
                this.x2SpinNode.active = false;
                this.crazyBar.active = true;
                break;
            case GameStatus.X2:
                this.spinBar.active = true;
                this.normalSpinNode.active = false;
                this.x2SpinNode.active = true;
                this.crazyBar.active = false;
                break;
        }
    }

    public setFreespinMultiplier(multiplier: number){
        if (multiplier === 0) {
            this.lblFreespinMultiplier.string = '';
        } else {
            this.lblFreespinMultiplier.string = `(x${multiplier})`;
        }
    }
}
