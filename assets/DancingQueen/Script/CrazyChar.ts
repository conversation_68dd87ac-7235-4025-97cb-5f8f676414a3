import { _decorator, Component, Node, Label, tween, v3 } from "cc";

const { ccclass, property } = _decorator;

@ccclass
export default class CrazyChar extends Component {
    @property(Node)
    charActive: Node = null;

    @property(Label)
    charNum: Label = null;

    private _charCount: number = 0;
    public get charCount(): number {
        return this._charCount;
    }
    public set charCount(value: number) {
        this._charCount = value;
        this.charNum.string = value.toString();
        this.charNum.node.parent.active = value > 0;
        this.charActive.active = value > 0;
        if (value === 1) {
            this.charActive.setScale(0,0,0);
            tween(this.charActive).to(0.3, { scale: v3(1, 1, 1) }, { easing: 'backOut' }).start();
        }
    }

    onLoad() {
        this.charActive.active = false;
        this.charNum.string = '0';
        this.charNum.node.parent.active = false;
    }
}
