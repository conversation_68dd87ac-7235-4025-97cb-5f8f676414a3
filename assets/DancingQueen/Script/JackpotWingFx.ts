import { _decorator, Component, Tween, tween, Node } from "cc";

const { ccclass, property } = _decorator;

@ccclass
export default class JackpotWingFx extends Component {

    @property(Node)
    rightWing: Node = null;

    @property(Node)
    leftWing: Node = null;

    @property
    angleUp: number = 5;

    @property
    angleDown: number = -5;

    @property
    duration: number = 0.5;

    protected onEnable(): void {
        this.rightWing.angle = this.angleUp;
        this.leftWing.angle = this.angleDown;
        // wing separate
        tween(this.rightWing)
            .to(this.duration, { angle: this.angleDown }, { easing: 'sineOut' })
            .to(this.duration, { angle: this.angleUp }, { easing: 'sineIn' })
            .union()
            .repeatForever()
            .start();

        tween(this.leftWing)
            .to(this.duration, { angle: this.angleUp }, { easing: 'sineOut' })
            .to(this.duration, { angle: this.angleDown }, { easing: 'sineIn' })
            .union()
            .repeatForever()
            .start();
    }

    protected onDisable(): void {
        // this.rightWing.stopAllActions();
        // this.leftWing.stopAllActions();
        Tween.stopAllByTarget(this.rightWing);
        Tween.stopAllByTarget(this.leftWing);
    }
}
