import { _decorator, RichText } from "cc";
import Dialog from "../../Lobby/scripts/common/Dialog";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("DancingQueen/DQConfirmDialog")
export class DQConfirmDialog extends Dialog {
    @property(RichText)
    private richTextMessage: RichText = null;
    
    private autoCloseTimer: number = null;

    private onDismissed: ((isConfirmed: boolean) => void) | null = null;
    private isConfirmed: boolean = false;
    isClickedConfirm: boolean = false;

    showConfirm(msg: string, doneTitle?: string, confirmTitle?: string, onDismissed?: (isConfirm: boolean) => void): void {
        // Call parent show4 method
        this.isClickedConfirm = false;
        this.onDismissed = onDismissed;
        this.richTextMessage.string = msg;

        // Clear any existing timer
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }

        this.scheduleOnce(this.autoClose, 5);
        super.show();
    }

    private autoClose = () => {
        // Auto-close without confirming (equivalent to cancel/done button)
        this.actConfirm();
    }

    actConfirm() {
        // Clear auto-close timer when user manually confirms
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }
        this.isConfirmed = true;
        this.dismiss();
    }

    dismiss() {
        // Clear auto-close timer when dialog is manually dismissed
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }
        super.dismiss();
    }

    _onDismissed(): void {
        super._onDismissed();
        this.onDismissed?.(this.isConfirmed);
    }
}
