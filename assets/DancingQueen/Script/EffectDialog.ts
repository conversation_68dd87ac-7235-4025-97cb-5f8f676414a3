import { _decorator, Label, v3 } from "cc";
import Dialog from "../../Lobby/scripts/common/Dialog";
import { Utils } from "../../Lobby/scripts/common/Utils";

const { ccclass, property } = _decorator;

@ccclass
export class EffectDialog extends Dialog {
    @property
    private duration: number = 1;

    @property(Label)
    private prizeValue: Label = null;

    onDismissed: () => void;

    showEffect(value: number, cb: () => void, isLabelAnim: boolean = true) {
        super.show();
        if (this.prizeValue) {
            this.prizeValue.string = "0";
            const halfDuration = this.duration / 2;
            if (isLabelAnim) {
                Utils.numberTo(this.prizeValue, value, halfDuration);
            } else {
                this.prizeValue.string = value.toString();
            }
        }
        this.scheduleOnce(() => {
            this.dismiss();
            cb(); // ✅ GỌI callback sau khi dismiss
        }, this.duration);
    }

    _onShowed() {
        super._onShowed();
        this.node.setPosition(v3(0, 0, 0));
    }

    _onDismissed() {
        super._onDismissed();
        // destroy node
        this.node.removeFromParent();
        this.node.destroy();
    }

}
