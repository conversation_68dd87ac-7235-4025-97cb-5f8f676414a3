<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>Mic_Gold_001.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,0},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_003.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,160},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_005.png</key>
      <dict>
        <key>frame</key>
        <string>{{160,0},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_007.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,320},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_009.png</key>
      <dict>
        <key>frame</key>
        <string>{{160,160},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_011.png</key>
      <dict>
        <key>frame</key>
        <string>{{320,0},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_013.png</key>
      <dict>
        <key>frame</key>
        <string>{{160,320},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_015.png</key>
      <dict>
        <key>frame</key>
        <string>{{320,160},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_017.png</key>
      <dict>
        <key>frame</key>
        <string>{{480,0},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_019.png</key>
      <dict>
        <key>frame</key>
        <string>{{320,320},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
      <key>Mic_Gold_021.png</key>
      <dict>
        <key>frame</key>
        <string>{{480,160},{160,160}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,160}}</string>
        <key>sourceSize</key>
        <string>{160,160}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>pixelFormat</key>
      <string>RGBA8888</string>
      <key>premultiplyAlpha</key>
      <false/>
      <key>realTextureFileName</key>
      <string>MicGold.png</string>
      <key>size</key>
      <string>{640,480}</string>
      <key>textureFileName</key>
      <string>MicGold</string>
    </dict>
  </dict>
</plist>
