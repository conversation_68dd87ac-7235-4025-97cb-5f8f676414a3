<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>l2_00.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_01.png</key>
      <dict>
        <key>frame</key>
        <string>{{160,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_02.png</key>
      <dict>
        <key>frame</key>
        <string>{{320,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_03.png</key>
      <dict>
        <key>frame</key>
        <string>{{480,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_04.png</key>
      <dict>
        <key>frame</key>
        <string>{{640,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_05.png</key>
      <dict>
        <key>frame</key>
        <string>{{800,0},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_06.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,384},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_07.png</key>
      <dict>
        <key>frame</key>
        <string>{{160,384},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_08.png</key>
      <dict>
        <key>frame</key>
        <string>{{320,384},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
      <key>l2_09.png</key>
      <dict>
        <key>frame</key>
        <string>{{480,384},{160,384}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{160,384}}</string>
        <key>sourceSize</key>
        <string>{160,384}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>pixelFormat</key>
      <string>RGBA8888</string>
      <key>premultiplyAlpha</key>
      <false/>
      <key>realTextureFileName</key>
      <string>fxExpandWild.png</string>
      <key>size</key>
      <string>{1024,1024}</string>
      <key>textureFileName</key>
      <string>fxExpandWild</string>
    </dict>
  </dict>
</plist>
