<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>frames</key>
    <dict>
      <key>x2.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,0},{146,99}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{146,99}}</string>
        <key>sourceSize</key>
        <string>{146,99}</string>
      </dict>
      <key>x3.png</key>
      <dict>
        <key>frame</key>
        <string>{{146,0},{145,100}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{145,100}}</string>
        <key>sourceSize</key>
        <string>{145,100}</string>
      </dict>
      <key>x1.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,99},{141,99}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{141,99}}</string>
        <key>sourceSize</key>
        <string>{141,99}</string>
      </dict>
      <key>Gift1.png</key>
      <dict>
        <key>frame</key>
        <string>{{291,0},{138,145}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{138,145}}</string>
        <key>sourceSize</key>
        <string>{138,145}</string>
      </dict>
      <key>A.png</key>
      <dict>
        <key>frame</key>
        <string>{{429,100},{83,111}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{83,111}}</string>
        <key>sourceSize</key>
        <string>{83,111}</string>
      </dict>
      <key>Gift2.png</key>
      <dict>
        <key>frame</key>
        <string>{{141,100},{138,145}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{138,145}}</string>
        <key>sourceSize</key>
        <string>{138,145}</string>
      </dict>
      <key>Gift3.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,198},{138,145}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{138,145}}</string>
        <key>sourceSize</key>
        <string>{138,145}</string>
      </dict>
      <key>R.png</key>
      <dict>
        <key>frame</key>
        <string>{{0,343},{88,116}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{88,116}}</string>
        <key>sourceSize</key>
        <string>{88,116}</string>
      </dict>
      <key>Y.png</key>
      <dict>
        <key>frame</key>
        <string>{{88,343},{76,116}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{76,116}}</string>
        <key>sourceSize</key>
        <string>{76,116}</string>
      </dict>
      <key>Z.png</key>
      <dict>
        <key>frame</key>
        <string>{{279,145},{92,112}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{92,112}}</string>
        <key>sourceSize</key>
        <string>{92,112}</string>
      </dict>
      <key>C.png</key>
      <dict>
        <key>frame</key>
        <string>{{164,245},{72,111}}</string>
        <key>offset</key>
        <string>{0,0}</string>
        <key>rotated</key>
        <false/>
        <key>sourceColorRect</key>
        <string>{{0,0},{72,111}}</string>
        <key>sourceSize</key>
        <string>{72,111}</string>
      </dict>
    </dict>
    <key>metadata</key>
    <dict>
      <key>format</key>
      <integer>2</integer>
      <key>pixelFormat</key>
      <string>RGBA8888</string>
      <key>premultiplyAlpha</key>
      <false/>
      <key>realTextureFileName</key>
      <string>_accumulate.png</string>
      <key>size</key>
      <string>{512,459}</string>
      <key>textureFileName</key>
      <string>_accumulate</string>
    </dict>
  </dict>
</plist>
