[{"__type__": "cc.AnimationClip", "_name": "bonus_explode", "_objFlags": 0, "__editorExtras__": {"embeddedPlayerGroups": []}, "_native": "", "sample": 60, "speed": 1, "wrapMode": 1, "enableTrsBlending": false, "_duration": 0.3333333333333333, "_hash": 500763545, "_tracks": [{"__id__": 1}, {"__id__": 7}, {"__id__": 18}, {"__id__": 29}], "_exoticAnimation": null, "_events": [], "_embeddedPlayers": [], "_additiveSettings": {"__id__": 35}, "_auxiliaryCurveEntries": []}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 2}, "proxy": null}, "_channel": {"__id__": 5}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 3}, {"__id__": 4}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "sprite"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 6}}, {"__type__": "cc.RealCurve", "_times": [0, 0.1666666716337204], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 8}, "proxy": null}, "_channels": [{"__id__": 10}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 9}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "sprite"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 11}}, {"__type__": "cc.RealCurve", "_times": [0], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 13}}, {"__type__": "cc.RealCurve", "_times": [0], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 15}}, {"__type__": "cc.RealCurve", "_times": [0], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 17}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.VectorTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 19}, "proxy": null}, "_channels": [{"__id__": 21}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}], "_nComponents": 3}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 20}, "scale"]}, {"__type__": "cc.animation.HierarchyPath", "path": "fx_spark"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 22}}, {"__type__": "cc.RealCurve", "_times": [0, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.5, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 24}}, {"__type__": "cc.RealCurve", "_times": [0, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1.5, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 26}}, {"__type__": "cc.RealCurve", "_times": [0, 0.25], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 28}}, {"__type__": "cc.RealCurve", "_times": [], "_values": [], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.animation.RealTrack", "_binding": {"__type__": "cc.animation.TrackBinding", "path": {"__id__": 30}, "proxy": null}, "_channel": {"__id__": 33}}, {"__type__": "cc.animation.TrackPath", "_paths": [{"__id__": 31}, {"__id__": 32}, "opacity"]}, {"__type__": "cc.animation.HierarchyPath", "path": "fx_spark"}, {"__type__": "cc.animation.ComponentPath", "component": "cc.UIOpacity"}, {"__type__": "cc.animation.Channel", "_curve": {"__id__": 34}}, {"__type__": "cc.RealCurve", "_times": [0, 0.0833333358168602, 0.3333333432674408], "_values": [{"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 1, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 255, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}, {"__type__": "cc.RealKeyframeValue", "interpolationMode": 0, "tangentWeightMode": 0, "value": 0, "rightTangent": 0, "rightTangentWeight": 0, "leftTangent": 0, "leftTangentWeight": 0, "easingMethod": 0, "__editorExtras__": null}], "preExtrapolation": 1, "postExtrapolation": 1}, {"__type__": "cc.AnimationClipAdditiveSettings", "enabled": false, "refClip": null}]