import { _decorator, Component, Node, Label, Button, Toggle, Animation, log, sp, Tween, tween, v3, Vec3, UIOpacity, ParticleSystem, CurveRange } from 'cc';
import App from '../../Lobby/scripts/common/App';
import { BroadcastReceiver } from '../../Lobby/scripts/common/BroadcastListener';
import Configs from '../../Lobby/scripts/common/Config';
import Http from '../../Lobby/scripts/common/Http';
import KingdomSignalRClient from '../../Lobby/scripts/common/networks/KingdomSignalRClient';
import { InfoView } from '../../Lobby/scripts/common/slot/InfoView';
import { EffectCreator, SlotPlayerResponse, EnumEffect } from '../../Lobby/scripts/common/slot/SlotConfig';
import { Utils } from '../../Lobby/scripts/common/Utils';
import { KingdomAudioManager, AUDIO_CLIP } from './KingdomAudioManager';
import KingdomPopupBonus from './KingdomPopupBonus';
import KingdomPopupGuide from './KingdomPopupGuide';
import KingdomPopupLSC from './KingdomPopupLSC';
import KingdomPopupLSH from './KingdomPopupLSH';
import KingdomPopupSelectLine from './KingdomPopupSelectLine';
import KingdomTrial, { TrialResult } from './KingdomTrial';
import KingdomSlotMachine from './SlotMachine/KingdomSlotMachine';
import Toast from './Toast';
import { EventIcon } from '../../Lobby/scripts/common/slot/EventIcon';
import eventBus from '../../Lobby/scripts/common/EventBus';

const { ccclass, property, menu } = _decorator;

export interface IKingdomObserver {
    onChangeRoom(sender: KingdomController, roomID: number): void;
}

@ccclass
@menu("Kingdom/Controller")
export default class KingdomController extends Component {
    private static sharedInstance?: KingdomController;
    private observers: { [index: string]: IKingdomObserver } = {};

    public static getInstance(): KingdomController {
        return this.sharedInstance || (this.sharedInstance = new this());
    }

    public addObserver(key: string, observer: IKingdomObserver) {
        this.observers[key] = observer;
    }

    public removeObserver(key: string) {
        delete this.observers[key];
    }

    public async dispatch(callback: (observer: IKingdomObserver) => Promise<void>) {
        await Promise.all(Object.keys(this.observers).map(key => callback(this.observers[key])));
    }

    @property(KingdomSlotMachine)
    private slotMachine: KingdomSlotMachine = null;

    @property(Toast) toast: Toast = null;
    @property(KingdomPopupLSC) popupLSC: KingdomPopupLSC = null;
    @property(KingdomPopupLSH) popupLSH: KingdomPopupLSH = null;
    @property(KingdomPopupGuide) popupGuide: KingdomPopupGuide = null;
    @property(KingdomPopupBonus) popupBonus: KingdomPopupBonus = null;
    @property(KingdomPopupSelectLine) popupSelectLine: KingdomPopupSelectLine = null;

    @property(Node) mainGame: Node = null;
    @property(Node) bottomHud: Node = null;
    @property(Node) buttonBackPopup: Node = null;
    @property(Node) warningPopup: Node = null;

    @property(Label) lblBet: Label = null;
    @property(Label) lblJackpot: Label = null;
    @property(Label) lblSession: Label = null;
    @property(Label) lblWinCash: Label = null;
    @property(Label) lblTotalBet: Label = null;
    @property(Label) lblTotalSelectedLine: Label = null;
    @property(Label) lblTotalAutoSpin: Label = null;
    @property(Node) textAutoSpin: Node = null;
    @property(Label) lblFreeSpin: Label = null;

    @property(Label) lblJackpotPrize: Label = null;
    @property(Label) lblJackpotNum: Label = null;
    @property(Label) lblBigWinPrize: Label = null;
    @property(Label) lblNormalPrize: Label = null;
    @property(Label) lblFreeSpinEffect: Label = null;
    @property(Label) lblFreeTicket: Label = null;

    @property(Button) btnSpin: Button = null;
    @property(Button) btnSelectLine: Button = null;
    @property(Button) btnStopAutoSpin: Button = null;
    @property(Toggle) toggleAutoSpin: Toggle = null;
    @property(Toggle) toggleSetting: Toggle = null;
    @property(Toggle) toggleTurbo: Toggle = null;
    @property(Toggle) toggleTrial: Toggle = null;
    @property(Label) lblTrialBalance: Label = null;
    @property(Toggle) toggleMusic: Toggle = null;
    @property(Toggle) toggleSound: Toggle = null;
    @property(Button) btnBetUp: Button = null;
    @property(Button) btnBetDown: Button = null;

    @property(Animation) effectBigWin: Animation = null;
    @property(Animation) effectJackpot: Animation = null;
    @property(Animation) effectFreeSpin: Animation = null;
    @property(Node) effectBonus: Node = null;

    @property(InfoView) infoPanel: InfoView = null;
    @property(EventIcon) eventIcon: EventIcon = null;
    @property(ParticleSystem) coinRainEffect: ParticleSystem = null;

    private _effectCreator: EffectCreator;
    private _isSpinning: boolean = false;
    public get isSpinning(): boolean {
        return this._isSpinning;
    }
    public set isSpinning(value: boolean) {
        this._isSpinning = value;

        this.btnSpin.interactable = !value;
        this.btnSelectLine.interactable = !value;
        this.toggleAutoSpin.isChecked = false;
        this.toggleAutoSpin.interactable = !value;
        this.btnBetUp.interactable = !value;
        this.btnBetDown.interactable = !value;

        if (value) {
            this.resetAllEffect();
        }
    }

    private _isTurbo: boolean = false;
    public get isTurbo(): boolean {
        return this._isTurbo;
    }
    public set isTurbo(value: boolean) {
        this._isTurbo = value;
        this.slotMachine.setTurbo(value);
    }

    totalFreeSpinPrize: number = 0;
    private _totalFreeSpin: number = 0;
    public get totalFreeSpin(): number {
        return this._totalFreeSpin;
    }
    private set totalFreeSpin(freeSpin: number) {
        this._totalFreeSpin = freeSpin;
        this.lblFreeSpin.string = Utils.formatNumber(freeSpin);
        this.lblFreeSpin.node.parent.active = freeSpin > 0;
    }

    private _currentAutoSpin: number = 0;
    public get currentAutoSpin(): number {
        return this._currentAutoSpin;
    }
    public set currentAutoSpin(value: number) {
        this._currentAutoSpin = value;

        this.textAutoSpin.active = value <= 0;
        this.lblTotalAutoSpin.string = Utils.formatNumber(value);
        this.btnStopAutoSpin.node.active = value > 0;
        this.lblTotalAutoSpin.node.active = value > 0;
    }

    private kingdomPlayerResponse: SlotPlayerResponse = null;
    private isTrial: boolean = false;
    private _trialBalance: number = 0;
    public get trialBalance(): number {
        return this._trialBalance;
    }
    public set trialBalance(value: number) {
        this._trialBalance = value;
        this.lblTrialBalance.string = Utils.formatNumber(value);
    }

    private _roomID: number = 1;
    public get roomID(): number {
        return this._roomID;
    }
    public set roomID(value: number) {
        this._roomID = value;
        this.dispatch(async (observer: IKingdomObserver) => {
            observer.onChangeRoom(this, value);
        });
        KingdomSignalRClient.getInstance().send(
            'PlayNow', [{ "CurrencyID": Configs.Login.CurrencyID, "RoomID": value }],
            (data) => {
            }
        )
    }

    private _freeTicket: number = 0;
    public get freeTicket(): number {
        return this._freeTicket;
    }
    public set freeTicket(value: number) {
        if (this.isTrial) return;
        this._freeTicket = value;
        this.lblFreeTicket.string = value.toString();
        this.lblFreeTicket.node.parent.active = value > 0;
    }
    private _trialResultHelper: TrialResult;


    protected onLoad() {
        KingdomController.sharedInstance = this;
        this.initListeners();
        this.initEffects();
        this.initHubs();
        this.slotMachine.setTurbo(false);
        this.slotMachine.initializeReels();
        this._trialResultHelper = new TrialResult(this);
    }


    protected start() {
        this.currentAutoSpin = 0;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.roomID = 1;
    }

    onEnable() {
        this.toggleMusic.isChecked = !KingdomAudioManager.Instance.isMuteMusic;
        this.toggleSound.isChecked = !KingdomAudioManager.Instance.isMuteEffect;

        // Resume audio if it was paused (when game becomes visible again)
        if (KingdomAudioManager.Instance.isPaused) {
            KingdomAudioManager.Instance.resumeAllAudio();
        }
    }

    private initListeners() {
        this.btnSelectLine.node.on('click', this.showSelectLine.bind(this));
        this.toggleTurbo.node.on('click', () => this.isTurbo = !this.isTurbo);
        [2000, 1000, 500, 100, 50, 20, 10].forEach((value, index) => {
            this.toggleAutoSpin.checkMark.node.children[index].on('click', () => {
                KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                this.toggleAutoSpin.isChecked = false;
                if (this.isTrial) {
                    this.showWarningPopup(App.instance.getTextLang("me35"));
                    return;
                }
                this.currentAutoSpin = value;
                this.onClickSpin();
            })
        })
        this.popupSelectLine.setOnSelectedChanged((selectedLines: number[]) => {
            const lineCount = selectedLines.length;
            this.lblTotalSelectedLine.string = lineCount.toString();
            this.lblTotalBet.string = Utils.formatNumber(lineCount * this.kingdomPlayerResponse.BetValue);
        });
        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            this.infoPanel.setInfo(Configs.Login.Nickname, Configs.Login.Avatar, Configs.Login.GoldBalance);
        }, this);
    }

    private initHubs() {
        KingdomSignalRClient.getInstance().receive("JoinGame", (data: SlotPlayerResponse) => {
            log("JoinGame", data);

            this.kingdomPlayerResponse = data;
            this.lblSession.string = `#`;

            this.popupSelectLine.setLines(data.SlotInfo.LastLineData);
            const lineCount = this.popupSelectLine.getSelectedLines().length;
            this.lblBet.string = Utils.formatNumber(data.BetValue);
            this.lblTotalBet.string = Utils.formatNumber(lineCount * data.BetValue);
            this.lblWinCash.string = Utils.formatNumber(data.SlotInfo.LastPrizeValue);

            this.updateMultiplierIcon();
            this.checkFreeTicket();

            if (data.SlotInfo.FreeSpins > 0) {
                this.totalFreeSpin = data.SlotInfo.FreeSpins;
                this.lblFreeSpinEffect.string = data.SlotInfo.FreeSpins.toString();
                this._effectCreator[EnumEffect.FREESPIN].effect().then(() => {
                    this.onClickSpin();
                })
            }
        })

        KingdomSignalRClient.getInstance().receive("ResultSpin", async (data: SlotPlayerResponse) => {
            log("ResultSpin", data);

            this.kingdomPlayerResponse = data;
            this.lblSession.string = `#${data.SpinData.SpinID}`;
            this.resetSession();
            // this.updateGoldCoin(parseInt(data.Account.GoldBalance));

            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.REEL_SPIN);
            await this.slotMachine.startSpin(data.SpinData.SlotsData);
            this.onSpinComplete();

        })

        KingdomSignalRClient.getInstance().receive("UpdateJackPot", (data: number) => {
            Utils.numberTo(this.lblJackpot, data, 0.5);
        })

        KingdomSignalRClient.getInstance().receive("ResultBonusGame", (data: SlotPlayerResponse) => {
            log("ResultBonusGame", data);
            this.updateGoldCoin(parseInt(data.Account.GoldBalance));
        })

        KingdomSignalRClient.getInstance().receive("MessageError", (data: number) => {
            this.showWarningPopup(App.instance.getTextLang(`me${data}`));
        })
    }

    private initEffects() {
        this.effectBigWin.node.active = false;
        this.effectJackpot.node.active = false;
        this.effectFreeSpin.node.active = false;
        this.effectBonus.active = false;

        this._effectCreator = {
            [EnumEffect.BIGWIN]: {
                condition: () => {
                    return this.getPayLinePrize() >= 85 * this.kingdomPlayerResponse.BetValue;
                },
                effect: async () => {
                    this.effectBigWin.node.active = true;
                    const payLinePrize = this.getPayLinePrize();
                    this.effectBigWin.play("TextEffect");
                    this.showTextBigWin(payLinePrize, 6);
                    this.makeCoinRain(15);
                    await new Promise<void>(resolve => {
                        this.effectBigWin.on(Animation.EventType.FINISHED, () => {
                            this.scheduleOnce(() => {
                                this.coinRainEffect.stopEmitting();
                                this.effectBigWin.node.active = false;
                                resolve();
                            }, 8)
                        }, this);
                    });
                }
            },
            [EnumEffect.JACKPOT]: {
                condition: () => {
                    return this.kingdomPlayerResponse.SpinData.IsJackpot;
                },
                effect: async () => {
                    const payLinePrize = this.getPayLinePrize();

                    this.effectJackpot.node.active = true;
                    this.effectJackpot.play("TextEffect");
                    this.showTextJackpot(payLinePrize, 10);
                    this.makeCoinRain(40);
                    this.lblJackpotNum.string = this.kingdomPlayerResponse.SpinData.JackpotNum.toString();
                    await new Promise<void>(resolve => {
                        this.effectJackpot.on(Animation.EventType.FINISHED, () => {
                            this.scheduleOnce(() => {
                                this.coinRainEffect.stopEmitting();
                                this.effectJackpot.node.active = false;
                                resolve();
                            }, 15)
                        }, this);
                    });
                }
            },
            [EnumEffect.FREESPIN]: {
                condition: () => {
                    return this.kingdomPlayerResponse.SpinData.IsFreeSpin;
                },
                effect: async () => {
                    // this.lblFreeSpin.node.parent.active = true;
                    this.effectFreeSpin.node.active = true;
                    this.effectFreeSpin.play("FxFreeSpin");
                    await new Promise<void>(resolve => {
                        this.scheduleOnce(() => {
                            this.effectFreeSpin.node.active = false;
                            resolve();
                        }, 3.5)
                    });
                }
            },
            [EnumEffect.BONUS]: {
                condition: () => {
                    return this.kingdomPlayerResponse.SpinData.BonusGameData !== "";
                },
                effect: async () => {
                    this.effectBonus.active = true;
                    let anim = this.effectBonus.getComponentInChildren(sp.Skeleton);
                    anim.setAnimation(0, "animation", false);
                    KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.FX_BONUS);
                    await new Promise<void>(resolve => {
                        this.scheduleOnce(() => {
                            this.effectBonus.active = false;
                            resolve();
                        }, 6)
                    });
                    await this.showBonusGame();
                }
            }
        }
    }

    private onClickSpin() {
        this.isSpinning = true;

        if (this.isTrial) {
            this.resetSession();
            const res = this._trialResultHelper.getResult();
            this.kingdomPlayerResponse.SpinData = res;
            this.trialBalance -= res.TotalBetValue;
            this.lblSession.string = `#${res.SpinID}`;
            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.REEL_SPIN);
            this.slotMachine.startSpin(res.SlotsData).then(() => {
                this.onSpinComplete();
                this.trialBalance += res.PayLinePrizeValue;
            });
            return;
        }
        if (this.freeTicket > 0 && this.totalFreeSpin <= 0) {
            this._spin(true);
        }
        else {
            this._spin(false);
        }
    }

    private _spin(byTicket: boolean) {
        const params = {
            "CurrencyID": Configs.Login.CurrencyID,
            "RoomID": this.roomID,
            "Lines": this.popupSelectLine.getSelectedLines().join(",")
        }
        if (params.Lines === "") {
            this.isSpinning = false;
            this.currentAutoSpin = 0;
            this.showWarningPopup(App.instance.getTextLang("me-60215"));
            return;
        }

        const event = byTicket ? 'SpinForTicket' : 'Spin';
        KingdomSignalRClient.getInstance().send(
            event, [params],
            (data) => {
                if (data.c < 0) {
                    this.currentAutoSpin = 0;
                    this.isSpinning = false;
                    this.showWarningPopup(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    private parsePositionData(positionData: string): { payLines: number[][], commonPayLines: number[] } {
        const payLines = positionData.split(";").map(row => row.split(",").map(Number));
        const commonPayLines = Array.from(new Set(payLines.flat()));
        return { payLines, commonPayLines };
    }


    private parsePrizeData(prizeData: string): string[] {
        return prizeData.split(";");
    }

    private async onSpinComplete() {
        let delay = 0.5;
        KingdomAudioManager.Instance.stopEffect(AUDIO_CLIP.REEL_SPIN);
        const payLinePrize = this.getPayLinePrize();
        let isFreeSpinCompleted = false;
        this.lblWinCash.string = '0';

        if (this.totalFreeSpin > 0) {
            delay = 1;
            this.totalFreeSpinPrize += payLinePrize;
            this.totalFreeSpin--;
            if (this.totalFreeSpin === 0) {
                isFreeSpinCompleted = true;
                // this.lblFreeSpin.node.parent.active = false;
            }
        }
        else {
            if (this.freeTicket > 0) {
                this.freeTicket--;
            }
            if (this.currentAutoSpin > 0) {
                this.currentAutoSpin--;
            }
        }

        const { payLines, commonPayLines } = this.parsePositionData(this.kingdomPlayerResponse.SpinData.PositionData);
        const prizeData = this.parsePrizeData(this.kingdomPlayerResponse.SpinData.PrizesData);

        if (payLinePrize > 0) {
            this.showTextPayLine(payLinePrize);
            this.slotMachine.highlightItems(commonPayLines, prizeData).then();
            Utils.numberTo(this.lblWinCash, payLinePrize, 0.5);
        }

        // Replace the runAction with tween system
        Tween.stopAllByTarget(this.node);

        tween(this.node)
            .delay(delay)
            .call(() => {
                this.showEffects(async () => {
                    // update gold balance
                    this.updateGoldCoin(parseInt(this.kingdomPlayerResponse.Account.GoldBalance));
                    let showSequentialWinLine = async () => {
                        if(payLinePrize > 0) {
                            for (let i = 0; i < payLines.length; i++) {
                                await this.slotMachine.highlightItems(payLines[i], [prizeData[i]]);
                            }
                        }
                    }
                    if (this.totalFreeSpin > 0) {
                        if(!this.isTurbo) await showSequentialWinLine();
                        this.isSpinning = false;
                        this.onClickSpin();
                        return;
                    }
                    if (isFreeSpinCompleted) {
                        await this.toast.showFreeSpinReward(this.totalFreeSpinPrize);
                        this.totalFreeSpinPrize = 0;
                    }
                    if (this.currentAutoSpin > 0) {
                        if(!this.isTurbo) await showSequentialWinLine();
                        this.isSpinning = false;
                        this.onClickSpin();
                        return;
                    }
                    this.isSpinning = false;
                    await showSequentialWinLine();
                });
            })
            .start();
    }

    private showEffects(finishCallback: Function) {
        let awaitable: Function[] = [];

        // Check and add big win or jackpot effect (mutually exclusive)
        if (this._effectCreator[EnumEffect.JACKPOT].condition()) {
            this.currentAutoSpin = 0;
            awaitable.push(this._effectCreator[EnumEffect.JACKPOT].effect);
        }
        else if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BIGWIN].effect);
        }

        if (this._effectCreator[EnumEffect.BONUS].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BONUS].effect);
        }

        if (this._effectCreator[EnumEffect.FREESPIN].condition()) {
            const newFreeSpin = this.kingdomPlayerResponse.SpinData.TotalFreeSpin;

            if (this.totalFreeSpin <= 0) {
                this.lblFreeSpinEffect.string = newFreeSpin.toString();
                awaitable.push(this._effectCreator[EnumEffect.FREESPIN].effect);
                awaitable.push(async () => {
                    this.totalFreeSpin += newFreeSpin;
                });
            } else {
                this.totalFreeSpin += newFreeSpin;
            }
        }

        // Execute all effects in sequence
        (async () => {
            for (const aw of awaitable) {
                await aw();
            }
        })().then(() => {
            finishCallback();
        });
    }

    private updateGoldCoin(coin: number) {
        if (coin <= 0 || this.isTrial) return;
        Configs.Login.GoldBalance = coin;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    private getPayLinePrize(): number {
        return this.kingdomPlayerResponse.SpinData.PayLinePrizeValue ?? 0;
    }

    updateMultiplierIcon() {
        this.getRoomMultiplier(this.roomID).then((multiplier) => {
            this.eventIcon.node.active = multiplier > 0;
            this.eventIcon.setIcon(multiplier, 0);
        });
    }

    checkFreeTicket() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountTicket'], { CurrencyID: Configs.Login.CurrencyID, GameID: Configs.InGameIds.Kingdom }, (status, res) => {
            if (status === 200) {
                const data = res.d.filter(item => item.roomID === this.roomID);
                const count = data.reduce((sum, item) => sum + item.balance, 0);
                this.freeTicket = count;
            }
        })
    }

    private async getRoomMultiplier(roomID: number): Promise<number> {
        return new Promise((resolve) => {
            Http.get(
                Configs.App.DOMAIN_CONFIG['GetListJackpot'],
                { CurrencyID: Configs.Login.CurrencyID },
                (status, res) => {
                    if (status !== 200 || !res?.d) {
                        resolve(0);
                        return;
                    }

                    const item = res.d.find(item =>
                        item.gameID === Configs.InGameIds.Kingdom &&
                        item.roomID === roomID &&
                        item.nextJackpot === 0
                    );

                    resolve(item ? item.multiplier : 0);
                }
            );
        });
    }

    private onClickRoomID(button: Button, roomID: string) {
        const ID = parseInt(roomID);
        this.roomID = ID;
        // this.popupSelectLine.currentRoom = ID;
    }

    private onChangeBetUp() {
        if (this.isTrial) {
            this.showWarningPopup(App.instance.getTextLang("me35"));
            return;
        }
        let roomID = this.roomID;
        roomID = roomID === 3 ? 1 : roomID + 1;
        this.roomID = roomID;
        // this.popupSelectLine.currentRoom = roomID;
    }

    private onChangeBetDown() {
        if (this.isTrial) {
            this.showWarningPopup(App.instance.getTextLang("me35"));
            return;
        }
        let roomID = this.roomID;
        roomID = roomID === 1 ? 3 : roomID - 1;
        this.roomID = roomID;
        // this.popupSelectLine.currentRoom = roomID;
    }

    private async showBonusGame() {
        const bonusData = this.kingdomPlayerResponse.SpinData.BonusGameData;
        let startBonus: number = this.kingdomPlayerResponse.SpinData.StartBonus;
        await new Promise<void>((resolve) => {
            this.popupBonus.onFinished = () => {
                this.onFinishBonusGame();
                resolve();
            };
            KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.ENTER_BONUS);
            this.popupBonus.showPopup(bonusData, startBonus);
        });
        KingdomAudioManager.Instance.stopEffect(AUDIO_CLIP.REWARD_BONUS);
        const finishWin = this.popupBonus.getFinishWin();
        this.lblWinCash.string = Utils.formatNumber(finishWin);
        if (this.totalFreeSpin > 0) {
            this.totalFreeSpinPrize += finishWin;
        }
        await this.toast.showBonusReward(finishWin);
    }

    private onFinishBonusGame() {
        KingdomSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "RoomID": this.roomID,
                "CurrencyID": Configs.Login.CurrencyID,
            }],
            (data) => {
                if (data.c < 0) {
                    this.showWarningPopup(App.instance.getTextLang("me" + data.c));
                }
            }
        );
    }

    public setButtonEnable(enable: boolean) {
        this.btnSpin.interactable = enable;
        this.btnSelectLine.interactable = enable;
        this.toggleAutoSpin.isChecked = false;
        this.toggleAutoSpin.interactable = enable;
        this.toggleTrial.interactable = enable;
        this.btnBetUp.interactable = enable;
        this.btnBetDown.interactable = enable;
        this.btnStopAutoSpin.interactable = true;
    }

    public resetAllEffect() {
        this.effectBigWin.stop();
        this.effectJackpot.stop();
        Tween.stopAllByTarget(this.effectFreeSpin);
        this.effectBonus.getComponentInChildren(sp.Skeleton).clearTrack(0);

        this.effectBigWin.node.active = false;
        this.effectJackpot.node.active = false;
        this.effectFreeSpin.node.active = false;
        this.effectBonus.active = false;
    }

    private showWarningPopup(message: string) {
        this.warningPopup.active = true;
        this.warningPopup.getComponentInChildren(Label).string = message;
        // auto hide after 2s
        this.scheduleOnce(() => {
            this.warningPopup.active = false;
        }, 2);
    }

    private showTextPayLine(prize: number) {
        if (this._effectCreator[EnumEffect.BIGWIN].condition() || this._effectCreator[EnumEffect.JACKPOT].condition()) return;
        KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.WIN);
        this.lblNormalPrize.string = '';
        Utils.numberTo(this.lblNormalPrize, prize, 0.5);
        this.lblNormalPrize.node.parent.active = true;
        this.scheduleOnce(() => {
            this.lblNormalPrize.node.parent.active = false;
        }, 1.5);
    }

    private showTextJackpot(prize: number, duration: number) {
        KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.JACKPOT);
        KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.COUNTER, true);
        this.scheduleOnce(() => {
            KingdomAudioManager.Instance.stopEffect(AUDIO_CLIP.COUNTER);
        }, duration);
        this.lblJackpotPrize.string = '';
        Utils.numberTo(this.lblJackpotPrize, prize, duration);
    }

    private showTextBigWin(prize: number, duration: number) {
        KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.FX_BIGWIN);
        KingdomAudioManager.Instance.playEffect(AUDIO_CLIP.COUNTER, true);
        this.scheduleOnce(() => {
            KingdomAudioManager.Instance.stopEffect(AUDIO_CLIP.COUNTER);
        }, duration);
        this.lblBigWinPrize.string = '';
        Utils.numberTo(this.lblBigWinPrize, prize, duration);
    }

    // Show guide popup
    public showGuide() {
        if (this.isSpinning) return;
        this.toggleSetting.isChecked = false;

        this.mainGame.active = false;
        this.bottomHud.active = false;
        this.buttonBackPopup.active = true;

        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.show();
        this.popupSelectLine.dismiss();

        this.popupGuide.onRoomChange(this.roomID);
    }

    // Show history popup
    public showHistory() {
        if (this.isSpinning) return;
        this.toggleSetting.isChecked = false;
        this.mainGame.active = false;
        this.bottomHud.active = false;
        this.buttonBackPopup.active = true;
        this.popupLSC.show();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    // Show jackpot history popup
    public showJackpotHistory() {
        if (this.isSpinning) return;
        this.toggleSetting.isChecked = false;
        this.mainGame.active = false;
        this.bottomHud.active = false;
        this.buttonBackPopup.active = true;
        this.popupLSC.dismiss();
        this.popupLSH.show();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    // Show select line popup
    public showSelectLine() {
        if (this.isTrial) {
            this.showWarningPopup(App.instance.getTextLang("me35"));
            return;
        }
        this.toggleSetting.isChecked = false;
        this.mainGame.active = false;
        this.bottomHud.active = false;
        this.buttonBackPopup.active = true;
        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.show();
    }

    onClickSound() {
        let isMute = KingdomAudioManager.Instance.isMuteEffect;
        KingdomAudioManager.Instance.muteEffect(!isMute);
    }

    onClickMusic() {
        let isMute = KingdomAudioManager.Instance.isMuteMusic;
        KingdomAudioManager.Instance.muteMusic(!isMute);
    }

    public hidePopup() {
        this.mainGame.active = true;
        this.bottomHud.active = true;
        this.buttonBackPopup.active = false;
        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    public showFeatureInDevelop() {
        App.instance.alertDialog.showMsg(App.instance.getTextLang("fish_pu5"));
    }

    private backToLobby() {
        App.instance.confirmDialog.showMsg(App.instance.getTextLang("sl64"), (isConfirm) => {
            if (isConfirm) {
                Utils.setStorageValue("last_open_game_id", "");
                KingdomSignalRClient.getInstance().dontReceive();
                App.instance.slotGame[Configs.InGameIds.Kingdom] = null;
                App.instance.gotoLobby();
            }
        });
    }


    private actHidden() {
        this.toggleSetting.isChecked = false;
        if (this.currentAutoSpin > 0) {
            App.instance.confirmDialog.showMsg(App.instance.getTextLang("sl74"), (isConfirm) => {
                if (isConfirm) {
                    KingdomAudioManager.Instance.pauseAllAudio();
                    let root = App.instance.slotGame[Configs.InGameIds.Kingdom];
                    root.removeFromParent();
                    root.active = false;
                }
            },
            );
        }
        else {
            App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
        }
    }

    private resetSession() {
        // this.lblJackpotPrize.string = '0';
        this.slotMachine.resetAllItems();
        Tween.stopAllByTarget(this.node);
        KingdomAudioManager.Instance.stopAllEffect();
    }

    onClickStopAutoSpin() {
        this.currentAutoSpin = 0;
    }

    private onToggleTrial() {
        if (this.isSpinning) return;
        this.isTrial = this.toggleTrial.isChecked;
        const title = this.toggleTrial.node.children[2].getComponent(Label);
        if (this.isTrial) {
            this.trialBalance = 10_000_000;
            this.lblTotalSelectedLine.string = '20';
            title.string = App.instance.getTextLang("sl8");
            this.lblFreeTicket.node.parent.active = false;
        } else {
            const betValue = this.kingdomPlayerResponse.BetValue;
            const lineCount = this.popupSelectLine.getSelectedLines().length;
            this.lblBet.string = Utils.formatNumber(betValue);
            this.lblTotalSelectedLine.string = lineCount.toString();
            this.lblTotalBet.string = Utils.formatNumber(lineCount * betValue);
            title.string = App.instance.getTextLang("sl9");
            this.lblFreeTicket.node.parent.active = this.freeTicket > 0;
        }
        this.lblTrialBalance.node.parent.active = this.isTrial;
    }

    private makeCoinRain(rateOverTime: number) {
        let rate = this.coinRainEffect.rateOverTime;
        rate.mode = CurveRange.Mode.Constant;
        rate.constant = rateOverTime;
        this.coinRainEffect.rateOverTime = rate;
        this.coinRainEffect.play();
    }
}
