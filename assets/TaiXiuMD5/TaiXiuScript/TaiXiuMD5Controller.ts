import { _decorator, Node, Sprite<PERSON><PERSON>e, Label, EditBox, Button, Prefab, v2, Event, UIOpacity, EventTouch, Sprite, Animation, instantiate, Vec2, v3, tween, Tween } from 'cc';
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Configs from "db://assets/Lobby/scripts/common/Config";
import MiniGameTXMD5SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXMD5SignalRClient";
import {MiniGame} from "db://assets/Lobby/scripts/common/MiniGame";
import App from "db://assets/Lobby/scripts/common/App";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import Config from "db://assets/Lobby/scripts/common/Config";
const { ccclass, property, menu } = _decorator;

@ccclass('TaiXiuMD5Controller')
@menu("MD5/Play")
export default class TaiXiuMD5Controller extends MiniGame {
    static instance: TaiXiuMD5Controller = null;
    GAME_ID: number = 123;
    @property(Node)
    background: Node | null = null;
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame | null = null;
    @property(Label)
    lblSession: Label | null = null;
    @property(Label)
    lblRemainTime: Label | null = null;
    @property(Label)
    lblRemainWaiting: Label | null = null;
    @property(Label)
    lblSumDices: Label | null = null;
    @property(Label)
    lblTotalBetTaiAll: Label | null = null;
    @property(Label)
    lblTotalBetXiuAll: Label | null = null;
    @property(Label)
    lblTotalBetTaiCurrent: Label | null = null;
    @property(Label)
    lblTotalBetXiuCurrent: Label | null = null;
    @property(Label)
    lblBetTai: Label | null = null;
    @property(Label)
    lblBetXiu: Label | null = null;
    @property(Label)
    lblBetTaiXu: Label | null = null;
    @property(Label)
    lblBetXiuXu: Label | null = null;
    @property(Label)
    lblUserTai: Label | null = null;
    @property(Label)
    lblUserXiu: Label | null = null;
    @property(Node)
    winTextNode: Node | null = null;
    @property(EditBox)
    editBoxBetTai: EditBox | null = null;
    @property(EditBox)
    editBoxBetXiu: EditBox | null = null;
    @property(Node)
    layoutBet1: Node | null = null;
    @property(Node)
    layoutBet2: Node | null = null;
    @property(Button)
    x2BetButton: Button | null = null;
    @property(Label)
    labelTextMD5: Label | null = null;
    @property(Node)
    dicesContainer: Node | null = null;
    @property([SpriteFrame])
    listSprDice: SpriteFrame[] = [];
    @property(Node)
    nodeTai: Node | null = null;
    @property(Node)
    nodeXiu: Node | null = null;
    @property(Node)
    bowl: Node | null = null;
    @property(Button)
    buttonNan: Button | null = null;
    // HISTORY
    @property(Node)
    historyList: Node | null = null;
    @property(Node)
    historyItem: Node | null = null;
    // NOTIFICATION
    @property(Label)
    lblToast: Label | null = null;
    // 1: Gold 2: Xu
    private isBetTypeGold = true;
    // CHAT
    @property(Node)
    chatBox: Node | null = null;
    // Popup Container
    @property(Node)
    popupContainer: Node | null = null;
    @property(Prefab)
    popupEventSoiCauPrefab: Prefab | null = null;
    @property(Prefab)
    popupDetailSessionPrefab: Prefab | null = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab | null = null;
    @property(Prefab)
    popupHonorsPrefab: Prefab | null = null;
    @property(Prefab)
    popupGuidePrefab: Prefab | null = null;
    private popupSoiCau: any = null;
    private popupDetailSession: any = null;
    private popupHistory: any = null;
    private popupHonors: any = null;
    private popupGuide: any = null;
    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    private isBetting = false;
    private resetLabels = [];
    private lastLocationIDWin: number = 0;
    private readonly bowlStartPos = v2(-252, 35);
    private isOpenBowl = false;
    private lastScore: number = 0;
    private isNan: boolean = false;
    private plainTextMD5: string = "";
    private detailSessions: any[] = [];
    private isBowlMovable: boolean = false;
    private lastBetAmount: number = 0;
    private lastBetAmountXu: number = 0;
    private originalBowlIndex: number = 0;
    private betLogs = [];
    private sessionId = 0;

    public onLoad() {
        super.onLoad();
        TaiXiuMD5Controller.instance = this;
        this.resetLabels = [
            this.lblTotalBetTaiAll,
            this.lblTotalBetXiuAll,
            this.lblTotalBetTaiCurrent,
            this.lblTotalBetXiuCurrent,
        ];
        this.lblToast.node.parent.active = false;
        this.bowl.active = true;
        this.labelTextMD5.string = "";
        this.dicesContainer.getChildByName('result').active = false;
        this.originalBowlIndex = this.bowl.getSiblingIndex();

        const formatBetValue = (editBox: EditBox) => {
            let value = parseInt(editBox.string.replace(/\./g, ""));
            if (isNaN(value) || value <= 0) {
                editBox.string = "";
            } else {
                editBox.string = Utils.formatNumber(value);
            }
        };

        this.editBoxBetTai.node.on("editing-did-began", () => {
            if (this.editBoxBetTai.string == "") return;
            this.editBoxBetTai.string = Utils.formatEditBox(this.editBoxBetTai.string).toString();
        });

        this.editBoxBetXiu.node.on("editing-did-began", () => {
            if (this.editBoxBetXiu.string == "") return;
            this.editBoxBetXiu.string = Utils.formatEditBox(this.editBoxBetXiu.string).toString();
        });

        this.editBoxBetTai.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        this.editBoxBetTai.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        this.x2BetButton.enabled = false;
    }

    protected start() {
        this.lblTotalBetXiuAll.node.active = Configs.Login.PortalID > 10;
        this.lblTotalBetTaiAll.node.active = Configs.Login.PortalID > 10;
        this.bowl.on(
            Node.EventType.TOUCH_MOVE,
            (event: EventTouch) => {
                if (this.isBowlMovable === false) {
                    return;
                }
                this.bowl.setSiblingIndex(this.originalBowlIndex + 2);
                var pos = this.bowl.getPosition();
                pos.x += event.getDeltaX();
                pos.y += event.getDeltaY();
                this.bowl.position = pos;

                let distance = Utils.v2Distance(
                    new Vec2(pos.x, pos.y),
                    this.bowlStartPos
                );
                if (Math.abs(distance) > 300) {
                    this.showResult();
                    this.isOpenBowl = true;
                    this.scheduleOnce(() => {
                        this.bowl.setSiblingIndex(this.originalBowlIndex);
                        this.bowl.active = false;
                    }, 2);
                }
            },
            this
        );
    }

    public show() {
        super.show();

        MiniGameTXMD5SignalRClient.getInstance().send("GetCurrentRoomsMD5", [{GameID: this.GAME_ID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        this.initHubs();
    }

    public dismiss() {
        super.dismiss();
        // App.instance.hideGameMini("TaiXiuMD5");
    }

    onDestroy() {
        MiniGameTXMD5SignalRClient.getInstance().dontReceive();
    }

    private handleDicesAnimationBetting() {
        this.lblRemainTime.node.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.bowl.active = false;
        const animNode = this.dicesContainer.getChildByName('anim');
        animNode.active = true;
        this.dicesContainer.getChildByName('result').active = false;
        const anim = animNode.getComponent(Animation);
        anim.play();
        this.scheduleOnce(() => {
            anim.stop();
            this.bowl.active = true;
            this.bowl.setSiblingIndex(this.originalBowlIndex);
            this.bowl.setPosition(v3(this.bowlStartPos.x, this.bowlStartPos.y));
            this.isBowlMovable = false;
            animNode.active = false;
            this.lblRemainTime.node.active = true;
        }, 1);
    }

    private handleOpenBowl() {
        this.isOpenBowl = true;
        this.bowl.setSiblingIndex(this.originalBowlIndex + 2);
        tween(this.bowl)
            .to(0.5, {position: v3(0, -220, 0)})
            .call(() => {
                this.showResult();
                this.scheduleOnce(() => {
                    this.bowl.setSiblingIndex(this.originalBowlIndex);
                    this.bowl.active = false;
                }, 2);
            })
            .start();
    }

    private initHubs() {
        MiniGameTXMD5SignalRClient.getInstance().receive("currentSessionMD5", (res) => {
            this.lblSession.string = `#${res.GameSessionID}`;
            this.sessionId = res.GameSessionID;
            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);
            if (res.GameStatus === 1) {
                this.handleDicesAnimationBetting();
                this.labelTextMD5.string = res.MD5;
                this.handleBettingPhase(res.RemainBetting);
            } else {
                var remainWaiting = res.RemainWaiting;
                this.handleWaitingPhase(remainWaiting);

                if (remainWaiting > 15) {
                    this.scheduleOnce(() => {
                        MiniGameTXMD5SignalRClient.getInstance().send("GetAccountResultMD5", [{GameID: this.GAME_ID, CurrencyID: Configs.Login.CurrencyID, GameSessionID: res.GameSessionID}], () => {});
                    }, remainWaiting - 15);
                }
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("currentResultMD5", (res) => {
            this.buttonNan.enabled = false;
            this.isBowlMovable = true;
            this.plainTextMD5 = res.PlainText;
            this.editBoxBetTai.enabled = false;
            this.editBoxBetXiu.enabled = false;
            this.lblRemainWaiting.node.parent.active = false;
            const resultNode = this.dicesContainer.getChildByName('result');
            resultNode.active = true;
            const dice_1 = resultNode.getChildByName('dice_1');
            const dice_2 = resultNode.getChildByName('dice_2');
            const dice_3 = resultNode.getChildByName('dice_3');
            dice_1.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice1 - 1];
            dice_2.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice2 - 1];
            dice_3.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice3 - 1];
            this.lastLocationIDWin = res.LocationIDWin;
            this.lastScore = res.Dice1 + res.Dice2 + res.Dice3;
            if (this.isNan) {
                this.scheduleOnce(() => {
                    if (this.isOpenBowl) {
                        return;
                    }
                    this.handleOpenBowl();
                }, 15);
            } else {
                this.handleOpenBowl();
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("currentRoomsInfoMD5", (res) => {
            for (const room of res) {
                if (room == null) continue;

                const isGoldRoom = room.BetType === 1;
                const isXuRoom = room.BetType === 2;

                if ((this.isBetTypeGold && isXuRoom) || (!this.isBetTypeGold && isGoldRoom)) {
                    continue;
                }

                const betInfo = room.BetInfo;

                this.lblTotalBetXiuCurrent.string = Utils.formatMoneyOnlyK(betInfo[11].TotalBetValue1);
                this.lblTotalBetTaiCurrent.string = Utils.formatMoneyOnlyK(betInfo[11].TotalBetValue2);
                this.lblUserXiu.string = `(${Utils.formatNumber(betInfo[11].TotalAccount1)})`;
                this.lblUserTai.string = `(${Utils.formatNumber(betInfo[11].TotalAccount2)})`;

                if (Configs.Login.PortalID > 10) {
                    this.lblTotalBetXiuAll.string = Utils.formatMoneyOnlyK(betInfo[88].TotalBetValue1);
                    this.lblTotalBetTaiAll.string = Utils.formatMoneyOnlyK(betInfo[88].TotalBetValue2);
                    this.lblUserXiu.string = `(${Utils.formatNumber(betInfo[11].TotalAccount1 + betInfo[88].TotalAccount1)})`;
                    this.lblUserTai.string = `(${Utils.formatNumber(betInfo[11].TotalAccount2 + betInfo[88].TotalAccount2)})`;
                }
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("gameHistoryMD5", (res) => {
            this.historyList.removeAllChildren();
            if (res == null || res.length == 0) {
                return;
            }
            this.detailSessions = [];
            for (let i = res.length - 1; i >= 0; i--) {
                this.detailSessions.push(res[i]);

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;
                item.getChildByName("last").active = i === 0;
                item.on(Node.EventType.TOUCH_END, () => {
                    this.actPopupDetailSession(res[i].GameSessionID);
                });

                if (i === 0) {
                    Tween.stopAllByTarget(item);
                    this.scheduleOnce(() => {
                        const posUp = v3(item.position.x, 5, item.position.z);
                        const posDown = v3(item.position.x, -5, item.position.z);

                        tween(item)
                            .repeatForever(
                                tween()
                                    .to(0.3, { position: posUp })
                                    .to(0.3, { position: posDown })
                            )
                            .start();
                    }, 0);
                }

                this.historyList.addChild(item);
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("betOfAccountMD5", (res) => {
            res.forEach((item: any) => {
                this.layoutBet1.active = false;
                this.layoutBet2.active = false;
                this.editBoxBetTai.string = '';
                this.editBoxBetXiu.string = '';

                if (item.BetType == 1) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiu : this.lblBetTai;
                    betLocation.string = Utils.formatMoneyOnlyK(item.BetValue);
                } else if (item.BetType == 2) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiuXu : this.lblBetTaiXu;
                    betLocation.string = Utils.formatMoneyOnlyK(item.BetValue);
                }
            })
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("resultOfAccountMD5", (res) => {
            let totalPrize = 0;

            res.forEach((item: any) => {
                if (item.AccountID === Configs.Login.AccountID && item.PortalID === Configs.Login.PortalID && item.PrizeValue > 0) {
                    totalPrize += item.PrizeValue;
                }
            });

            if (totalPrize <= 0) {
                this.winTextNode.active = false;
                return;
            }

            this.winTextNode.active = true;
            this.winTextNode.getComponent(Label).string = '+ ' + Utils.formatNumber(totalPrize);
            this.winTextNode.position = v3(this.winTextNode.x, -50);
            tween(this.winTextNode)
                .to(3, {position: v3(this.winTextNode.x, 150)})
                .call(() => {
                    this.winTextNode.active = false;
                })
                .start();

            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        });
    }

    private handleBettingPhase(remainTime: number) {
        if (remainTime === 60) {
            this.showToast(App.instance.getTextLang("txt_taixiu_new_session"));
            this.isOpenBowl = false;
            this.resetSessionLabels();
            this.lblBetTai.string = "0";
            this.lblBetXiu.string = "0";
            this.lblBetTaiXu.string = "0";
            this.lblBetXiuXu.string = "0";
            this.lastScore = 0;
            this.lblSumDices.string = "";
            this.lblRemainTime.string = "01:00";
            this.plainTextMD5 = "";
            this.x2BetButton.enabled = this.betLogs.find((item) => item.sessionID === this.sessionId - 1 && item.betType === (this.isBetTypeGold ? 1 : 2)) !== undefined;
        }

        if (remainTime < 3) {
            this.isBetting = false;
            return;
        }

        this.isBetting = true;
        this.editBoxBetTai.enabled = true;
        this.editBoxBetXiu.enabled = true;
        this.buttonNan.enabled = true;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblRemainWaiting.node.active = false;

        let secondsLeft = remainTime;
        this.unschedule(this.countdownRemainTime);
        this.schedule(this.countdownRemainTime = () => {
            this.hideResult();
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownRemainTime);
                    return;
                }

                const minutes = Math.floor(secondsLeft / 60);
                const seconds = secondsLeft % 60;
                this.lblRemainTime.string = `${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownRemainTime);
            }
        }, 1);
    }

    private resetSessionLabels() {
        this.resetLabels.forEach(label => label.string = "0");
        this.lblUserTai.string = "(0)";
        this.lblUserXiu.string = "(0)";
    }

    private handleWaitingPhase(waitingTime: number) {
        if (waitingTime > 0) this.isBetting = false;

        if (waitingTime < 19) {
            MiniGameTXMD5SignalRClient.getInstance().send("GetCurrentResultMD5", [{ GameID: this.GAME_ID }], () => {});
        }

        this.layoutBet1.active = false;
        this.layoutBet2.active = false;
        this.editBoxBetTai.string = '';
        this.editBoxBetXiu.string = '';
        this.lblRemainTime.node.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        let secondsLeft = waitingTime;
        this.unschedule(this.countdownWaitingTime);
        this.schedule(this.countdownWaitingTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownWaitingTime);
                    return;
                }

                if (this.isOpenBowl === false && secondsLeft > 15) {
                    const secondsLeftOpenBowl = secondsLeft - 15;
                    this.lblRemainWaiting.string = `00:${secondsLeftOpenBowl < 10 ? "0" + secondsLeftOpenBowl : secondsLeftOpenBowl}`;
                    this.lblRemainWaiting.node.active = true;
                    this.lblRemainWaiting.node.parent.active = true;
                    this.lblSumDices.node.active = false;
                } else {
                    this.lblSumDices.node.active = secondsLeft > 12;
                    this.lblRemainWaiting.node.active = secondsLeft <= 12;
                    this.lblRemainWaiting.string = `00:${secondsLeft < 10 ? "0" + secondsLeft : secondsLeft}`;
                }

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownWaitingTime);
            }
        }, 1);
    }

    actBet(_event: any, data: string) {
        if (!this.isBetting) {
            return;
        }
        var isXiu = data == "1";
        var editBox = isXiu ? this.editBoxBetXiu : this.editBoxBetTai;
        var betValue = Utils.formatEditBox(editBox.string);
        if (betValue <= 0 || isNaN(betValue)) return;

        MiniGameTXMD5SignalRClient.getInstance().send("SetBetMd5", [{
            GameID: this.GAME_ID,
            CurrencyID: Configs.Login.CurrencyID,
            BetType: this.isBetTypeGold ? 1 : 2,
            Location: isXiu ? 1 : 2,
            Amount: betValue
        }], (res) => {
            if (res < 0) {
                this.showToast(App.instance.getTextLang(`me${res}`));
            }

            var log = this.betLogs.find((item) => item.sessionID === this.sessionId && item.betType === (this.isBetTypeGold ? 1 : 2));
            if (log) {
                log.betValue += betValue;
            } else {
                this.betLogs.push({
                    location: this.currentBetType,
                    betValue: betValue,
                    sessionID: this.sessionId,
                    betType: this.isBetTypeGold ? 1 : 2
                })
            }

            this.editBoxBetTai.string = "";
            this.editBoxBetXiu.string = "";
            this.layoutBet1.active = false;
            this.layoutBet2.active = false;
            this.x2BetButton.enabled = false;
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        });
    }

    actShowLayOutBet() {
        if (!this.editBoxBetTai.enabled || !this.editBoxBetXiu.enabled) {
            return;
        }

        this.layoutBet1.active = true;
        this.layoutBet2.active = false;
    }

    actShowLayoutBetCustom() {
        this.layoutBet1.active = false;
        this.layoutBet2.active = true;
    }

    private currentBetType: string = "";
    updateCurrentBetType(_event: Event, type: string) {
        this.currentBetType = type;
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    private getCurrentEditBox(): EditBox {
        return this.currentBetType === "1" ? this.editBoxBetXiu : this.editBoxBetTai;
    }

    private setEditBoxValue(editBox: EditBox, value: number) {
        editBox.string = (isNaN(value) || value === 0) ? "" : Utils.formatNumber(value);
    }

    updateBetAmount(_event: Event, amount: string) {
        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = currentAmount + parseInt(amount);
        this.setEditBoxValue(editBox, newAmount);
    }

    updateBetAmountCustom(_event: Event, amount: string) {
        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = parseInt(currentAmount.toString() + amount);
        this.setEditBoxValue(editBox, newAmount);
    }

    deleteBetAmount() {
        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string).toString();
        const newAmount = currentAmount.length > 1 ? parseInt(currentAmount.slice(0, -1)) : 0;
        this.setEditBoxValue(editBox, newAmount);
    }

    x2Bet() {
        var log = this.betLogs.find((item) => item.sessionID === this.sessionId - 1 && item.betType === (this.isBetTypeGold ? 1 : 2));
        if (!this.isBetting || !log) return;

        this.currentBetType = log.location;
        var edb = log.location == "1" ? this.editBoxBetXiu : this.editBoxBetTai;
        edb.string = Utils.formatNumber((log.betValue * 2));
    }

    clearBetAmount() {
        this.editBoxBetXiu.string = "";
        this.editBoxBetTai.string = "";
    }

    actAgree() {
        if (!this.isBetting) {
            return;
        }

        let betValue = "";

        if (this.currentBetType == "1") {
            betValue = this.editBoxBetXiu.string;
        } else if (this.currentBetType == "2") {
            betValue = this.editBoxBetTai.string;
        }

        if (!betValue || parseInt(betValue) <= 0) {
            return;
        }

        if (this.currentBetType == "1") {
            this.actBet(null, "1");
        } else if (this.currentBetType == "2") {
            this.actBet(null, "2");
        }
    }

    copyTextMD5() {
        Utils.copy(this.labelTextMD5.string);
        this.showToast(App.instance.getTextLang('txt_copied'));
    }

    actSwitchCoinXu() {
        this.isBetTypeGold = !this.isBetTypeGold;
        this.resetSessionLabels();
        this.lblBetTai.node.active = this.isBetTypeGold;
        this.lblBetXiu.node.active = this.isBetTypeGold;
        this.lblBetTaiXu.node.active = !this.isBetTypeGold;
        this.lblBetXiuXu.node.active = !this.isBetTypeGold;
        MiniGameTXMD5SignalRClient.getInstance().send("GetCurrentRoomsMD5", [{GameID: this.GAME_ID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
    }

    toggleChatBox() {
        this.chatBox.active = !this.chatBox.active;
    }

    toggleLight(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');

        on.active = !on.active;
        off.active = !off.active;

        this.background.active = off.active;
    }

    toggleNan(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');
        on.active = !on.active;
        off.active = !off.active;

        this.isNan = !this.isNan;
    }

    private showResult() {
        var nodeResult: Node;
        if (this.lastLocationIDWin === 1) {
            nodeResult = this.nodeXiu;
        } else if (this.lastLocationIDWin === 2) {
            nodeResult = this.nodeTai;
        } else {
            return;
        }
        this.hideResult();
        nodeResult.getChildByName('light').active = true;
        tween(nodeResult.getChildByName('icon'))
            .repeatForever(
                tween()
                    .to(0.25, { scale: v3(1.15, 1.15, 1.15) }, { easing: "quadOut" })
                    .to(0.2, { scale: v3(0.9, 0.9, 0.9) }, { easing: "quadIn" })
                    .to(0.15, { scale: v3(1.05, 1.05, 1.05) }, { easing: "sineOut" })
                    .to(0.1, { scale: v3(1.0, 1.0, 1.0) }, { easing: "sineInOut" })
            )
            .start();

        this.lblRemainWaiting.node.parent.active = true;
        this.lblRemainWaiting.node.active = false;
        this.lblSumDices.string = this.lastScore.toString();
        this.labelTextMD5.string = this.plainTextMD5;
    }

    private hideResult() {
        this.lblRemainWaiting.node.parent.active = false;
        Tween.stopAllByTarget(this.nodeTai.getChildByName('icon'));
        Tween.stopAllByTarget(this.nodeXiu.getChildByName('icon'));
        this.nodeTai.getChildByName('light').active = false;
        this.nodeXiu.getChildByName('light').active = false;
        this.nodeTai.getChildByName('icon').scale = v3(1, 1, 1);
        this.nodeXiu.getChildByName('icon').scale = v3(1, 1, 1);
    }

    private showToast(message: string) {
        this.lblToast.string = message;
        let parent = this.lblToast.node.parent;
        let uiOpacity = parent.getComponent(UIOpacity);
        if (!uiOpacity) {
            uiOpacity = parent.addComponent(UIOpacity);
        }
        Tween.stopAllByTarget(parent);
        parent.active = true;
        uiOpacity.opacity = 0;

        tween(uiOpacity)
            .to(0.1, { opacity: 255 })
            .delay(2)
            .to(0.2, { opacity: 0 })
            .call(() => {
                parent.active = false;
            })
            .start();
    }

    actPopupEventSoiCau() {
        if (this.popupSoiCau == null) {
            this.popupSoiCau = instantiate(this.popupEventSoiCauPrefab).getComponent("PopupSoiCauMD5");
            this.popupSoiCau.node.parent = this.popupContainer;
            this.popupSoiCau.show();
            App.instance.showLoading(false);
        } else {
            this.popupSoiCau.show();
        }
    }

    actPopupDetailSession(session: number) {
        if (this.popupDetailSession == null) {
            this.popupDetailSession = instantiate(this.popupDetailSessionPrefab).getComponent("PopupSessionMD5");
            this.popupDetailSession.node.parent = this.popupContainer;
            this.popupDetailSession.showDetail(session, this.detailSessions);
            App.instance.showLoading(false);
        } else {
            this.popupDetailSession.showDetail(session, this.detailSessions);
        }
    }

    actPopupHistory() {
        if (this.popupHistory == null) {
            this.popupHistory = instantiate(this.popupHistoryPrefab).getComponent("PopupHistoryMD5");
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.show();
            App.instance.showLoading(false);
        } else {
            this.popupHistory.show();
        }
    }

    actPopupHonors() {
        if (this.popupHonors == null) {
            this.popupHonors = instantiate(this.popupHonorsPrefab).getComponent("PopupHonorsMD5");
            this.popupHonors.node.parent = this.popupContainer;
            this.popupHonors.showDetail(this.isBetTypeGold);
            App.instance.showLoading(false);
        } else {
            this.popupHonors.showDetail(this.isBetTypeGold);
        }
    }

    actPopupGuide() {
        if (this.popupGuide == null) {
            this.popupGuide = instantiate(this.popupGuidePrefab).getComponent("Dialog");
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
            App.instance.showLoading(false);
        } else {
            this.popupGuide.show();
        }
    }

    actGoToTaiXiuMD5Live() {
        App.instance.openGame(Config.InGameIds.TaiXiuMD5Live);
    }
}