import { _decorator, Node, ScrollView, instantiate, Label, Color, Toggle } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
const {ccclass, property, menu} = _decorator;

@ccclass('PopupHistoryMD5')
@menu("MD5/PopupHistory")
export class PopupHistoryMD5 extends Dialog {
    @property(Node)
    prefab: Node | null = null;
    @property(ScrollView)
    scroll: ScrollView | null = null;
    private isGold = true;
    private gameID = 123;
    private page: number = 1;
    private per_page: number = 10;

    show() {
        this.scroll.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        super.show();
    }

    dismiss() {
        super.dismiss();
        this.scroll.content.removeAllChildren();
    }

    _onShowed() {
        super._onShowed();

        this.page = 1;
        this.loadData();
    }

    onChangeType(toggle: Toggle, data: number) {
        const target = toggle.node;
        if (data == 1) {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('xu').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        } else {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('tipzo').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        }
        if (toggle.isChecked === false) return;
        if (data == 1 && this.isGold === true) return;
        if (data == 2 && this.isGold === false) return;
        this.scroll.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        this.scroll.content.removeAllChildren();
        this.isGold = !this.isGold;
        this.page = 1;
        this.loadData();
    }

    private loadData() {
        App.instance.showLoading(true);

        const totalCount = this.per_page * this.page;
        const params = {
            currencyID: Configs.Login.CurrencyID,
            gameID: this.gameID,
            betType: this.isGold ? 1 : 2,
            topCount: totalCount,
        }

        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceMd5GetAccountHistory'], params, (status, res) => {
            if (status === 200) {
                var data = res.d;
                if (totalCount > data.length) {
                    this.scroll.node.off('scroll-to-bottom', this.onScrollToBottom, this);
                }
                for (var i = this.page - 1; i < data.length; i++) {
                    var node = instantiate(this.prefab);
                    const bg1 = node.getChildByName("bg1");
                    const bg2 = node.getChildByName("bg2");

                    if (bg1 && bg2) {
                        bg1.active = i % 2 === 0;
                        bg2.active = i % 2 !== 0;
                    }
                    this.setItemData(node.getChildByName('content'), data[i]);
                    node.active = true;
                    this.scroll.content.addChild(node);
                }
            }

            App.instance.showLoading(false);
        });
    }

    setItemData(item: Node, data: any) {
        item.getChildByName("lblSession").getComponent(Label).string = "#" + data.gameSessionID;
        item.getChildByName("lblTime").getComponent(Label).string = Utils.formatDatetime(data.startTime, 'dd/MM/yyyy HH:mm:ss');
        item.getChildByName("lblBetDoor").getComponent(Label).string = data.locationName == 1 ? App.instance.getTextLang("tx44") : App.instance.getTextLang("tx43");
        item.getChildByName("lblResult").getComponent(Label).string = data.result;
        item.getChildByName("lblBet").getComponent(Label).string = Utils.formatNumber(data.totalBetValue);
        item.getChildByName("lblRefund").getComponent(Label).string = Utils.formatNumber(data.refundValue);
        item.getChildByName("lblWin").getComponent(Label).string = Utils.formatNumber(data.prizeValue);
    }

    onScrollToBottom() {
        this.page++;
        this.loadData();
    }
}