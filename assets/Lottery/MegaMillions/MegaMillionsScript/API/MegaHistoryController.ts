import {
    _decorator,
    Component,
    Node,
    Prefab,
    Label,
    Color,
    instantiate,
    CCInteger,
} from 'cc';

import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const { ccclass, property } = _decorator;

@ccclass('MegaHistoryController')
export default class MegaHistoryController extends Component {

    @property(Prefab) detailHistory: Prefab = null!;
    @property(Node)   popupContainer: Node = null!;

    @property(Node) listHistory: Node = null!;
    @property(Node) itemHistory: Node = null!;

    @property(Node) prevPage: Node = null!;
    @property(Node) nextPage: Node = null!;

    @property({ type: CCInteger }) ticketType: number = 2;

    @property(Node) containerPage: Node = null!;
    @property(Node) listPage: Node = null!;
    @property(Node) activePage: Node = null!;
    @property(Node) inactivePage: Node = null!;

    private maxPage = 0;
    private currentPage = 1;

    start() {
        this.listHistory.removeAllChildren();
        this.listPage.removeAllChildren();

        Http.get(
            Configs.App.DOMAIN_CONFIG['PowerBallGetAccountsHistory'],
            { TicketType: this.ticketType, Page: this.currentPage, PageSize: 61, CurrencyID: Configs.Login.CurrencyID },
            (_status: number, res: any) => {
                if (res.c < 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                    return;
                }

                this.maxPage = Math.ceil(res.d.length / 6);
                this.containerPage.active = this.maxPage !== 0;
                if (this.maxPage === 0) return;

                this.refreshPageList();
            }
        );
    }

    private createPageNode(pageNumber: number): Node {
        const isActive = this.currentPage === pageNumber;
        const pageTemplate = isActive ? this.activePage : this.inactivePage;
        const pageNode = instantiate(pageTemplate);

        const lbl = pageNode.getComponentInChildren(Label)!;
        if (lbl) lbl.string = pageNumber.toString();

        pageNode.on(Node.EventType.TOUCH_END, () => {
            this.currentPage = pageNumber;
            this.loadData();
        });

        return pageNode;
    }

    private refreshPageList(): void {
        this.listPage.removeAllChildren();
        for (let i = 1; i <= this.maxPage; i++) {
            const pageNode = this.createPageNode(i);
            this.listPage.addChild(pageNode);
        }
    }

    loadData() {
        App.instance.showLoading(true);

        Http.get(
            Configs.App.DOMAIN_CONFIG['PowerBallGetAccountsHistory'],
            { TicketType: this.ticketType, Page: this.currentPage, PageSize: 6, CurrencyID: Configs.Login.CurrencyID },
            (_status: number, res: any) => {
                App.instance.showLoading(false);

                if (res.c < 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                    return;
                }

                this.refreshPageList();
                const data = res.d as any[];

                if (this.currentPage === 1) {
                    this.prevPage.active = false;
                }

                if (data.length === 0) {
                    if (this.currentPage > 1) this.currentPage--;
                    return;
                }

                this.prevPage.active = this.currentPage > 1;
                this.nextPage.active = this.currentPage < this.maxPage;

                this.listHistory.removeAllChildren();

                for (let i = 0; i < data.length; i++) {
                    const itemNode = instantiate(this.itemHistory);
                    const item = data[i];
                    const contentNode = itemNode.getChildByName('content')!;

                    const type = item.betType == 1 ? App.instance.getTextLang('txt_bo_so') : `BAO ${item.betType}`;
                    // console.log(item);
                    contentNode.getChildByName('lblMaVe')!.getComponent(Label)!.string = `#${item.betAccountID}`;
                    contentNode.getChildByName('lblNgayMua')!.getComponent(Label)!.string =
                        (new Date(item.createdTime)).toLocaleString("en-GB").replace(',', '');
                    // contentNode.getChildByName('lblKyQuayThuong')!.getComponent(Label)!.string =
                    //     Utils.formatDatetime88(item.dateAward);
                    contentNode.getChildByName('lblKyQuayThuong')!.getComponent(Label)!.string =
                        `#${item.gameSessionID}`;
                    contentNode.getChildByName('lblLoaiVe')!.getComponent(Label)!.string = type;
                    const lblGiaVe = contentNode.getChildByName('lblGiaVe')!.getComponent(Label)!;
                    if (item.betValue === 0) {
                        lblGiaVe.string = App.instance.getTextLang('txt_free');
                    }else {
                        lblGiaVe.string = Utils.formatNumber(item.betValue);
                    }

                    // contentNode.getChildByName('lblGiaVe')!.getComponent(Label)!.string =
                    //     Utils.formatNumber(item.betValue);

                    const dateAward = new Date(item.dateAward);
                    const now = new Date();

                    let displayValue = '-';

                    if (dateAward < now) {
                        displayValue = item.prizeValue > 0 ? Utils.formatNumber(item.prizeValue) : '0';
                    }

                    contentNode.getChildByName('lblTongThang')!.getComponent(Label)!.string = displayValue;

                    // contentNode.getChildByName('lblTongThang')!.getComponent(Label)!.string =
                    //     item.prizeValue > 0 ? Utils.formatNumber(item.prizeValue) : '-';

                    // numbers
                    const numbers: string[] = (item.ticketData as string).split(',');
                    const nodeNumbers = contentNode.getChildByName('lblSoDuThuong')!;
                    const template = nodeNumbers.getChildByName('number')!;
                    nodeNumbers.removeAllChildren();

                    const detailNumbers: string[] = [];
                    for (let j = 0; j < numbers.length; j++) {
                        const numberNode = instantiate(template);
                        const val = parseInt(numbers[j]);
                        const detailNumber = val < 10 ? '0' + val : String(val);
                        detailNumbers.push(detailNumber);
                        numberNode.getComponent(Label)!.string = detailNumber;
                        nodeNumbers.addChild(numberNode);
                    }

                    // special ball
                    const numberSpecialNode = instantiate(template);
                    const ballVal = parseInt(item.ball);
                    const detailNumberSpecial = ballVal < 10 ? '0' + ballVal : String(ballVal);
                    const ballLbl = numberSpecialNode.getComponent(Label)!;
                    ballLbl.string = detailNumberSpecial;
                    ballLbl.color = new Color().fromHEX(this.ticketType === 1 ? '#cf0e2e' : '#fff600');
                    nodeNumbers.addChild(numberSpecialNode);

                    if (item.isAuto === 1) {
                        const tcNode = instantiate(template);
                        const tcLabel = tcNode.getComponent(Label)!;
                        tcLabel.string = "TC";
                        tcLabel.color = new Color("#FFFFFF");
                        nodeNumbers.addChild(tcNode);
                    }

                    itemNode.on(Node.EventType.TOUCH_END, () => {
                        this.actShowDetailHistory(
                            item.betAccountID,
                            item.gameSessionID,
                            item.createdTime,
                            item.dateAward,
                            type,
                            item.betValue,
                            item.prizeValue,
                            detailNumbers,
                            detailNumberSpecial,
                            item.isAuto
                        );
                    });

                    this.listHistory.addChild(itemNode);
                }
            }
        );
    }

    actShowDetailHistory(
        betAccountID: number,
        sessionID: number,
        createdTime: string,
        dateAward: string,
        type: string,
        betValue: number,
        prizeValue: number,
        numbers: string[],
        ball: string,
        isAuto: number
    ) {
        const detailHistoryPopup = instantiate(this.detailHistory);
        this.popupContainer.addChild(detailHistoryPopup);

        // Ép kiểu để TS nhận ra showDetail (tránh TS2339)
        const cmp = detailHistoryPopup.getComponent('MegaPopupDetailHistory') as unknown as {
            showDetail: (
                betAccountID: number,
                sessionID: number,
                createdTime: string,
                dateAward: string,
                type: string,
                betValue: number,
                prizeValue: number,
                numbers: string[],
                ball: string,
                isPowerBall: boolean,
                isAuto: number
            ) => void
        };

        cmp?.showDetail(
            betAccountID,
            sessionID,
            createdTime,
            dateAward,
            type,
            betValue,
            prizeValue,
            numbers,
            ball,
            this.ticketType === 1,
            isAuto
        );
    }

    onBtnPrevClick() {
        this.currentPage = Math.max(1, this.currentPage - 1);
        this.loadData();
    }

    onBtnNextClick() {
        this.currentPage = Math.min(this.maxPage, this.currentPage + 1);
        this.loadData();
    }
}
