[{"__type__": "cc.Prefab", "_name": "MiniPoker", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "MiniPoker", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 1154}, {"__id__": 86}, {"__id__": 1156}, {"__id__": 1158}], "_prefab": {"__id__": 1160}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Box", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 438}, {"__id__": 540}], "_active": true, "_components": [{"__id__": 1149}, {"__id__": 1151}], "_prefab": {"__id__": 1153}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "GamePlay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}, {"__id__": 12}, {"__id__": 36}, {"__id__": 47}, {"__id__": 57}, {"__id__": 75}, {"__id__": 963}, {"__id__": 88}, {"__id__": 498}, {"__id__": 519}, {"__id__": 197}, {"__id__": 216}, {"__id__": 985}, {"__id__": 1022}, {"__id__": 1033}, {"__id__": 1044}, {"__id__": 1055}, {"__id__": 410}, {"__id__": 1064}, {"__id__": 430}, {"__id__": 423}, {"__id__": 445}, {"__id__": 472}, {"__id__": 1072}, {"__id__": 1088}, {"__id__": 353}, {"__id__": 1131}, {"__id__": 234}, {"__id__": 344}], "_active": true, "_components": [{"__id__": 1142}, {"__id__": 1144}, {"__id__": 1146}], "_prefab": {"__id__": 1148}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.325, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "628c89ec-2c48-4082-9c14-0dcc0a1552cc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecd+AJWSRJ35UqZ+8TWIxb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fQNnyJmZKhIPbFedyn1SL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 918, "height": 413}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eLiivJcRBzJwZpp1FogS2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8cUkW/CO9FEqMKxnPBfEMx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnSpinAnim", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 13}, {"__id__": 21}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 35}, "_lpos": {"__type__": "cc.Vec3", "x": 336.959, "y": 95.921, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnSpin0", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "191e6865-c764-44a4-92b2-01068091a363@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "962QHEdsZKwpN7GZrzh/5s"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54bpr6BJZE6oITt56HPCWC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 146}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fJP7iNIhJg52uC3AvrGg0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5eYrCA5/hEcqPKZ0loZAsQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnSpin1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": false, "_components": [{"__id__": 22}, {"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -130, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbd2aea2-a034-4ab6-8b41-08950625d2cd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c2gb/MJZFf4dEFUW1fJR5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 25}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bvcxmopRCN7deT3BNlLJi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 27}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53pDfyxLpHBqUldXpaK0Qb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecR5La979JcJg0hvqbHFf9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 30}, "playOnLoad": false, "_clips": [{"__uuid__": "f3df8e61-4b84-4561-a160-1f160b30c3b4", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "f3df8e61-4b84-4561-a160-1f160b30c3b4", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ev0YKQg1EKIY6E4PApiHh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 32}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7z+lJoedNjaot6K9kBnWH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 34}, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 293}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edVGgE8BRJYJzyUsCAkboQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17Ft2dxX1Mgbw3kF/M1HJu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 429.787, "y": 7.113, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "708eeba2-e026-4a79-a64c-4bf625c66976@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31Ig+Y9l9A5r0cto7EuU2Z"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 40}, "clickEvents": [{"__id__": 41}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd8z/i+KVKY55ZHBliv9/0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 43}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dx+JcYdlEEpKUVUZ8OWS6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 45}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "465Cc/r1VNA5jo9bT+SJPN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8bUU31UV1Fdakjcdsiam7l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tut2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": false, "_components": [{"__id__": 48}, {"__id__": 50}, {"__id__": 52}, {"__id__": 54}], "_prefab": {"__id__": 56}, "_lpos": {"__type__": "cc.Vec3", "x": 337.1669999999999, "y": 31.476, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 49}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5b9a7965-5543-4ad5-b5a3-0e5954b7b866@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99YLeInNhECK+7q7zzAybu"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 51}, "playOnLoad": true, "_clips": [{"__uuid__": "1aef1808-e32b-4ce6-8fa5-01a84ae1aa3c", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "1aef1808-e32b-4ce6-8fa5-01a84ae1aa3c", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6su1/S2xJC4hCUcs3eBGg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 53}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ff9HldcdDTqxqzv0TmAx9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79XiBgV2FDgbs7t/Hn7opE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82HQZRMCVOPq40iAAndDTY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 58}], "_active": true, "_components": [{"__id__": 68}, {"__id__": 70}, {"__id__": 72}], "_prefab": {"__id__": 74}, "_lpos": {"__type__": "cc.Vec3", "x": 337.167, "y": 11.578, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "tut2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 57}, "_children": [], "_active": true, "_components": [{"__id__": 59}, {"__id__": 61}, {"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 60}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5b9a7965-5543-4ad5-b5a3-0e5954b7b866@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bF1fSlhRFaahYXpYpFr8L"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 62}, "playOnLoad": true, "_clips": [{"__uuid__": "1aef1808-e32b-4ce6-8fa5-01a84ae1aa3c", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "1aef1808-e32b-4ce6-8fa5-01a84ae1aa3c", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fc1TqSY3JPRpgDrTU+LFRp"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 64}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aRx6PvWtOnqZtdPq9KDUA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 66}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77vdM+FTBHpbGjBKMBszy4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1UlYqHQNKjIy5D3g/wJQ1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 150}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c20KB93W9Bbq/JVEkDGCvu"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 71}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8euozOJaBKDK//f1+f/8wS"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 57}, "_enabled": true, "__prefab": {"__id__": 73}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68ArsdFAhGxaqWg7KrjcdX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42tr4UD5xBBaXu1hu7m6Hl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "itemTemplate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 76}], "_active": false, "_components": [{"__id__": 84}, {"__id__": 980}, {"__id__": 982}], "_prefab": {"__id__": 984}, "_lpos": {"__type__": "cc.Vec3", "x": -759.404, "y": 307.281, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 75}, "_children": [], "_active": true, "_components": [{"__id__": 77}, {"__id__": 79}, {"__id__": 81}], "_prefab": {"__id__": 83}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0a2e8465-e2d9-4229-b6d6-bfb0d99db24b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abhiugO0FPOJYt8KAHmlpP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 80}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8762tCqNRHp6Svcikygjqz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 76}, "_enabled": true, "__prefab": {"__id__": 82}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65T76V5yFK/ZouBifKBlpF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25FUy9hpBORqUzKQgR8cUc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "652e5EeFspG2L/rQsaBQYH5", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 85}, "mini": {"__id__": 86}, "sprite": {"__id__": 77}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1frKsPU1tDi7D3iQ06LTaA"}, {"__type__": "93f55F36+ZG/4x1s8HLfpyR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 87}, "gamePlay": {"__id__": 3}, "nameGame": "MiniPoker", "bettingNode": {"__id__": 88}, "nodeGold": [{"__id__": 196}, {"__id__": 215}], "nodeResulfReward": {"__id__": 234}, "nodejacport": {"__id__": 344}, "goldTicketclone": {"__id__": 352}, "lblGoldTicket": {"__id__": 355}, "lblJackpot": {"__id__": 408}, "lblPhein": {"__id__": 422}, "btnSpinAnim": {"__id__": 29}, "lblWinCash": {"__id__": 430}, "popupContainer": {"__id__": 438}, "x1": {"__id__": 444}, "x2": {"__id__": 471}, "popups": [], "nodeAuto": {"__id__": 498}, "nodeStop": {"__id__": 519}, "CardNode": {"__id__": 540}, "slotLayer": {"__id__": 962}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bEZwk0uFPm5Fjpt1x9TYX"}, {"__type__": "cc.Node", "_name": "betting", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 89}, {"__id__": 123}, {"__id__": 157}], "_active": true, "_components": [{"__id__": 191}, {"__id__": 193}], "_prefab": {"__id__": 195}, "_lpos": {"__type__": "cc.Vec3", "x": -3, "y": -9.621, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnBet10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [{"__id__": 90}, {"__id__": 98}], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": -161.031, "y": -129.421, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 89}, "_children": [], "_active": true, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 95}], "_prefab": {"__id__": 97}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 92}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1e0rUg8ftKpLLlH3+Yuog0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 94}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6bBNC+5xIBJm56O0qORSY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 55.078125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bgozaYitKRbiFqDIf5CcG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "308tZV8RdAloE8s+fKGWck", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 89}, "_children": [{"__id__": 99}], "_active": true, "_components": [{"__id__": 107}, {"__id__": 109}, {"__id__": 111}], "_prefab": {"__id__": 113}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 98}, "_children": [], "_active": true, "_components": [{"__id__": 100}, {"__id__": 102}, {"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 101}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 130, "g": 31, "b": 0, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7105Z9EM9AoaAMIDx9V11O"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 103}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8b5lo71wFJRYVdw/YCmpvI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 99}, "_enabled": true, "__prefab": {"__id__": 105}, "_contentSize": {"__type__": "cc.Size", "width": 55.078125, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fWKmG55hG4Z8TXeJQ+VSJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94gir6gPZPxoZSEM3lyy2U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 108}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0832870c-03df-4cec-a2c7-9ce25fa64acd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42lQ+yyMtAY732w8arVNsQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 110}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88OHE6IpFOAJ01Eo7gjSEx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25LGjD82FPA5IKniNqvc5L"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80M1HJdalL8LpRy3p0ck98", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 115}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bdd195e7-8ab5-4bb0-83b4-abcde1720cfd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8ZQbZnNNKCbHjRyaF9UTK"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 117}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcVBG9kk5PlYD3VE9nrRSa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 119}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c91DYhAnREyLqyFhTd5+iX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eEYl4Qk9OhruwfGQuqg9s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eEHo1nFBEk4v24mu50XbV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnBet100", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [{"__id__": 124}, {"__id__": 132}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}], "_prefab": {"__id__": 156}, "_lpos": {"__type__": "cc.Vec3", "x": -11.698, "y": -130.108, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 127}, {"__id__": 129}], "_prefab": {"__id__": 131}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 126}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1.000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92g+SrZ4hMW5zZArSPUegw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 128}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef9zNLAJlN4b/qt4U8qhIU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 130}, "_contentSize": {"__type__": "cc.Size", "width": 82.75, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acLGXsi6JJWofeDNPadHkv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73IjKauD5BR6JDZwxV3Mg6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Sprite copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [{"__id__": 133}], "_active": true, "_components": [{"__id__": 141}, {"__id__": 143}, {"__id__": 145}], "_prefab": {"__id__": 147}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 132}, "_children": [], "_active": true, "_components": [{"__id__": 134}, {"__id__": 136}, {"__id__": 138}], "_prefab": {"__id__": 140}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 135}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 130, "g": 31, "b": 0, "a": 255}, "_string": "1.000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4C9/QJsBEjLRQo6AcGX6V"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 137}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6DhJQPI9ICZSn59FetgCI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 139}, "_contentSize": {"__type__": "cc.Size", "width": 82.75, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61hMMmp11JK6ivOB7RZoyU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9KwUnOzhLZoANVBRg3WEs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 142}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0832870c-03df-4cec-a2c7-9ce25fa64acd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eHDw3ZOxIlJoefJQo9WJb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 144}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "930pxq+y5BN6FHGlKDXuE6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 132}, "_enabled": true, "__prefab": {"__id__": 146}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecIjilhVpLa7+mXsRhQkTr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2e/k1lOVdDN6Pmu9i/N4uB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bdd195e7-8ab5-4bb0-83b4-abcde1720cfd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "961IAWZ3hK4ateo4GBjqaw"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 151}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd1F01cNlGyLpofDPa2baK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 153}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1rL46eN9Da5KDBveO4OVt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbeEeBq9pN7bWoyGKHzrUE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3m7MkhldA0qU1AOp78Ugk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnBet1000", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 88}, "_children": [{"__id__": 158}, {"__id__": 166}], "_active": true, "_components": [{"__id__": 182}, {"__id__": 184}, {"__id__": 186}, {"__id__": 188}], "_prefab": {"__id__": 190}, "_lpos": {"__type__": "cc.Vec3", "x": 136.644, "y": -130.108, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 157}, "_children": [], "_active": true, "_components": [{"__id__": 159}, {"__id__": 161}, {"__id__": 163}], "_prefab": {"__id__": 165}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 160}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "10.000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6gbYeiPJMe4gc+gPg/Umb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 162}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7b1+YHrwBPF65RnSxHp+nR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 164}, "_contentSize": {"__type__": "cc.Size", "width": 101.109375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bf5h8L+u1MNYiUTzU48JFc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46iCzQqupP84dAYvfx2ZmI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Sprite copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 157}, "_children": [{"__id__": 167}], "_active": true, "_components": [{"__id__": 175}, {"__id__": 177}, {"__id__": 179}], "_prefab": {"__id__": 181}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 166}, "_children": [], "_active": true, "_components": [{"__id__": 168}, {"__id__": 170}, {"__id__": 172}], "_prefab": {"__id__": 174}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 169}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 130, "g": 31, "b": 0, "a": 255}, "_string": "10.000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eDOdc1LlL7aK1fUCEHfA9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 171}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "810x8LBxxDa5tJD4me/uiz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 173}, "_contentSize": {"__type__": "cc.Size", "width": 101.109375, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fB/PnGK5CBK3lByMOkZps"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3odxnHQ5AL7bhTR2e5jIG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0832870c-03df-4cec-a2c7-9ce25fa64acd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecYLof0dVK8JMfceOTuSEs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 178}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbAh5H8xNJvZUta6IiwN5V"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 180}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7rB3du9ZCMahAizxIrgh4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8LAcCIZ5JiqXaS7B1BKCX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 183}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bdd195e7-8ab5-4bb0-83b4-abcde1720cfd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "651dT7NOZKKbaX1UnLw1HN"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 185}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1hTe82UVMHpCsVcWzy5ov"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 187}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65SAd5OzVOFKh+U2rqFNkT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 189}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0j4VwNpVAO73kkFUYVuQy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eWbQKsr9JzaOoo8WQRR15", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 192}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63dJt8rldONaiCao1OMVIp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 194}, "_contentSize": {"__type__": "cc.Size", "width": 895, "height": 260}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25Zd7vMntK4at8RvzF4k//"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2do01WzsxPtqwv94t4SqbS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "gold", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 197}, "_children": [], "_active": true, "_components": [{"__id__": 208}, {"__id__": 210}, {"__id__": 212}], "_prefab": {"__id__": 214}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "coin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 196}], "_active": true, "_components": [{"__id__": 198}, {"__id__": 200}, {"__id__": 203}, {"__id__": 205}], "_prefab": {"__id__": 207}, "_lpos": {"__type__": "cc.Vec3", "x": -287.437, "y": -118.098, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-4ec2-b522-08f9c1cfb801@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aapKIGxPFL1IazOvWXvVHA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 201}, "clickEvents": [{"__id__": 202}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67VW0j5G9EvqKT+U72QUmx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickCoin", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 204}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5+cN/ZDFDz6JiRd59fDnx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 206}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61QMIqGrhOzZAEE6DV1Npf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2/044K35Na41B8m8SWYxy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 209}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7ba86d39-0c2d-47ac-844a-77aca0a32adb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83Q0Legu5MX7JkSFJr4RCW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 211}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f01BR0lN5KC6DxtpUy9iQS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 213}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72JY3TjApAXLqvvqTCW5Lh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eftRCyAoNAAphWT5J9GvKz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "gold", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 216}, "_children": [], "_active": true, "_components": [{"__id__": 227}, {"__id__": 229}, {"__id__": 231}], "_prefab": {"__id__": 233}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "coin copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 215}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}, {"__id__": 222}, {"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 142.916, "y": 143.008, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 218}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-4ec2-b522-08f9c1cfb801@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ahfew9y9NKI6iADFyLxou"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 220}, "clickEvents": [{"__id__": 221}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39IFEjDf9Bj5ScRCK974A2"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickCoin", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 223}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b21YyHoeNJ9qLM8sJAueoA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 225}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8c7kg3kQtGTr+MFP8vVSWI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a71QyCHBZLw6R91rEY8mKL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 228}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7ba86d39-0c2d-47ac-844a-77aca0a32adb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceC4m7kGNI0YbYJNFh8Hi5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 230}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68ZuJCntNAA5HgIum6+KRp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 232}, "_contentSize": {"__type__": "cc.Size", "width": 47, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fft/7B+dOpIbwcg42RJvA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ec+XtC+7pN5pk5WD4CrFL9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "resulf", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 235}, {"__id__": 245}, {"__id__": 255}, {"__id__": 267}, {"__id__": 279}, {"__id__": 291}, {"__id__": 303}, {"__id__": 315}, {"__id__": 327}], "_active": true, "_components": [{"__id__": 339}, {"__id__": 341}], "_prefab": {"__id__": 343}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mp-jackpot-en", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 242}], "_prefab": {"__id__": 244}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 237}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "38a73b7b-b118-4c0d-a46a-61d6c8ef08e9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2tFNeaChOELa6CAYi8cBl"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 239}, "viet": {"__uuid__": "7c27f649-0b5d-43da-8f4a-736b12f13a8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "38a73b7b-b118-4c0d-a46a-61d6c8ef08e9@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "cfb1f920-b212-49cc-b3d5-45f8edd5daea@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "38a73b7b-b118-4c0d-a46a-61d6c8ef08e9@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "38a73b7b-b118-4c0d-a46a-61d6c8ef08e9@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "38a73b7b-b118-4c0d-a46a-61d6c8ef08e9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25uuZJ0P5FIZDCz2+zs0P2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 241}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93qTaMkllBIIcKD9V2LwLm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 235}, "_enabled": true, "__prefab": {"__id__": 243}, "_contentSize": {"__type__": "cc.Size", "width": 363, "height": 104}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c36ZqzGu9F8oVgIq/RUA6V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dCxEqbLJO3JEjtCqZIAs5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "mp-straighflush-th", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 246}, {"__id__": 248}, {"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 247}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0aaee497-44db-46d3-b5ab-d173ecbc0621@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4Cfvx4E1NzKxXmmJjS3HX"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 249}, "viet": {"__uuid__": "0868b5fd-de00-4ca8-b9d7-2fbe8126c961@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "7b619d6b-0336-48ee-886f-c6c92f88641a@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "0aaee497-44db-46d3-b5ab-d173ecbc0621@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "7b619d6b-0336-48ee-886f-c6c92f88641a@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "817b34e8-c35e-4982-a3e7-18e4ede6c64c@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "7b619d6b-0336-48ee-886f-c6c92f88641a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2XOrpLTxDgoWZ1OlswqR2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 251}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efMhzwripMyb/Fqp8moo5o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 253}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bU1rHjDVP0ZrBuNjg78ms"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a51IuVT2FL2ZXvjHSCBU2j", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "tu auy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 256}, {"__id__": 258}, {"__id__": 260}, {"__id__": 262}, {"__id__": 264}], "_prefab": {"__id__": 266}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 257}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "cd7a7d60-7aa0-48d5-a7e6-30ec9ec53d62", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbWSUQqR5M/IHaJJbzvUuv"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 259}, "id": "ca20", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51Av1bR+1MuKZgC+2S3zsS"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 261}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ferAAGtyFERpFo4Mpl6Pgl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 263}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfdrv41mtCeY3XqOs0lWWU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 126.509765625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26tk9fUyxCYbVbNdyYF/x3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "97GPPo2WlIN6K6m88kdL2Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "culu", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 268}, {"__id__": 270}, {"__id__": 272}, {"__id__": 274}, {"__id__": 276}], "_prefab": {"__id__": 278}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 269}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32F6OY499KNoISPllF3eSt"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 271}, "id": "ca19", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3vEU8Hl1IwY9fd4xnJw0u"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 273}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21ba4BJa5OVb22MyVAv9F+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 275}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6ymVTTztL1LYVtoPuNgzA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60UsUx3LBAGL7IegtDxS/f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05cDjqVWhCUI2rjRsw+ZxG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "thung", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 280}, {"__id__": 282}, {"__id__": 284}, {"__id__": 286}, {"__id__": 288}], "_prefab": {"__id__": 290}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 281}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7404kmeBCf72+/qCpa1kP"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 283}, "id": "ca18", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39Gn0y2tdElqM4105YlQRB"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 285}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97B+jn2H1I4piQhKZ6AZkA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 287}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fU7mxwYVI26iFUOoAmeyI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 289}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbrfmeFdxKo7WKpY3tE/Tt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8ng1B7WlGIK9JEfGi4nv8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sanh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 292}, {"__id__": 294}, {"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "_prefab": {"__id__": 302}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 293}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2fPaZFixHAqcWy8eEUrGX"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 295}, "id": "ca17", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57WJvG0hhCuZuN2bkoCl6M"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 297}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13Xa7RIjVJRKc0Dh96BdJ3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 299}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eXWcVqwVHnYEVGB/YIjES"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 291}, "_enabled": true, "__prefab": {"__id__": 301}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67oa7M7HRNdoV1mQe00IJu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1Zum/LCVJvrYnEbrCAf2H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sam", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 304}, {"__id__": 306}, {"__id__": 308}, {"__id__": 310}, {"__id__": 312}], "_prefab": {"__id__": 314}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 305}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9xgytzOJDh4uaEg3YYP3q"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 307}, "id": "ca16", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bJTbfgmdBTpxU88RgEP6B"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 309}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4C87wu5pNJqOLaGeLRYFk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 311}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aCWrWfHhKeaxUFQ1WTfzD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 313}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dRviWfI1H5rZQ/EM9PqRO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56P3KPgYxPlLWpsh6PGsVA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "haidoi", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 316}, {"__id__": 318}, {"__id__": 320}, {"__id__": 322}, {"__id__": 324}], "_prefab": {"__id__": 326}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 317}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfcHjy2S1AYZHX5vrtjrmc"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 319}, "id": "ca27", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dmTu7o7RHt73VU27ci4i/"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 321}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdhjH6O5FHV6lJ+kHAG7LB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 323}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67zs+RS1ZOx4sySidahhYZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 325}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dXb30WORMxohuvxaWYA4q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53Uz8xeu5KGY9HAlcVj/tV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "doi j", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 234}, "_children": [], "_active": false, "_components": [{"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}, {"__id__": 336}], "_prefab": {"__id__": 338}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 329}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 254, "g": 190, "b": 37, "a": 255}, "_string": "Label", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 234, "g": 115, "b": 20, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76lB2uaBRNhbeJ/4isB+Up"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 331}, "id": "mnpk9", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddwFDr+itH8JNPq8B7ux7K"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 333}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acgdzp+0BEQbL5K0GB6t1C"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 335}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29rremUOREOKFh9gdgfk8O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 113.0087890625, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3dkHo2D9BBI2UEfzABu32"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3b0vYtgwJHNohYndMUm6Vr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 340}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02DkoXjQ9KUrimmK+N9mXe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 342}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9atvqk+PhN6pfVCTCd58/N"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dzdStF1lGILvXtjDJtCmx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "effect", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": false, "_components": [{"__id__": 345}, {"__id__": 347}, {"__id__": 349}], "_prefab": {"__id__": 351}, "_lpos": {"__type__": "cc.Vec3", "x": -2.9, "y": 692.886, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 344}, "_enabled": true, "__prefab": {"__id__": 346}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": -1, "emissionRate": 184.66666666666666, "life": 1.5, "lifeVar": 0.5, "angle": 0, "angleVar": 0, "startSize": 40, "startSizeVar": 0, "endSize": -1, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 51, "endSpin": 500, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 608, "y": 1}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -906}, "speed": 0, "speedVar": 337, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 50, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": true, "autoRemoveOnFinish": false, "_preview": false, "preview": false, "_custom": false, "_file": {"__uuid__": "4b63ef5a-fa88-4dad-af42-a5aacbde06df", "__expectedType__": "cc.ParticleAsset"}, "_spriteFrame": {"__uuid__": "027bea70-eff1-46e4-8fcd-07d6715e3011@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 277, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4zx+SpitLVoFL6UY4t3sO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 344}, "_enabled": true, "__prefab": {"__id__": 348}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aPcgah4VG0qU0bU8bT2GT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 344}, "_enabled": true, "__prefab": {"__id__": 350}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7alAnDVyFAVLzvsddh19nd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "18aHBm/7hDGbuGPmh8wOKr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lucky", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 353}, "_children": [{"__id__": 393}], "_active": false, "_components": [{"__id__": 401}, {"__id__": 403}, {"__id__": 405}], "_prefab": {"__id__": 407}, "_lpos": {"__type__": "cc.Vec3", "x": -1.298, "y": -225.622, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.5, "y": 1.5, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 354}, {"__id__": 352}, {"__id__": 362}], "_active": true, "_components": [{"__id__": 381}, {"__id__": 383}, {"__id__": 386}, {"__id__": 388}, {"__id__": 390}], "_prefab": {"__id__": 392}, "_lpos": {"__type__": "cc.Vec3", "x": 1, "y": 238.005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "vevang", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 353}, "_children": [], "_active": true, "_components": [{"__id__": 355}, {"__id__": 357}, {"__id__": 359}], "_prefab": {"__id__": 361}, "_lpos": {"__type__": "cc.Vec3", "x": 212.008, "y": -64.66, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 354}, "_enabled": true, "__prefab": {"__id__": 356}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 59, "g": 19, "b": 3, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34, "_fontSize": 33, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 2, "_enableWrapText": true, "_font": {"__uuid__": "2a454e9c-9bb5-4dc4-abe7-622841fb9753", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5aEAWH7RMsLRko0omN5YL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 354}, "_enabled": true, "__prefab": {"__id__": 358}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cjYNxEWtCDY0m8vUmA3wH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 354}, "_enabled": true, "__prefab": {"__id__": 360}, "_contentSize": {"__type__": "cc.Size", "width": 55.8, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21a82MpClIq42KmGVkI+SZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60nk69J6tMuoETK2xR0NRB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Button", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 353}, "_children": [{"__id__": 363}], "_active": true, "_components": [{"__id__": 373}, {"__id__": 376}, {"__id__": 378}], "_prefab": {"__id__": 380}, "_lpos": {"__type__": "cc.Vec3", "x": 222.452, "y": -52.878, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 362}, "_children": [], "_active": true, "_components": [{"__id__": 364}, {"__id__": 366}, {"__id__": 368}, {"__id__": 370}], "_prefab": {"__id__": 372}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 365}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fcFYg5opES7fnYzwrq2B5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 367}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fc1gC067pHgLiXVUkKD2RA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 369}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eTT+9zZlAUr0pzM1RQvM+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 371}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ae5qZi/kNOm6X3AjSBFtiz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2a8+fn7xLAqlBTLltdfPy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 374}, "clickEvents": [{"__id__": 375}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_normalSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 363}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04/zrDURtNVJxF6oXfp+br"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actMyGoldTicket", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 377}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbqWosFvpJBKk0Mrxvy1zL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 379}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bsnpeKYpBb4fqkLX81aEn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4SkUxIPhNXY9lNHNOdbu3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 382}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3498b9c3-a203-40fe-9a93-ba7bb468125a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15S4apAG5M8pOZ65M7pzrF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": false, "__prefab": {"__id__": 384}, "clickEvents": [{"__id__": 385}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2EnoKrPxNpLRKmxeRwx8E"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actGoldTicket", "customEventData": ""}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 387}, "viet": {"__uuid__": "3498b9c3-a203-40fe-9a93-ba7bb468125a@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "1c066f0a-f083-4b65-8b2d-c6df231aecd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "1c066f0a-f083-4b65-8b2d-c6df231aecd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "1c066f0a-f083-4b65-8b2d-c6df231aecd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "1c066f0a-f083-4b65-8b2d-c6df231aecd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "1c066f0a-f083-4b65-8b2d-c6df231aecd5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16VmY37ENFyqsmUI8PMaTl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 389}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cPcK6EL9LLaaSAa5GoA7c"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 353}, "_enabled": true, "__prefab": {"__id__": 391}, "_contentSize": {"__type__": "cc.Size", "width": 497, "height": 233}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eajN86VaRIza+Tc3FDNnXG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99CxfVWuVCNIl5GDppjJ0Z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lucky", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 352}, "_children": [], "_active": true, "_components": [{"__id__": 394}, {"__id__": 396}, {"__id__": 398}], "_prefab": {"__id__": 400}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 395}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 97}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2YK59DGdMUZMwBDLIrF8u"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 397}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d95308a5-7384-411e-8d80-d6628b01d9ed@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5demZ2ch1JGYCio514wGj3"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 393}, "_enabled": true, "__prefab": {"__id__": 399}, "playOnLoad": false, "_clips": [{"__uuid__": "b591d04f-f53e-4dde-b741-3ab075442022", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "b591d04f-f53e-4dde-b741-3ab075442022", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0beUsPqIlI5bE4ujm25eVO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0c3Qib2gBJ5qwdZTLIgDDs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f764f4e8-19fa-4352-9789-d7bd336b388b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84ZqLYtp1NRaBgACO2pjtR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 404}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32+PVlth9GuK4708I25wEu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 352}, "_enabled": true, "__prefab": {"__id__": 406}, "_contentSize": {"__type__": "cc.Size", "width": 100.3, "height": 118.292683}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccsul6Nr5KRZBR8ZdmC5f5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81NodBLfBHaaYGN9lBpH5m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 421}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 234, "b": 0, "a": 255}, "_string": "100", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "9a8cf213-6dd7-45a9-b876-c0246641c503", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.Node", "_name": "lblJackpot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 410}, "_children": [], "_active": true, "_components": [{"__id__": 408}, {"__id__": 416}, {"__id__": 418}], "_prefab": {"__id__": 420}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 4.405, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "jackpotBG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 409}], "_active": true, "_components": [{"__id__": 411}, {"__id__": 413}], "_prefab": {"__id__": 415}, "_lpos": {"__type__": "cc.Vec3", "x": 4.644, "y": 141.406, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 412}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6YMquTTFP/ZZ62HTb5UYR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 414}, "_contentSize": {"__type__": "cc.Size", "width": 235, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fPTbyMWlOeoiqnQ6G2Eug"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57IT23DkhFmbSvRoW0UN7i", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 417}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ce9LTSpO1BS4J3mNxysm2G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 409}, "_enabled": true, "__prefab": {"__id__": 419}, "_contentSize": {"__type__": "cc.Size", "width": 67.44140625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dsTm6HWlP15LgpTwSvTpl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1+40vroZOKpudUjt+jUtQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fmyTiBiJANYzoeH6DBOle"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 429}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25, "_fontSize": 25, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "9a8cf213-6dd7-45a9-b876-c0246641c503", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.Node", "_name": "lblWinCoin copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 422}, {"__id__": 424}, {"__id__": 426}], "_prefab": {"__id__": 428}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -95.615, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 425}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "802wnRVs9L3Ih/FoLXX8ys"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 427}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85Feib2Y1OK7tQa/D1dvXM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eeay1FwFOhZUdICADIPCu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "5erWth3TBL/YDko0vPCsCM"}, {"__type__": "cc.Node", "_name": "lblWinCoin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 431}, {"__id__": 433}, {"__id__": 435}], "_prefab": {"__id__": 437}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 114.747, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 432}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "9a8cf213-6dd7-45a9-b876-c0246641c503", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6AliWGYRHya5QvwZQGIlI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 434}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78DG2jHH1C768E7eAGLbmB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 436}, "_contentSize": {"__type__": "cc.Size", "width": 16.8603515625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fJ7KtD91OKYTErMmpdgB0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26HmKdeKVBuYmdNLOEZms8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BoxPopup", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 439}, {"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 438}, "_enabled": true, "__prefab": {"__id__": 440}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16I9OMOQVAaqyXaIjUXMnb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 438}, "_enabled": true, "__prefab": {"__id__": 442}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43Zx+5wZhPmr0Rv/NQiybd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aekOIE6lJDSrkb0MeofSVo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 445}, "_children": [], "_active": true, "_components": [{"__id__": 464}, {"__id__": 466}, {"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "un_select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 444}, {"__id__": 446}], "_active": true, "_components": [{"__id__": 454}, {"__id__": 456}, {"__id__": 459}, {"__id__": 461}], "_prefab": {"__id__": 463}, "_lpos": {"__type__": "cc.Vec3", "x": 265.245, "y": -101.066, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 445}, "_children": [], "_active": true, "_components": [{"__id__": 447}, {"__id__": 449}, {"__id__": 451}], "_prefab": {"__id__": 453}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 448}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1X", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 18, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0mCmxO6JGH7KtSAxPXfAL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 450}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfOwu6o8BOV49TwJwKwI+J"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 452}, "_contentSize": {"__type__": "cc.Size", "width": 22.0166015625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e114THgidBjazsZgdgOhKi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beYyr+grpHEbiW+PzOmJMu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 455}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "df9131c0-5d10-42ae-aa68-daaef4bca528@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0emJePo4VIKaARBSoqq9ZR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 457}, "clickEvents": [{"__id__": 458}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "086mcMbhxAgb/27Jv/t3cJ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickX1", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 460}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aarWhMdcFMCJ/rfWRZlZ9o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 462}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "822nhsvbRD3rWq7TWV0jyc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "97yob4zyxBJoEFDleb0N04", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 465}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "84182db2-c3e3-443d-be4e-2c7350f17ad5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34yccLNrpIbrWwKsapPkyr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 467}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0pBMSFHZHpYIURcfUh/wk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 469}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64bkTsc3ZGn6oT2CYrOIN5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06T/shxexIgafuMPNCRrvq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "select", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 472}, "_children": [], "_active": false, "_components": [{"__id__": 491}, {"__id__": 493}, {"__id__": 495}], "_prefab": {"__id__": 497}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "un_select copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 471}, {"__id__": 473}], "_active": true, "_components": [{"__id__": 481}, {"__id__": 483}, {"__id__": 486}, {"__id__": 488}], "_prefab": {"__id__": 490}, "_lpos": {"__type__": "cc.Vec3", "x": 326.631, "y": -101.066, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 472}, "_children": [], "_active": true, "_components": [{"__id__": 474}, {"__id__": 476}, {"__id__": 478}], "_prefab": {"__id__": 480}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 475}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "2X", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 18, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7pFjoEEZDQI13gcgODY49"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 477}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4l8BwszFIeLlccbWbdRbl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 479}, "_contentSize": {"__type__": "cc.Size", "width": 22.0166015625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4E0KxTM5D56V99nS5/3M3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99mxzbpwRPy5HPgFSILMLv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 482}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "df9131c0-5d10-42ae-aa68-daaef4bca528@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0b5L9k/btBW4nFzkkkLnKC"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 484}, "clickEvents": [{"__id__": 485}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fRDJK2IlB9ZnSuvDtiRPn"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickX2", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 487}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "201ZNuZ8BKgJbHAQAbWne2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 489}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71JYNRnf9MT5+u5esIYyFi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1STE4tjFOVa5nr0u9T6OT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 492}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "84182db2-c3e3-443d-be4e-2c7350f17ad5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9xR78m11CVJ8BSpeY4Bjw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 494}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7R4ZAQGZJN7P127nwR3k6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 496}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfcLKNqOBG0LOCrmyqJlub"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98gU1cQj1EVLiSZUF91ESk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "auto", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 499}], "_active": true, "_components": [{"__id__": 509}, {"__id__": 511}, {"__id__": 514}, {"__id__": 516}], "_prefab": {"__id__": 518}, "_lpos": {"__type__": "cc.Vec3", "x": 297.85, "y": -152.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 498}, "_children": [], "_active": true, "_components": [{"__id__": 500}, {"__id__": 502}, {"__id__": 504}, {"__id__": 506}], "_prefab": {"__id__": 508}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 501}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "TỰ ĐỘNG", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0RZpzJEBEeaoEAWh/sUAh"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 503}, "id": "sl46", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03KOq4JtRPL5YvYed4xR5F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 505}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00V/uOt4hDQImAozBJx4S1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 499}, "_enabled": true, "__prefab": {"__id__": 507}, "_contentSize": {"__type__": "cc.Size", "width": 93.7685546875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eLjyPvk5D0J4PNBqmQDoo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cd71Sop19KFqdFA1Ozwjm2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 510}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ba4a8608-7ee6-4170-93be-ae95c3cc9f29@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe4/qCs7NFL6o2f3PiypES"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 512}, "clickEvents": [{"__id__": 513}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef87QWpENBnpO7t/UY2yQ4"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickAutoRotate", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 515}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51MKjkb3VHurUUorWznFix"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 517}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43VX9PNzZAcqFnRkqNAFcu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cPnSV89BKP7SLF/Dm03Gd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "stop", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 520}], "_active": true, "_components": [{"__id__": 530}, {"__id__": 532}, {"__id__": 535}, {"__id__": 537}], "_prefab": {"__id__": 539}, "_lpos": {"__type__": "cc.Vec3", "x": 298.85, "y": -152.674, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "New Label copy", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 519}, "_children": [], "_active": true, "_components": [{"__id__": 521}, {"__id__": 523}, {"__id__": 525}, {"__id__": 527}], "_prefab": {"__id__": 529}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 522}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "TỰ ĐỘNG", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3/3TAONZN2Li3LnKlpJUO"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 524}, "id": "sl47", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84KGNwAQlMjr/GyPmyr253"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 526}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14cbFHqBtHrLWepAL9HweI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 520}, "_enabled": true, "__prefab": {"__id__": 528}, "_contentSize": {"__type__": "cc.Size", "width": 93.7685546875, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9TOF1qY1AKLVhGTQuuSR9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67AS/jHCJHPb7EH7GQsMsO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 531}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ba4a8608-7ee6-4170-93be-ae95c3cc9f29@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14tkSCEH5LiqeMeY3jnpK1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 533}, "clickEvents": [{"__id__": 534}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d54DQHeQlBUp5isb9kmsTX"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "onClickStop", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 536}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66cpVC53hGPq0lBetbYHmh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 538}, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cVQb/lNRO2pCUO5vPAng2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5a6uo06RNN2KTiq1ZncwK2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "CardsFrima", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 541}, {"__id__": 549}, {"__id__": 557}, {"__id__": 565}, {"__id__": 573}, {"__id__": 581}, {"__id__": 589}, {"__id__": 597}, {"__id__": 605}, {"__id__": 613}, {"__id__": 621}, {"__id__": 629}, {"__id__": 637}, {"__id__": 645}, {"__id__": 653}, {"__id__": 661}, {"__id__": 669}, {"__id__": 677}, {"__id__": 685}, {"__id__": 693}, {"__id__": 701}, {"__id__": 709}, {"__id__": 717}, {"__id__": 725}, {"__id__": 733}, {"__id__": 741}, {"__id__": 749}, {"__id__": 757}, {"__id__": 765}, {"__id__": 773}, {"__id__": 781}, {"__id__": 789}, {"__id__": 797}, {"__id__": 805}, {"__id__": 813}, {"__id__": 821}, {"__id__": 829}, {"__id__": 837}, {"__id__": 845}, {"__id__": 853}, {"__id__": 861}, {"__id__": 869}, {"__id__": 877}, {"__id__": 885}, {"__id__": 893}, {"__id__": 901}, {"__id__": 909}, {"__id__": 917}, {"__id__": 925}, {"__id__": 933}, {"__id__": 941}, {"__id__": 949}], "_active": false, "_components": [{"__id__": 957}, {"__id__": 959}], "_prefab": {"__id__": 961}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spade-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 542}, {"__id__": 544}, {"__id__": 546}], "_prefab": {"__id__": 548}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 543}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b85faa0f-2e30-42a9-81e3-55d8149a57ed@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9hayNbA1A655DlrzoQ2Vd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 545}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccmcGaiFtN6JRHOE2MMHoP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 547}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7BMbOaXxK4LQ8TCpZtoZL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cNPbsDXVB2rXGi3eMHBNH", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 550}, {"__id__": 552}, {"__id__": 554}], "_prefab": {"__id__": 556}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 551}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b1303b3d-02fb-4285-9b9f-78d05143d552@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d0NzUAopHtqQv3qiXkoSi"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 553}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16lBjFsvRM9Zaz+AymvFhG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 549}, "_enabled": true, "__prefab": {"__id__": 555}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73ePAhQKtFYppSWyZfhv77"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bEjCdYxhMuYbVq+hVWfbL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 558}, {"__id__": 560}, {"__id__": 562}], "_prefab": {"__id__": 564}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 557}, "_enabled": true, "__prefab": {"__id__": 559}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "51faccf5-d085-4fc6-a580-9ee3323b0169@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ddtjdkNhDaL5WPWPgKSlp"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 557}, "_enabled": true, "__prefab": {"__id__": 561}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3T8F0OqdMBoYoTSchbGer"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 557}, "_enabled": true, "__prefab": {"__id__": 563}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ciavfxOpFBp1DSbtPmflv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48AEE2Q7lPQ74sHYlXkfnf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 566}, {"__id__": 568}, {"__id__": 570}], "_prefab": {"__id__": 572}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 567}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9723972a-0542-4210-a5be-c5f03e08376c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaIXvQ5gVIPaSQmQgAy7Ky"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 569}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aDw7Y0MxFxoIfvBl07Bxm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 565}, "_enabled": true, "__prefab": {"__id__": 571}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f35tjEASFMzJTYFR1cLFfm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63PSdcW+tDSL8CEq+4hXAq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 574}, {"__id__": 576}, {"__id__": 578}], "_prefab": {"__id__": 580}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 575}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5fc93c90-160c-4d97-abe1-4a5b4612bed2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbhTpDjNBNcp0rAhLhEpgj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 577}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38TddxOJ5HrqDj2Vc4h+6c"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 579}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20Ql/E8vNJr6hIVwq084n0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abXhK0yNpP95eshz1C0fbN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 582}, {"__id__": 584}, {"__id__": 586}], "_prefab": {"__id__": 588}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 583}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "25ab2810-250e-4146-bc90-b947e7146472@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fElhBgvZAA5QQAl2vBnSY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 585}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6xQmK4WpObIstoTWRhGqb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 581}, "_enabled": true, "__prefab": {"__id__": 587}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27TnK4QqZJJ4OLG+7AOSxt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41BEhqb6RMm74O5qMr9+0b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 590}, {"__id__": 592}, {"__id__": 594}], "_prefab": {"__id__": 596}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 589}, "_enabled": true, "__prefab": {"__id__": 591}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "27045a15-8d20-4aa6-8b9d-75d54daa1982@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30IaksAWNJIK4TlC/IG4jq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 589}, "_enabled": true, "__prefab": {"__id__": 593}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4RyisWGBHtrj/BGXhJtz+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 589}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74VSfcNu1MLpRJ23+WKNzO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03SN9bUxJAUrdP2alB2/t4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 598}, {"__id__": 600}, {"__id__": 602}], "_prefab": {"__id__": 604}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 599}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "01f5589f-6cf8-45ef-ae35-97425c2f46c3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1rXURZmdM0YrnbEJuFr/u"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 601}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cYpKRu9xJNZOWEgxuj1zU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 603}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cLbribExHSpP41OlTnadw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3FuofZbZPaokP+XXDmznv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 606}, {"__id__": 608}, {"__id__": 610}], "_prefab": {"__id__": 612}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 605}, "_enabled": true, "__prefab": {"__id__": 607}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a375e45e-7fc8-4ccd-abc4-eec04e730794@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7d6vV0eVNMJYqNXDtgRH0F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 605}, "_enabled": true, "__prefab": {"__id__": 609}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19e8NuwA5CgYOjcT2n8QT0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 605}, "_enabled": true, "__prefab": {"__id__": 611}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddv/FgSWVCWbnZyZKA/1JO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dgsm9uAlBtbeRM975vg3r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-J", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 614}, {"__id__": 616}, {"__id__": 618}], "_prefab": {"__id__": 620}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 613}, "_enabled": true, "__prefab": {"__id__": 615}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "83bd5286-f191-4ac8-9bac-7ca8d6e07844@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ce0ErcweRFtpq1M3T+dEx9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 613}, "_enabled": true, "__prefab": {"__id__": 617}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3b+0HCyJtJT4VEkV9SrQ5O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 613}, "_enabled": true, "__prefab": {"__id__": 619}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2futX8lHFARZ1EFB6N1w0n"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "90nUNtKUZOvpl8q7xIAOIr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-Q", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 622}, {"__id__": 624}, {"__id__": 626}], "_prefab": {"__id__": 628}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 623}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fe74f230-8d7b-48f1-8cfe-96bafc6ae410@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e840gUpIdP0Y6Wv4wQ4KJs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 625}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eaCa3PoZA2arN7aMsPG8d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 621}, "_enabled": true, "__prefab": {"__id__": 627}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fET9uOK9F/bCyoeni+Jr0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6mT0AjSpGv6vs+LvMvtJZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-K", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 630}, {"__id__": 632}, {"__id__": 634}], "_prefab": {"__id__": 636}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 631}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bb776c7e-6b65-4566-9c81-c76245746047@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5ybNkZ5xKjoz8YWfvQjcC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 633}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51BZ+mzXZDWKZUfLKyIO35"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 635}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73/uNi/cpN3ZNOnkYZe2Ud"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "77DK5mJuJBkK0RRhBftzGo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "spade-A", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 638}, {"__id__": 640}, {"__id__": 642}], "_prefab": {"__id__": 644}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 639}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fb673891-f60f-498a-9bed-c117afa5fd2d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cQEC3mVZKQp4z8fNs7P5P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 641}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c4QFRvHLxGnqyP56kCQxbo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 643}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eHGOJHBdN2L1eG4DmEMql"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69bpWoM9dNY7fVjP1Jgpxt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 646}, {"__id__": 648}, {"__id__": 650}], "_prefab": {"__id__": 652}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 647}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0a2e8465-e2d9-4229-b6d6-bfb0d99db24b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9KQxOvUNKY7eFQAvO4wuE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 649}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aCd3c0ORHFq7M+9BzLktE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 645}, "_enabled": true, "__prefab": {"__id__": 651}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beKVF1SoRLvJxIkUhb8t8p"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eNw26OrdLA4SuhzEK2VBU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 654}, {"__id__": 656}, {"__id__": 658}], "_prefab": {"__id__": 660}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 655}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32c67358-073f-4db1-8050-7fc74573f0d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05+8dF3HFK1Jv/dGFKdTyN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 657}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2a054FhtG45vpEF5kSMsx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 659}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42/3ocLl1EOYNC/9RjlQxl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1aDM6K1KFJ6prGV8ckgEyt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 662}, {"__id__": 664}, {"__id__": 666}], "_prefab": {"__id__": 668}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 663}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbdc940a-ec4f-40b3-89f9-e421f713f074@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcQJ/CSTlFT5v7X+pYOVmJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 665}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bafEXalJNoaSBgeRb+gOd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 667}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4f8dUMOqtHjoq+Vfx9xCcB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4euusNUQFCopUygM169jLA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 670}, {"__id__": 672}, {"__id__": 674}], "_prefab": {"__id__": 676}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 669}, "_enabled": true, "__prefab": {"__id__": 671}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b96d7c4d-0d78-444d-97e7-0f41809816f3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b72l5LvLxMQJkERLOJe1OR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 669}, "_enabled": true, "__prefab": {"__id__": 673}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4930EgmXhPorbXkG6vpiTK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 669}, "_enabled": true, "__prefab": {"__id__": 675}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecFWTGBF1ASJwkujFU8cfI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41P42EXktKGqo2r1dLgUZu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 678}, {"__id__": 680}, {"__id__": 682}], "_prefab": {"__id__": 684}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 679}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "42de981f-8688-4f7a-b3de-32047d9a5687@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "camVQI3G1HdpvnXEHSndZG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 681}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee+YzFdLpKyaJ4A+rsHk+6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 683}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9QpR4RR9JdobMgXRnTre+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2flEgksC5B2YFJqDjddiv+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 686}, {"__id__": 688}, {"__id__": 690}], "_prefab": {"__id__": 692}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 685}, "_enabled": true, "__prefab": {"__id__": 687}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d489908-2bbe-4aba-ba18-cb1ab419c321@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48KR4G1cZIW5L7Dxs1Dgex"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 685}, "_enabled": true, "__prefab": {"__id__": 689}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76S6E86eFO/ahfU8Q334l1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 685}, "_enabled": true, "__prefab": {"__id__": 691}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ey8TLUQlMkLIn/kA8AcX+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76G2fAKOFBMakkbX7Y6dPB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 694}, {"__id__": 696}, {"__id__": 698}], "_prefab": {"__id__": 700}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 693}, "_enabled": true, "__prefab": {"__id__": 695}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d076c6c6-f308-45b3-8a20-4a435448d60a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bWSx92MhOvph6g+Rdrbr5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 693}, "_enabled": true, "__prefab": {"__id__": 697}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cdCZ6oIxEOps8Gyu5ZxEi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 693}, "_enabled": true, "__prefab": {"__id__": 699}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66E4OjQodFsrDADVYRrDj8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5Qaql7l9L958ZBYa4Vt1P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 702}, {"__id__": 704}, {"__id__": 706}], "_prefab": {"__id__": 708}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 701}, "_enabled": true, "__prefab": {"__id__": 703}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "df403d0d-7dbe-467d-b703-f0fc69a1a2cb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "561bw390NDda27OCjJbGgQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 701}, "_enabled": true, "__prefab": {"__id__": 705}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30AiVJt5pIT4uXVluft84p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 701}, "_enabled": true, "__prefab": {"__id__": 707}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe9XD5KpxLgJxNB5PKJsV/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccWETkMPpPSr5roF/A0VKc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 710}, {"__id__": 712}, {"__id__": 714}], "_prefab": {"__id__": 716}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 709}, "_enabled": true, "__prefab": {"__id__": 711}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e181154e-eba2-4775-b79e-a0fed9e3480f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8898boX3JCWIWCLfdGvRWA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 709}, "_enabled": true, "__prefab": {"__id__": 713}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "485BKRFcVPf6aGgEDlINT3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 709}, "_enabled": true, "__prefab": {"__id__": 715}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39HsqFN+1LCKvwQSDGsEwq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6clpbtjNpEmagGghy805SJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-J", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 718}, {"__id__": 720}, {"__id__": 722}], "_prefab": {"__id__": 724}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 717}, "_enabled": true, "__prefab": {"__id__": 719}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3b214535-ef89-4a98-938e-50d14796d920@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbH/re1q5Ckb1spHexbFV7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 717}, "_enabled": true, "__prefab": {"__id__": 721}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98s4uA2shO7Z6tA3evYdpJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 717}, "_enabled": true, "__prefab": {"__id__": 723}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0NwGZ96RFoaQkXQ19iqA+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5aee2ycE5EF6pEyF1I8h8r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-Q", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 726}, {"__id__": 728}, {"__id__": 730}], "_prefab": {"__id__": 732}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 727}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b220d448-4339-4b81-a90d-6f1bef16b69d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52UkkYcXJLIp+YREaeXcZP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 729}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eWX/PFfdMp7bKmaeyhlTE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 731}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdiEpxUH9BvYsnHsE3KDcS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3jZ/N7uBHzrKOeAbSIIrq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-K", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 734}, {"__id__": 736}, {"__id__": 738}], "_prefab": {"__id__": 740}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 733}, "_enabled": true, "__prefab": {"__id__": 735}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1ec02f56-62dc-42a0-aa1e-4c99b2d6d42d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecw2gaAFhGL4FyVmzGGQDU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 733}, "_enabled": true, "__prefab": {"__id__": 737}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08dE5wR49CL5OM5nWJDOOB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 733}, "_enabled": true, "__prefab": {"__id__": 739}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c3rlSCZ9DHKIId2vLivfS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63mJCI3TRHE7Hd3+u43oMo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "club-A", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 742}, {"__id__": 744}, {"__id__": 746}], "_prefab": {"__id__": 748}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 743}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7e1927aa-aaf1-4780-b05b-7373e9b80a4a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72HRwMyvFPRJqS73mAz/jq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 745}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2dkOT5A9Fi5QgYRxv3K30"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 747}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dpLAorQJFzaOXvF5cWoHU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28KcyvhnRKzobm55obPpcN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 750}, {"__id__": 752}, {"__id__": 754}], "_prefab": {"__id__": 756}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 749}, "_enabled": true, "__prefab": {"__id__": 751}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "42d5dee8-fcee-4d05-bf60-1d055a7619d7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6nggs1EhABJSXwCwJcd7h"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 749}, "_enabled": true, "__prefab": {"__id__": 753}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85brPOpNpErLtTGAi9tAvv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 749}, "_enabled": true, "__prefab": {"__id__": 755}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4i44tRhVG7YRWkx4WohlB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acaihWZ5hLM4m9qTS4oo6J", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 758}, {"__id__": 760}, {"__id__": 762}], "_prefab": {"__id__": 764}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 759}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d11c9ecd-9df8-4de0-9ba6-7b2ccb6cd2a7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32lxKKvHtI1pZVQiZmPhGv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 761}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bxGsI1xxOeIlFWsPquD9i"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 757}, "_enabled": true, "__prefab": {"__id__": 763}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3O9H0grNHWZ3on2C+dbnk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fwr/xW7JOx6FaaduTKgFc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 766}, {"__id__": 768}, {"__id__": 770}], "_prefab": {"__id__": 772}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 765}, "_enabled": true, "__prefab": {"__id__": 767}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8d64db84-78c9-49cc-8766-0cdb3578b3e8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bjIlPyXpDw7c2CxjLBvcs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 765}, "_enabled": true, "__prefab": {"__id__": 769}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fnKZazIFFV5LQwsDUJK0o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 765}, "_enabled": true, "__prefab": {"__id__": 771}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a76zVcLC1O1au4PjbgvdcW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5rx5TGJZO05M24AbOcTU4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 774}, {"__id__": 776}, {"__id__": 778}], "_prefab": {"__id__": 780}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 773}, "_enabled": true, "__prefab": {"__id__": 775}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7436e0fc-d1a9-494c-8a6e-5e165d8b9ff3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaY+yyBqxESYIUeD+WAuqd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 773}, "_enabled": true, "__prefab": {"__id__": 777}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8banXRN7tBr6dtlfHndepP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 773}, "_enabled": true, "__prefab": {"__id__": 779}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88kU22noRAU68l+2i3ARaQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "598qkMuLlHX7L1Xm1wxw4S", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 782}, {"__id__": 784}, {"__id__": 786}], "_prefab": {"__id__": 788}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 781}, "_enabled": true, "__prefab": {"__id__": 783}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e71b749-9bbd-4936-9063-7f3ef126ec64@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9X/NoHGxAv67PzFtQrF3R"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 781}, "_enabled": true, "__prefab": {"__id__": 785}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cHEzG9gZJyo3oCkshrIOU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 781}, "_enabled": true, "__prefab": {"__id__": 787}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1aW+WsNpDp4UavQDPFwE3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1154RsThGrbJxHnYuT+EL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 790}, {"__id__": 792}, {"__id__": 794}], "_prefab": {"__id__": 796}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 791}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fbd4e42a-400c-4412-a80d-fdaa480c7e5c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47VtlFgpxCpIxX1NGtaoFJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 793}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0l9XIDnBF1qx86j/CbWrF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 795}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44Vmz8CwFGHa7uxISNeB+8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4RSIYBnlPvbG2eAQDtlqJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 798}, {"__id__": 800}, {"__id__": 802}], "_prefab": {"__id__": 804}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 799}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5f0afee7-a403-4774-b530-1950c97d12bd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58Mz00+rpDXr8fvqHuEaI1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 801}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78u+ZlYgZLyLeHCNh7v3HL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 803}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6prZRMrJAZI5Vs6HJRNPs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33B+2CHltJ2odju8/FlhaX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 806}, {"__id__": 808}, {"__id__": 810}], "_prefab": {"__id__": 812}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 805}, "_enabled": true, "__prefab": {"__id__": 807}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "71d22969-e3b4-4a92-a4c1-f753e8621788@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54Cbvp2K5BZ58OiWzfvbgr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 805}, "_enabled": true, "__prefab": {"__id__": 809}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e63Rp1OXdAWanY7VhrJB8h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 805}, "_enabled": true, "__prefab": {"__id__": 811}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c//+Zy2REOpnHrAuV9TLm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ff72NnjyBL0qPcSu3D0+f7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 814}, {"__id__": 816}, {"__id__": 818}], "_prefab": {"__id__": 820}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 815}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0ca0e8f9-4e77-4dd7-96c7-0b05e980da80@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fRWQYIRRLfoBaiQ18kGg2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 817}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2LGTP+cBPvLvhOoGamICT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 819}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eftXpJvjVLn5uHJRtFnDu9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26S2kRxSFAm5UbGCE3ca+z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-J", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 822}, {"__id__": 824}, {"__id__": 826}], "_prefab": {"__id__": 828}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 821}, "_enabled": true, "__prefab": {"__id__": 823}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ded911c2-76ea-463b-be3e-16739ecc6bb3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a863pLgn1OnrdSXuwiosCa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 821}, "_enabled": true, "__prefab": {"__id__": 825}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dL+t+2p5DErCTXAO8NkA1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 821}, "_enabled": true, "__prefab": {"__id__": 827}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "535wnYkilNir9imRX8kn7i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96YL3I4VBPea0dnIvHuuzh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-Q", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 830}, {"__id__": 832}, {"__id__": 834}], "_prefab": {"__id__": 836}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 831}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1758f145-3afc-4f9e-b093-515dc675e8b7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90eyKSNv5BAqpvCGrDCKiv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 833}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eddEpAWgZLF7bCbLhkINl5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 835}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76+59t80BLhL7/0W5PRRxf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46RViO5uJCLoPL7JUYd5DL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-K", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 838}, {"__id__": 840}, {"__id__": 842}], "_prefab": {"__id__": 844}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 837}, "_enabled": true, "__prefab": {"__id__": 839}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e3073f63-2c6a-4048-8101-5ba90572e629@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53oH1dcepDlptUYBwjT9lC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 837}, "_enabled": true, "__prefab": {"__id__": 841}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9bnceVgdOZ5lelcDtmL5z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 837}, "_enabled": true, "__prefab": {"__id__": 843}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83tiHdY4BKpKFekq5Rz3xS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9ATlTsZBIAJw1JYkna2Ii", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "diamond-A", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 846}, {"__id__": 848}, {"__id__": 850}], "_prefab": {"__id__": 852}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 845}, "_enabled": true, "__prefab": {"__id__": 847}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "91304985-706e-4f5f-9c2d-bf3c7796cd62@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26IKq7yO9JqaBTU+99pVvC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 845}, "_enabled": true, "__prefab": {"__id__": 849}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84pok3TLtEGJmyCLncQq9t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 845}, "_enabled": true, "__prefab": {"__id__": 851}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12uIE8iZlB6J0nn7lUvDCo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0QEPw7LxHA7HvagPvPEsW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 854}, {"__id__": 856}, {"__id__": 858}], "_prefab": {"__id__": 860}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 853}, "_enabled": true, "__prefab": {"__id__": 855}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5e23d026-3b9f-48d8-aabb-ff4fd4c8aae4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5dj/3lm9EJI6219PVCJQ9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 853}, "_enabled": true, "__prefab": {"__id__": 857}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f26RqoRUBOlaVWDN+mAsCs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 853}, "_enabled": true, "__prefab": {"__id__": 859}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef8ip/AEZAWJnsNWecmfki"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c6WFXuQ7JDf6Tj7xsMaL4T", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 862}, {"__id__": 864}, {"__id__": 866}], "_prefab": {"__id__": 868}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 861}, "_enabled": true, "__prefab": {"__id__": 863}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8f13f588-e38e-4c79-ad3c-75adf1c586a0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b85AwpjK5L4KiFv3cupDDg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 861}, "_enabled": true, "__prefab": {"__id__": 865}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c77tDQEKJA0r5EGmIQ2B5L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 861}, "_enabled": true, "__prefab": {"__id__": 867}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebPigbbaBCtLgegUR4MOrI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2jWyOEfRLyKDIfykdsaD0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 870}, {"__id__": 872}, {"__id__": 874}], "_prefab": {"__id__": 876}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 869}, "_enabled": true, "__prefab": {"__id__": 871}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4b33cbc4-8148-4614-8e07-2c4550d3e79e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bhiHKtZ9NF6kWncvqvvhq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 869}, "_enabled": true, "__prefab": {"__id__": 873}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5udCKbzVF8bCYzE1p8bJT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 869}, "_enabled": true, "__prefab": {"__id__": 875}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18WAULgu5DAIitt1mWjmvf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91ltAl0WVCXrNdJpA/Q4pv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 878}, {"__id__": 880}, {"__id__": 882}], "_prefab": {"__id__": 884}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 879}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ce337aaa-7b5d-4d1d-b588-115a78ec66b7@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9WedcAdpP3YFOA6JJrWgW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 881}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eU3cT46dC8ZwZ1yMYFqSF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 883}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cJ1mV9h1D7oW3gvacCyes"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09LybXrmZNeZpXQcpuIPUD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 886}, {"__id__": 888}, {"__id__": 890}], "_prefab": {"__id__": 892}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 885}, "_enabled": true, "__prefab": {"__id__": 887}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "49cac991-5a1f-41a4-8821-73baa519f228@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bMBr1nehFYYaB2SfWo71Q"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 885}, "_enabled": true, "__prefab": {"__id__": 889}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19K2GS6WlIG6ljy5dZcqLj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 885}, "_enabled": true, "__prefab": {"__id__": 891}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7j2PWJAVNg5acU+TsnD/W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aTtd67cNJZLsfYgGklNe/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 894}, {"__id__": 896}, {"__id__": 898}], "_prefab": {"__id__": 900}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 893}, "_enabled": true, "__prefab": {"__id__": 895}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "26cc6fe4-85e2-4a65-ba5c-c0b715acae86@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26eT9ZiZFOpJAk5322vZPo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 893}, "_enabled": true, "__prefab": {"__id__": 897}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcjKItV0BIVJiUNdh9dvTE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 893}, "_enabled": true, "__prefab": {"__id__": 899}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "078ywO9aJPr5Vy5GSvRwlQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84zzHt+1dORqGumc2oUC6M", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 902}, {"__id__": 904}, {"__id__": 906}], "_prefab": {"__id__": 908}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 901}, "_enabled": true, "__prefab": {"__id__": 903}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c8ab5b75-628c-42cb-ad10-f4a8f2b89dd9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09ZNQ7uPRGiZ2hviZtGW6u"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 901}, "_enabled": true, "__prefab": {"__id__": 905}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dp0w4b35EgLPUpbXJU4F+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 901}, "_enabled": true, "__prefab": {"__id__": 907}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d53tr4FmlJwIdtsvHCR5Yf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "95KVk76vREwKMThr5EnVAD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 910}, {"__id__": 912}, {"__id__": 914}], "_prefab": {"__id__": 916}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 909}, "_enabled": true, "__prefab": {"__id__": 911}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b4460180-4d33-4feb-8cb5-eb3ac310c695@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3mUrA1mBL9KDP+ZqwQpd1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 909}, "_enabled": true, "__prefab": {"__id__": 913}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37goo2roFFXovFsTu6+pAr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 909}, "_enabled": true, "__prefab": {"__id__": 915}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 136}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feZtPScdJCZ76w5PEihRjZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "85zmJ98d9IqYFur8Ti9OxE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 918}, {"__id__": 920}, {"__id__": 922}], "_prefab": {"__id__": 924}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 917}, "_enabled": true, "__prefab": {"__id__": 919}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dab8ae23-7709-42f5-ad87-993c3ab92818@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "896QPVA4BFJ5aJrLbvaEh9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 917}, "_enabled": true, "__prefab": {"__id__": 921}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6coW+Qfu9Du5USbENLaz3z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 917}, "_enabled": true, "__prefab": {"__id__": 923}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22W6BTECJKY6sXNHDh2MpG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dTGt14RFCRoZky92KI/E7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-J", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 926}, {"__id__": 928}, {"__id__": 930}], "_prefab": {"__id__": 932}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 925}, "_enabled": true, "__prefab": {"__id__": 927}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "cbfaf887-19bf-4931-9362-de88b2fd97fd@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b69HY2/x5D5qFSl3RvuUoU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 925}, "_enabled": true, "__prefab": {"__id__": 929}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f2fjtby9HJJIKVmwNx/tF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 925}, "_enabled": true, "__prefab": {"__id__": 931}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58S1XqMbxAjr8evTawFX2c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4f22i6/pFEoKnklL61GXeI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-Q", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 934}, {"__id__": 936}, {"__id__": 938}], "_prefab": {"__id__": 940}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 933}, "_enabled": true, "__prefab": {"__id__": 935}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4a20ef57-9c22-4028-956b-eaa3fb9a43e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65mKEX/8FI8708YxpEpTL1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 933}, "_enabled": true, "__prefab": {"__id__": 937}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68e2e432dAVIQsbbwsD/FK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 933}, "_enabled": true, "__prefab": {"__id__": 939}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71agrAW7tMkYgWQNomRmoR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "267tb1hgtLvqFGqtxmzf7h", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-K", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 942}, {"__id__": 944}, {"__id__": 946}], "_prefab": {"__id__": 948}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 941}, "_enabled": true, "__prefab": {"__id__": 943}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "11e2d263-12ea-4bc9-bc45-babd18718921@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1a/GQIFYVA5IhwbXB+YU0N"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 941}, "_enabled": true, "__prefab": {"__id__": 945}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcRwFPmrVPDLCx86z1CUbm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 941}, "_enabled": true, "__prefab": {"__id__": 947}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bg7BcgMdH45X2Ttv8X0pB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "239EZDwMpKcIP78hoShhhA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "heart-A", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [], "_active": true, "_components": [{"__id__": 950}, {"__id__": 952}, {"__id__": 954}], "_prefab": {"__id__": 956}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 951}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f7221f5f-a7b1-415b-9307-6143e313bee2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aS9L20iVM+aCL1an+gX5N"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 953}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdF4XDLn1PkYZwA1kKBEbA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 955}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96n4mTEitMWa7h6TXEm4vM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdso4O6LVMzpZkPocQlLtJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": {"__id__": 958}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4Kim59StKsbIqjBsmfdpQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": {"__id__": 960}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d31/mcRxBOoIe7yjReavs2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2efka78FVGS6qP9+eOMoS5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "62c5aekeZlAL4VtbeCtXj2Z", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 963}, "_enabled": true, "__prefab": {"__id__": 979}, "nodeSlot": {"__id__": 964}, "itemPrefab": {"__id__": 75}, "soCot": 5, "_id": ""}, {"__type__": "cc.Node", "_name": "Slot", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 964}], "_active": true, "_components": [{"__id__": 962}, {"__id__": 974}, {"__id__": 976}], "_prefab": {"__id__": 978}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 963}, "_children": [], "_active": true, "_components": [{"__id__": 965}, {"__id__": 967}, {"__id__": 969}, {"__id__": 971}], "_prefab": {"__id__": 973}, "_lpos": {"__type__": "cc.Vec3", "x": -269.997, "y": -56.365, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 964}, "_enabled": true, "__prefab": {"__id__": 966}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddentzks1NSoaLXyF520Yn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 964}, "_enabled": true, "__prefab": {"__id__": 968}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aPAU1/pZAOIGs227SIitG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 964}, "_enabled": true, "__prefab": {"__id__": 970}, "_contentSize": {"__type__": "cc.Size", "width": 528, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "011oV7yQRIrrAg//tqgBai"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 964}, "_enabled": true, "__prefab": {"__id__": 972}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07NmWNOdFJP7Udv2+aJs8Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11FoxnX6FEL6+wd9TN4tzQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 963}, "_enabled": true, "__prefab": {"__id__": 975}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79rjdFhxhMmaJs5FCE4by9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 963}, "_enabled": true, "__prefab": {"__id__": 977}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0s7CIWqBP9b8Askua9nUY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1F4L610hFZ4jiVqyfoQax", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fhOsy+8RBTbqEz5jhg1p8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 981}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aajXCSuQ9GrLkIyw7v5tfp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 75}, "_enabled": true, "__prefab": {"__id__": 983}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afs/NFysVElKzjBQbxlZxr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daBMTyrc1FhoF7/AoSMQtv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "toggleAuto", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 986}, {"__id__": 994}, {"__id__": 1002}], "_active": false, "_components": [{"__id__": 1012}, {"__id__": 1015}, {"__id__": 1017}, {"__id__": 1019}], "_prefab": {"__id__": 1021}, "_lpos": {"__type__": "cc.Vec3", "x": -329.887, "y": -173.632, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 985}, "_children": [], "_active": true, "_components": [{"__id__": 987}, {"__id__": 989}, {"__id__": 991}], "_prefab": {"__id__": 993}, "_lpos": {"__type__": "cc.Vec3", "x": -84.262, "y": 18.725, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 986}, "_enabled": true, "__prefab": {"__id__": 988}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8e7I1VeCdFSbbZZi/i2dl7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 986}, "_enabled": true, "__prefab": {"__id__": 990}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51xO5MW8NEbodlqcKq/r+c"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 986}, "_enabled": true, "__prefab": {"__id__": 992}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bQx/hWLtEDbmzd4Gw7y0G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "224N0YSAVGpqEtp8Z5iMSv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 985}, "_children": [], "_active": false, "_components": [{"__id__": 995}, {"__id__": 997}, {"__id__": 999}], "_prefab": {"__id__": 1001}, "_lpos": {"__type__": "cc.Vec3", "x": -84.262, "y": 18.762, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 994}, "_enabled": true, "__prefab": {"__id__": 996}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32NNMM7f9G4q7BNJLQsHE+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 994}, "_enabled": true, "__prefab": {"__id__": 998}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45F0bxd6ZDQZWbOTC5pBof"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 994}, "_enabled": true, "__prefab": {"__id__": 1000}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fUzYhSDxLPoAN1fwIohhK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7V0pi54NALaZ+Gq95k6kt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 985}, "_children": [], "_active": true, "_components": [{"__id__": 1003}, {"__id__": 1005}, {"__id__": 1007}, {"__id__": 1009}], "_prefab": {"__id__": 1011}, "_lpos": {"__type__": "cc.Vec3", "x": 15.346, "y": 1.375, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.55, "y": 0.55, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1002}, "_enabled": true, "__prefab": {"__id__": 1004}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "TỰ QUAY", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 45, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85SQ5o1fxPe5sG7Hc2/3BY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1002}, "_enabled": true, "__prefab": {"__id__": 1006}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03kXHoy9hO+q3bvNmt9FEu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1002}, "_enabled": true, "__prefab": {"__id__": 1008}, "_contentSize": {"__type__": "cc.Size", "width": 203.2, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feAEAyOWhD6qkKNP5NArxK"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1002}, "_enabled": true, "__prefab": {"__id__": 1010}, "id": "txt_auto_spin", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b0ppv+35Hwb8VFiC/kHGF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dO+Y56nhEIqBG5YdHzMjy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 1013}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 986}, "checkEvents": [{"__id__": 1014}], "_isChecked": false, "_checkMark": {"__id__": 995}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d34eCr0J5N+680xDYbgs9G"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 1016}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05o3guejJFuL8D884JIx9R"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 1018}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9d/13a4kxHDbrFvGLtE3G3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 1020}, "_contentSize": {"__type__": "cc.Size", "width": 194, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7CYOGAqhMvbipWjlb/wr8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "084kpNTvFMWapaMpjm79HM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnRank", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 1023}, {"__id__": 1025}, {"__id__": 1028}, {"__id__": 1030}], "_prefab": {"__id__": 1032}, "_lpos": {"__type__": "cc.Vec3", "x": -370.83, "y": -65.828, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1022}, "_enabled": true, "__prefab": {"__id__": 1024}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a6f15c1d-43da-41bd-bb40-5862089e498d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bc+DHxXL9OXqH8WVCET/CI"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1022}, "_enabled": true, "__prefab": {"__id__": 1026}, "clickEvents": [{"__id__": 1027}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43FCrsIxNH75/NARp2HTgF"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actPopupHonor", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1022}, "_enabled": true, "__prefab": {"__id__": 1029}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78e8GNi4dJJoIpNXWQP9T2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1022}, "_enabled": true, "__prefab": {"__id__": 1031}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7e6lIoii1G/6uKlATvh/zj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eHNR+rmBJXbVBX+fC/y3/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnGuide", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 1034}, {"__id__": 1036}, {"__id__": 1039}, {"__id__": 1041}], "_prefab": {"__id__": 1043}, "_lpos": {"__type__": "cc.Vec3", "x": -337.633, "y": 122.844, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1033}, "_enabled": true, "__prefab": {"__id__": 1035}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a324548b-8dcd-4eb9-8990-************@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9s9wEYKdGBp++Zd/vjdcl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1033}, "_enabled": true, "__prefab": {"__id__": 1037}, "clickEvents": [{"__id__": 1038}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fPkZE3AxNuK/RIBWvBQdD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actPopupGuide", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1033}, "_enabled": true, "__prefab": {"__id__": 1040}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffXA7/CCFAw7qE+BM+sOQ5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1033}, "_enabled": true, "__prefab": {"__id__": 1042}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29e/6j12hPgY4WLHeflI9J"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfBChOPupGepCkJF6datpG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnHistory", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 1045}, {"__id__": 1047}, {"__id__": 1050}, {"__id__": 1052}], "_prefab": {"__id__": 1054}, "_lpos": {"__type__": "cc.Vec3", "x": -393.283, "y": 31.046, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1044}, "_enabled": true, "__prefab": {"__id__": 1046}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4583c13b-0a78-4213-9135-e9e8378ce113@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abBbT/bstG24t0TqSBDnvG"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1044}, "_enabled": true, "__prefab": {"__id__": 1048}, "clickEvents": [{"__id__": 1049}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d2fZL8ptDuLgc7JQkIUyO"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actPopupHistory", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1044}, "_enabled": true, "__prefab": {"__id__": 1051}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85IsXGkRVLoYV2MpwzyEQY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1044}, "_enabled": true, "__prefab": {"__id__": 1053}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1exuEQmWJPiIl1Vpvto7Vs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aMPaQLZtNRKXMS76d5gcF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnSpin", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 1056}, {"__id__": 1059}, {"__id__": 1061}], "_prefab": {"__id__": 1063}, "_lpos": {"__type__": "cc.Vec3", "x": 332.59, "y": 97.223, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1057}, "clickEvents": [{"__id__": 1058}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 1055}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69Bg6vXhZMWIiCn3hRytFe"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actSpin", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1060}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4Kh4DHQRPOaLYnCNMDxEm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1062}, "_contentSize": {"__type__": "cc.Size", "width": 90, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fczEnFGldB7axVYDAW3uh/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43T9ilfZlEEoyiS10UmFy/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "sprResult", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": false, "_components": [{"__id__": 1065}, {"__id__": 1067}, {"__id__": 1069}], "_prefab": {"__id__": 1071}, "_lpos": {"__type__": "cc.Vec3", "x": 30.753, "y": -453.366, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1064}, "_enabled": true, "__prefab": {"__id__": 1066}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_skeletonData": null, "defaultSkin": "default", "defaultAnimation": "", "_premultipliedAlpha": true, "_timeScale": 1, "_preCacheMode": 0, "_cacheMode": 0, "_sockets": [], "_useTint": false, "_debugMesh": false, "_debugBones": false, "_debugSlots": false, "_enableBatch": false, "loop": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeym/Huz9DPL+Vv8Dz+Nm3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1064}, "_enabled": true, "__prefab": {"__id__": 1068}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4aG7iRNJxJM7HYz66872x/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1064}, "_enabled": true, "__prefab": {"__id__": 1070}, "_contentSize": {"__type__": "cc.Size", "width": 858, "height": 595.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfvbhgHaxK6awA4NbfIFWr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11CWWSLgpDD6EJrAad2DDI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "toast", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 1073}], "_active": false, "_components": [{"__id__": 1081}, {"__id__": 1083}, {"__id__": 1085}], "_prefab": {"__id__": 1087}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.325, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lblToast", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1072}, "_children": [], "_active": true, "_components": [{"__id__": 1074}, {"__id__": 1076}, {"__id__": 1078}], "_prefab": {"__id__": 1080}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.153, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1073}, "_enabled": true, "__prefab": {"__id__": 1075}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "...", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 35, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44ksVP9h9N2pZxT4MvNLlR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1073}, "_enabled": true, "__prefab": {"__id__": 1077}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dr+XSoi9B04oifUue7nId"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1073}, "_enabled": true, "__prefab": {"__id__": 1079}, "_contentSize": {"__type__": "cc.Size", "width": 29.17236328125, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11AHlqfzJG6J0pPMgf1aqs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6e7X9u11JF0pmtrWYTbKP5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1072}, "_enabled": true, "__prefab": {"__id__": 1082}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 41, "b": 41, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27OjbU2gFDg4FER1iWmIBr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1072}, "_enabled": true, "__prefab": {"__id__": 1084}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79KojsGHlKbL1il/R92sdP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1072}, "_enabled": true, "__prefab": {"__id__": 1086}, "_contentSize": {"__type__": "cc.Size", "width": 679, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8blfRqD5Mf6/RdFBnKDbd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1Vr1P5wZH6bPaTvc5pnTz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "toggleAuto", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [{"__id__": 1089}, {"__id__": 1097}, {"__id__": 1105}, {"__id__": 1113}], "_active": false, "_components": [{"__id__": 1123}, {"__id__": 1126}, {"__id__": 1128}], "_prefab": {"__id__": 1130}, "_lpos": {"__type__": "cc.Vec3", "x": 326.799, "y": -173.632, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Group 18", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1088}, "_children": [], "_active": true, "_components": [{"__id__": 1090}, {"__id__": 1092}, {"__id__": 1094}], "_prefab": {"__id__": 1096}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1089}, "_enabled": true, "__prefab": {"__id__": 1091}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9InwbTsRAZ6raABpnn7kJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1089}, "_enabled": true, "__prefab": {"__id__": 1093}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6939M+a3FB17bzPz4eOGYW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1089}, "_enabled": true, "__prefab": {"__id__": 1095}, "_contentSize": {"__type__": "cc.Size", "width": 194, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ceYhjh25M9p2wB2TGu6wH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54ZSBYCUtJ5Lh+gQ04VZaQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1088}, "_children": [], "_active": true, "_components": [{"__id__": 1098}, {"__id__": 1100}, {"__id__": 1102}], "_prefab": {"__id__": 1104}, "_lpos": {"__type__": "cc.Vec3", "x": 88.596, "y": 18.725, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1097}, "_enabled": true, "__prefab": {"__id__": 1099}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4S5BgOF5Ky5jYhdPtG3cR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1097}, "_enabled": true, "__prefab": {"__id__": 1101}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcpz1wAjNLN5o4/junomf3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1097}, "_enabled": true, "__prefab": {"__id__": 1103}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48zLuTRT9K/Kc1+7GTdF0P"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6aDcPBVudKkozlxaf2YXew", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1088}, "_children": [], "_active": false, "_components": [{"__id__": 1106}, {"__id__": 1108}, {"__id__": 1110}], "_prefab": {"__id__": 1112}, "_lpos": {"__type__": "cc.Vec3", "x": 88.596, "y": 18.762, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1105}, "_enabled": true, "__prefab": {"__id__": 1107}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffx02E+dZJI6AopJusjOoB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1105}, "_enabled": true, "__prefab": {"__id__": 1109}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63Nes9Y/lIhqNhNdr5XFDk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1105}, "_enabled": true, "__prefab": {"__id__": 1111}, "_contentSize": {"__type__": "cc.Size", "width": 44, "height": 35}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "213UDKnCBMPaiszMLgT8P4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3WFR0NbtGY7AMLmLMRZ7t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1088}, "_children": [], "_active": true, "_components": [{"__id__": 1114}, {"__id__": 1116}, {"__id__": 1118}, {"__id__": 1120}], "_prefab": {"__id__": 1122}, "_lpos": {"__type__": "cc.Vec3", "x": -7.505, "y": -3.785, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.55, "y": 0.55, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1113}, "_enabled": true, "__prefab": {"__id__": 1115}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "SIÊU TỐC", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 45, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80CJHs2+5LOZ3FMykndEBM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1113}, "_enabled": true, "__prefab": {"__id__": 1117}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddzt64JspGGKZMpj560HiV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1113}, "_enabled": true, "__prefab": {"__id__": 1119}, "_contentSize": {"__type__": "cc.Size", "width": 212.52, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a72Dlf/cRBVJ97d5tuKOgn"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1113}, "_enabled": true, "__prefab": {"__id__": 1121}, "id": "txt_super_fast", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74TLy+iBJFLJpsX76cJwYn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48LyIjL5tBEK1FKwExBv7q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1088}, "_enabled": true, "__prefab": {"__id__": 1124}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 1097}, "checkEvents": [{"__id__": 1125}], "_isChecked": false, "_checkMark": {"__id__": 1106}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0codsWA5VGyYfyRqmeAZuL"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1088}, "_enabled": true, "__prefab": {"__id__": 1127}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16D5INn3xD4LC+fWqbt4Z4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1088}, "_enabled": true, "__prefab": {"__id__": 1129}, "_contentSize": {"__type__": "cc.Size", "width": 194, "height": 71}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bxBBW2JtCUocXzDJtHSfW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8jjRulsBG7LDoj7lFBO3m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "title-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 1132}, {"__id__": 1134}, {"__id__": 1137}, {"__id__": 1139}], "_prefab": {"__id__": 1141}, "_lpos": {"__type__": "cc.Vec3", "x": -39.0075, "y": 268.08299999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1133}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "3498b9c3-a203-40fe-9a93-ba7bb468125a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0zVAbYJ5Inr5gDXiE93aS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1135}, "clickEvents": [{"__id__": 1136}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.95, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12C4YqS8xPXL6faP2mlSLw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "93f55F36+ZG/4x1s8HLfpyR", "handler": "actGoldTicket", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1138}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dtjHB7uRPXLMcg9h6ApuI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1140}, "_contentSize": {"__type__": "cc.Size", "width": 387.759, "height": 172.844}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30241cYFFKHaZSIS5Q0Bsr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ca1uAsA2xHBY5jRsvd3QuK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 1143}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "384W23S11OlqS/MkEknYwr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 1145}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96jwo53kVFAK5RkZ5hPjn8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 1147}, "_contentSize": {"__type__": "cc.Size", "width": 925.5, "height": 420}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.4}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95WHW6vu9AfZDcbEfIrkqA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74iPlmdOxDYLcklXnKt8X5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 1150}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33ZBBmTe5Msa9UuzRi2dlr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 1152}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04RhGaMD5DxJP9ai4kxF1o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a3zuoXD/BFCpXPSDPFEtSL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1155}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96rQojRhRHRpTS/Ut/lF5x"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1157}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86v/wBFM1C+rNKkkrJeej+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1159}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "992jWeL6BIp5FeEsQByrC/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27NDlFh65NzrOoc5tcn/Py", "instance": null, "targetOverrides": null}]