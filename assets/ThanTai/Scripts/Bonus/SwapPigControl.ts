import { _decorator, Component, Label, Button } from "cc";
const { ccclass, property, menu } = _decorator;

@ccclass
@menu("ThanTai/Bonus/SwapPigControl")
export default class SwapPigControl extends Component {

    @property(Label) lblFrom: Label = null;
    @property(Label) lblTo: Label = null;
    @property(Button) btnAgree: Button = null;
    @property(Button) btnCancel: Button = null;

    public from: number = null;
    public to: number = null;

    setInfo(from: number, to: number) {
        this.from = from;
        this.to = to;
        this.lblFrom.string = from.toString();
        this.lblTo.string = to.toString();
    }
}
