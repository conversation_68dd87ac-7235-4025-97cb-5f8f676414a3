import { _decorator, Component, Node, instantiate, ScrollView, EditBox, Sprite<PERSON>rame, Sprite, Label, RichText, CCInteger, tween, UITransform, Button } from 'cc';
import App from "db://assets/Lobby/scripts/common/App";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {XocDiaLiveController} from "db://assets/CasinoLive/scripts/XocDia/XocDiaLiveController";
import Config from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import {RongHoLiveController} from "db://assets/CasinoLive/scripts/RongHo/RongHoLiveController";
import {RouletteLiveController} from "db://assets/CasinoLive/scripts/Roulette/RouletteLiveController";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("CasinoLive/ChatBox")
export class CasinoLiveChatBox extends Component {

    CHAT_CHANNEL: string = "game_xd_jp_live";
    CHAT_MD5_CHANNEL: string = "game_xd_md5_live";
    RH_CHANNEL: string = "game_rh_live";

    @property(Node)
    itemChatTemplate: Node = null;
    @property(ScrollView)
    scrMessage: ScrollView = null;
    @property(EditBox)
    edbMessage: EditBox = null;
    @property(Node)
    chatBox: Node = null;
    @property(Node)
    chatTipBox: Node = null;
    @property(Node)
    listGiftNode: Node = null;
    @property(Node)
    giftTemplate: Node = null;
    @property([SpriteFrame])
    giftFrames: SpriteFrame[] = [];
    @property(Node)
    selectedTIP: Node = null;
    @property(Node)
    tipIconTemplate: Node = null;
    @property(CCInteger)
    gameId: number = -1;
    @property(Label)
    lblToast: Label = null;

    private giftList = [
        { id: 1, value: "1K", key: "gift_commission" },
        { id: 2, value: "2K", key: "gift_lollipop" },
        { id: 3, value: "5K", key: "gift_icecream" },
        { id: 4, value: "10K", key: "gift_milk_tea" },
        { id: 5, value: "20K", key: "gift_beer" },
        { id: 6, value: "50K", key: "gift_kiss" },
        { id: 7, value: "100K", key: "gift_heart" },
        { id: 8, value: "200K", key: "gift_ring" },
        { id: 9, value: "500K", key: "gift_iphone" },
        { id: 10, value: "1M", key: "gift_diamond" },
        { id: 11, value: "2M", key: "gift_crown" },
        { id: 12, value: "5M", key: "gift_motorbike" },
        { id: 13, value: "10M", key: "gift_car" },
    ];

    private giftIdSelected = 0;
    private giftNodeSelected: Node = null;
    private defaultChatChannel = this.CHAT_CHANNEL;
    private isMD5 = false;

    onDestroy() {
        ChatHubSignalRClient.getInstance().unRegisterChat(this.defaultChatChannel, (_res) => {})
    }

    start() {
        switch (this.gameId) {
            case -1:
                this.isMD5 = XocDiaLiveController.instance.isMD5;
                if (this.isMD5) {
                    this.gameId = Config.InGameIds.XocdiaMd5Live;
                    this.defaultChatChannel = this.CHAT_MD5_CHANNEL;
                } else {
                    this.gameId = Config.InGameIds.XocDiaLive;
                    this.defaultChatChannel = this.CHAT_CHANNEL;
                }
                break;
            case Config.InGameIds.XocdiaMd5Live:
                this.isMD5 = true;
                this.defaultChatChannel = this.CHAT_MD5_CHANNEL;
                break;
            case Configs.InGameIds.XocDiaLive:
                this.isMD5 = false;
                this.defaultChatChannel = this.CHAT_CHANNEL;
                break;
            case Configs.InGameIds.DragonTigerLive:
                this.defaultChatChannel = this.RH_CHANNEL;
                break;
            default:
                this.isMD5 = false;
                this.defaultChatChannel = this.CHAT_CHANNEL;
                break;
        }

        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.itemChatTemplate.active = false;
        if (this.edbMessage) {
            this.edbMessage.node.on("editing-return", () => {
                this.sendChat();
            });

            this.chatBox.active = true;
            this.chatTipBox.active = false;

            this.listGiftNode.removeAllChildren();
            const valueStr = App.instance.getTextLang("text_tri_gia");
            this.giftList.forEach((item) => {
                var giftNode = instantiate(this.giftTemplate);
                const key = item.key;
                const value = item.value;
                const name = App.instance.getTextLang(key);
                giftNode.getChildByName('ICON').getComponent(Sprite).spriteFrame = this.giftFrames[item.id - 1];
                giftNode.getChildByName('NAME').getComponent(Label).string = name;
                giftNode.getChildByName('VALUE').getComponent(Label).string = value;
                giftNode.on(Node.EventType.TOUCH_END, () => {
                    this.listGiftNode.children.forEach((child) => {
                        child.getChildByName('checkmark').active = false;
                    })

                    if (this.giftIdSelected == item.id) {
                        this.giftIdSelected = 0;
                        this.selectedTIP.active = false;
                        this.giftNodeSelected = null;
                        return;
                    }

                    giftNode.getChildByName('checkmark').active = true;
                    this.giftNodeSelected = giftNode;
                    this.giftIdSelected = item.id;
                    this.selectedTIP.active = true;
                    this.selectedTIP.getChildByName('ICON').getComponent(Sprite).spriteFrame = this.giftFrames[item.id - 1];
                    this.selectedTIP.getChildByName('TITLE').getComponent(Label).string = `TIP ${name}`;
                    this.selectedTIP.getChildByName('AMOUNT').getComponent(Label).string = `${valueStr}: ${value}`;
                });
                giftNode.active = true;
                giftNode.parent = this.listGiftNode;
            });
        }

        ChatHubSignalRClient.getInstance().registerChat(this.defaultChatChannel, (res) => {
            if (this.edbMessage == null) return;
            if (res.c < 0) {
                this.edbMessage.placeholderLabel.string = App.instance.getTextLang(`me${res.c}`);
                this.edbMessage.placeholderLabel.enableWrapText = true;
                this.edbMessage.enabled = false;
                this.edbMessage.node.getComponentInChildren(Button).enabled = false;

                return;
            }

            this.edbMessage.placeholderLabel.string = App.instance.getTextLang("TLN_ENTER_CHAT");
            this.edbMessage.enabled = true;
            this.edbMessage.node.getComponentInChildren(Button).enabled = true;
        });

        ChatHubSignalRClient.getInstance().receiveChat((response) => {
            response.forEach((item: any) => {
                if (item.i !== this.defaultChatChannel) {
                    return;
                }

                this.addMessage(item);
            });
        });

        ChatHubSignalRClient.getInstance().receiveLastMessages((response) => {
            this.scrMessage?.content.removeAllChildren();
            response.forEach((item: any) => {
                if (item.i !== this.defaultChatChannel) {
                    return;
                }

                if (item.v == 1) {
                    var tipId = item.c.split("|")[3] || -1;
                    if (tipId <= 0) return;
                    this.addMessage(item, tipId);
                } else if (item.v == 0) {
                    this.addMessage(item);
                }
            });
        });

        ChatHubSignalRClient.getInstance().receiveTIP((response) => {
            response.forEach((item: any) => {
                if (item.i !== this.defaultChatChannel) {
                    return;
                }

                var tipId = item.c.split("|")[3] || -1;
                if (tipId <= 0) return;
                this.addMessage(item, tipId);

                if (this.tipIconTemplate == null) return;
                if (`${item.a}:${item.p}` !== `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) return;

                let videoLive: Node = null;
                if (this.gameId == Configs.InGameIds.DragonTigerLive) {
                    videoLive = RongHoLiveController.instance.liveNode;
                } else if (this.gameId == Configs.InGameIds.RouletteLive) {
                    videoLive = RouletteLiveController.instance.liveNode;
                } else {
                    videoLive = XocDiaLiveController.instance.liveNode;
                }

                let worldPosStart = this.tipIconTemplate.worldPosition;
                let parent = this.node.parent.parent;
                let localPosStart = parent.getComponent(UITransform).convertToNodeSpaceAR(worldPosStart);

                var iconTip = instantiate(this.tipIconTemplate);
                iconTip.parent = parent;
                iconTip.getComponent(Sprite).spriteFrame = this.giftFrames[tipId - 1];
                iconTip.setPosition(localPosStart);
                iconTip.active = true;
                let worldPosEnd = videoLive.worldPosition;
                let localPosEnd = parent.getComponent(UITransform).convertToNodeSpaceAR(worldPosEnd);

                tween(iconTip)
                    .to(2, { position: localPosEnd }, { easing: 'cubicOut' })
                    .call(() => {
                        iconTip.destroy();
                    })
                    .start();
            });
        });
    }

    show() {
        this.node.active = true;
    }

    hide() {
        this.node.parent.active = false;
    }

    addMessage(data: any, tipId: number = -1) {
        let item: Node = null;
        for (var i1 = 0; i1 < this.scrMessage.content.children.length; i1++) {
            let node = this.scrMessage.content.children[i1];
            if (!node.active) {
                item = node;
                break;
            }
        }
        if (item == null) {
            if (this.scrMessage.content.children.length >= 50) {
                item = this.scrMessage.content.children[0];
            } else {
                item = instantiate(this.itemChatTemplate);
            }
        }
        var zIndex = 0;
        for (var i2 = 0; i2 < this.scrMessage.content.children.length; i2++) {
            let node = this.scrMessage.content.children[i2];
            if (node != item) {
                node.setSiblingIndex(zIndex++);
            }
        }
        item.parent = this.scrMessage.content;
        var msg = `<color=#ffffff>${data.c}</color>`;
        if (tipId > 0) {
            msg = `<color=#fff000>${App.instance.getTextLang('tipped')}</color> <img src='${tipId}' width=40 height=40 alt=''/>`;
        }
        if (data.v >= 6) {
            item.getComponent(RichText).string = msg;
        } else if (`${data.a}:${data.p}` === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
            item.getComponent(RichText).string = `<color=#fff600>${data.n}: ${msg}</c>`;
        } else {
            item.getComponent(RichText).string = `<color=#6bfb01>${data.n}: ${msg}`;
        }

        item.active = true;
        item.setSiblingIndex(zIndex++);
        this.scrollToBottom();
    }

    sendChat() {
        let msg = this.edbMessage.string.trim();
        if (msg.length == 0) {
            return;
        }
        this.edbMessage.string = "";

        ChatHubSignalRClient.getInstance().sendChat(this.defaultChatChannel, msg, (_response) => {
            this.edbMessage.focus();
        });
    }

    scrollToBottom() {
        this.scrMessage.scrollToBottom(0.2);
    }

    toggleChatTip() {
        this.chatTipBox.active = !this.chatTipBox.active;
        this.chatBox.active = !this.chatBox.active;
    }

    actTIP() {
        ChatHubSignalRClient.getInstance().sendTIP(this.defaultChatChannel, `${this.gameId}|${this.giftIdSelected}`, (response) => {
            if (response.c < 0) {
                this.showToast(App.instance.getTextLang(`me${response.c}`));
            }

            if (this.giftNodeSelected) {
                this.giftNodeSelected.emit(Node.EventType.TOUCH_END);
            }

            this.chatBox.active = true;
            this.chatTipBox.active = false;

            if (response.c < 0) return;

            XocDiaLiveController.instance?.updateBalance();
            RongHoLiveController.instance?.updateBalance();
            RouletteLiveController.instance?.updateBalance();
        });
    }

    showToast(msg: string) {
        this.lblToast.string = msg;
        this.lblToast.node.active = true;

        setTimeout(() => {
            this.lblToast.node.active = false;
        }, 2000);
    }
}