[{"__type__": "cc.Prefab", "_name": "RoulettePopupHistory", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "RoulettePopupHistory", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 357}], "_active": true, "_components": [{"__id__": 508}, {"__id__": 510}, {"__id__": 512}, {"__id__": 514}, {"__id__": 516}], "_prefab": {"__id__": 518}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbKaTcM2dKgqph2n9FkwUA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1080, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b8PPrSk5CA6tjv53HRcZJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 128, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afkor4VQ1PcpBHNYW75g3o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 1919.9999999999998, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6rWnwJedP24HNP8N+KZq4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6Vx1UT1FOm7l6QcC1jmlg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 25}, {"__id__": 38}, {"__id__": 108}, {"__id__": 230}], "_active": true, "_components": [{"__id__": 350}, {"__id__": 352}, {"__id__": 354}], "_prefab": {"__id__": 356}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": 2.842170943040401e-14, "y": 350, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "LỊCH SỬ ", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 37.6875, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00zY/ZH6JCvpiQThdPl1eK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 9, "_target": null, "_left": 503.93607766829916, "_right": 0, "_top": 48.10000000000002, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1t/f6hmFAboA/qAKktpNi"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "id": "pr1", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdeanA53tIAqOI0e9sRjcn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 21}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bomrbxv5FxJtNVA1ssUHo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 111.12784466340173, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f47F85yqVIQJK8HUrqiz/w"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcEjD2GOVDnq/XQGL4wQ3R", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ButtonClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 28}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 468.9190000000001, "y": 343.363, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 27}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7a782e51-5907-4722-b5aa-e93a8b6e57cc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0RqzHO/5FMLoGHSHxALN2"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 29}, "clickEvents": [{"__id__": 30}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 25}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cCFK70v9F2LLxb8MNXKMq"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "f8a59+MmfFAUqVceDsVUr5u", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 32}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 9.58099999999996, "_top": 20.637, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "af17RuFxZPVrysz3Ghctvq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 34}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3Zh3l1N5Ln6CgoWirv5xo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbHm/2FT9JWqrzUA71NR82"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bdFfA/7xJG6IQ2owI+jBJY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 39}, {"__id__": 49}, {"__id__": 59}, {"__id__": 69}, {"__id__": 79}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}, {"__id__": 105}], "_prefab": {"__id__": 107}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 243.05, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Session", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 40}, {"__id__": 42}, {"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 48}, "_lpos": {"__type__": "cc.Vec3", "x": -449.5833333333333, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 41}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c8MKfe7BKlqYxhaMDJKDL"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 43}, "id": "txt_session", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69/3cRKQZMLLwbUtfqjGWo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 45}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16wYSZV6xA+rUEJ0AWTVpC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 47}, "_contentSize": {"__type__": "cc.Size", "width": 179.83333333333334, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7x7e4CHhBQ54I+FXC1jlW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "108DQJ+QNNgY43pG04guKb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 50}, {"__id__": 52}, {"__id__": 54}, {"__id__": 56}], "_prefab": {"__id__": 58}, "_lpos": {"__type__": "cc.Vec3", "x": -269.74999999999994, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 51}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8edlWAjXhI7YcqwsN5Tvg+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 53}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edKtI7XZ1HzqmbjhZmZILV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 79.76408241993158, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36OUA9/DREHqWsG9nNNb7H"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 49}, "_enabled": true, "__prefab": {"__id__": 57}, "id": "TITLE_TIME", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39p+uopitBaKvLRU46LVKi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5WdgOjItBy5OEvtrIaB0g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Result", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}], "_prefab": {"__id__": 68}, "_lpos": {"__type__": "cc.Vec3", "x": -89.91666666666659, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 61}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON><PERSON> qu<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "de6+TNdEdPc5J5Cl4v6d0p"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 63}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a3sJRuUBD/qD+bMW2sh5v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 65}, "_contentSize": {"__type__": "cc.Size", "width": 66.4267286613806, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eenMYr4FpBobqDaeEXCaPB"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 67}, "id": "tx21", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aKLdLu6JNk7qvwNkkrG6C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fPxbW6m9O65cfzf04esGt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Bet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 70}, {"__id__": 72}, {"__id__": 74}, {"__id__": 76}], "_prefab": {"__id__": 78}, "_lpos": {"__type__": "cc.Vec3", "x": 89.91666666666676, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 71}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "Đặt", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fe/vo1/xOuKWU+/J/CrWl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 73}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2crUOOBo5ALJjMtEafe+yE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 75}, "_contentSize": {"__type__": "cc.Size", "width": 31.313755927394276, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddk8CSu3hJEbuI5aQEBH+N"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 69}, "_enabled": true, "__prefab": {"__id__": 77}, "id": "tx22", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a8/Frkx5OgpwQrvohyJq8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3MH52vRVDCrRslUiwgz1s", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Win", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": 269.7500000000001, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 81}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "Nhậ<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91WAb41ddBj5QYx7cn8+Fj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 83}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9GwM3NMBKkpXWmJiAmTzm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 85}, "_contentSize": {"__type__": "cc.Size", "width": 45.57090038090796, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54oTBp0RZFtLwpegl+9tz4"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 79}, "_enabled": true, "__prefab": {"__id__": 87}, "id": "tx24", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efcFxYfAVP7YQclBupfYld"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89hZfu+g9I1bSQCNA1UMgr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Detail", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}, {"__id__": 94}, {"__id__": 96}], "_prefab": {"__id__": 98}, "_lpos": {"__type__": "cc.Vec3", "x": 449.5833333333335, "y": 3.623, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 91}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bgkZRT3VGY5LMwOjs4WEb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 93}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08a5FHi0RA45rwinkRWwyr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 95}, "_contentSize": {"__type__": "cc.Size", "width": 50.649912060789795, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daPWzS3DFCNoe7e9z4MVhT"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 97}, "id": "TITLE_DESCRIPTION", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73iUhpYidID66apyA+0I30"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02h1SrID9HW5GNOGZzxz99", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 100}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ep9e9NfxMy402M1+Y3+X8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 102}, "_alignFlags": 40, "_target": null, "_left": 20, "_right": 20, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1572, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddZ0qV/+lF+LNXs3cD3mUD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 104}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90SCsSyAlEYpq4I/XOBtKC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 1079, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02dXfYRbxAhZN2l6XRcW/R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70qOsJDIJIraVe5J0wjCiv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "scrList", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 109}], "_active": true, "_components": [{"__id__": 221}, {"__id__": 223}, {"__id__": 225}, {"__id__": 227}], "_prefab": {"__id__": 229}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 233.21299999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 108}, "_children": [{"__id__": 110}], "_active": true, "_components": [{"__id__": 210}, {"__id__": 212}, {"__id__": 214}, {"__id__": 216}, {"__id__": 218}], "_prefab": {"__id__": 220}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -277.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [{"__id__": 111}], "_active": true, "_components": [{"__id__": 201}, {"__id__": 203}, {"__id__": 205}, {"__id__": 207}], "_prefab": {"__id__": 209}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 251, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "prefab", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [{"__id__": 112}], "_active": true, "_components": [{"__id__": 196}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 111}, "_children": [{"__id__": 113}, {"__id__": 123}, {"__id__": 133}], "_active": false, "_components": [{"__id__": 191}, {"__id__": 193}], "_prefab": {"__id__": 195}, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 112}, "_children": [], "_active": true, "_components": [{"__id__": 114}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 115}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0138aab9-b9d8-44ec-86ce-2e51ea57a2a3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32Bg5PzPJIy6bc4+AOW3c+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 117}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1623, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cn1jr0LNEiprJoT+AfHj7"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 119}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67MDL0tGdFvJAgJqzgpYPl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 113}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 1060, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36tcfCYm5Nt4sjy/HCtZGU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dx6BKtENCjLwtpyYwASuP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 112}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}, {"__id__": 128}, {"__id__": 130}], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 125}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dd78f5c1-8278-4ec6-b30b-ebf90deb1f1e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a6lj7JHtNSp5WLiJ4Wt4o"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 127}, "_alignFlags": 40, "_target": null, "_left": -5.684341886080802e-14, "_right": 5.684341886080802e-14, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1623, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29a1kQUVdJeoBxeFUeJZXK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 129}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42wPx95TRJq6oRRDyM3d/f"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 1060, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8eyK3JmVCZoT9VmIRChBA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8HCLO4XdEwpTFHz/oiV/4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 112}, "_children": [{"__id__": 134}, {"__id__": 142}, {"__id__": 150}, {"__id__": 158}, {"__id__": 166}, {"__id__": 174}], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}, {"__id__": 188}], "_prefab": {"__id__": 190}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lblSession", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}, {"__id__": 139}], "_prefab": {"__id__": 141}, "_lpos": {"__type__": "cc.Vec3", "x": -450, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 233, "g": 229, "b": 248, "a": 255}, "_string": "123456789", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8chiLmq5BHZa3nkMBJZxI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8rU9rXNpMpb8fecW5pm0W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 140}, "_contentSize": {"__type__": "cc.Size", "width": 89.78207108986318, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bALEdKSVGTZKOklnlsAMG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b39i31r3tCPLFoQr7mleyn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lblTime", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 143}, {"__id__": 145}, {"__id__": 147}], "_prefab": {"__id__": 149}, "_lpos": {"__type__": "cc.Vec3", "x": -270, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 144}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 233, "g": 229, "b": 248, "a": 255}, "_string": "THỜI GIAN", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "634uUT6KFEd6v+ApmIosX2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 146}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbOu8aBtZORq7nEamiK8WC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 142}, "_enabled": true, "__prefab": {"__id__": 148}, "_contentSize": {"__type__": "cc.Size", "width": 89.1621968283582, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75jl2bwO5E4L34FvZdsyZb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52KtKP+1JHka16+fSXf1dp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lblResult", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 151}, {"__id__": 153}, {"__id__": 155}], "_prefab": {"__id__": 157}, "_lpos": {"__type__": "cc.Vec3", "x": -90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 152}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 233, "g": 229, "b": 248, "a": 255}, "_string": "KẾT QUẢ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7vufo9kxPxpNXUYiZRWip"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 154}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eKGcRbXdKaJfnzon9RKgG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 150}, "_enabled": true, "__prefab": {"__id__": 156}, "_contentSize": {"__type__": "cc.Size", "width": 76.14480089785447, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3d4swvq+hA6Y0tk2NHQ4MZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6tG5oHV9GhZnhYJwgWY0t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lblBet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 159}, {"__id__": 161}, {"__id__": 163}], "_prefab": {"__id__": 165}, "_lpos": {"__type__": "cc.Vec3", "x": 90, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 160}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0f/v3S6ftD55sLG6SdTNxA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 162}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc9yNgtxVL1Idr02RFyTNP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 164}, "_contentSize": {"__type__": "cc.Size", "width": 11.297747104322138, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bTMZVMnhM84fPzn/8ipLb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1axA7FHFBaIADCWmbOnu8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lblPrize", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 167}, {"__id__": 169}, {"__id__": 171}], "_prefab": {"__id__": 173}, "_lpos": {"__type__": "cc.Vec3", "x": 270, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 168}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cS+aUFddPnon5O8VZn0X9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 170}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "911b70Gu9FZo7Yvck1HwmE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 172}, "_contentSize": {"__type__": "cc.Size", "width": 11.297747104322138, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57fpzOMSxNiKNOvNYTnVnj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "71PzEDd9ZISYj6+3MdmT8x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "detail", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 175}, {"__id__": 177}, {"__id__": 179}, {"__id__": 181}], "_prefab": {"__id__": 183}, "_lpos": {"__type__": "cc.Vec3", "x": 450, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 255, "b": 234, "a": 255}, "_string": "<PERSON> ti<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "98c0d9fc-a17c-4e37-ba0d-6d24c86d3ec6", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2eyMfV+hC57IO6/cbvLlG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 178}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e0qQZivRGt6mMX2u9uKAs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 180}, "_contentSize": {"__type__": "cc.Size", "width": 62.1875728777985, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6hTI5W8dDRo1ipmcDYQ5G"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 174}, "_enabled": true, "__prefab": {"__id__": 182}, "id": "sl43", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21ke030ShOU6vZMboz9At+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28IjfKt6ZDtKLiVY4cIU9f", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 185}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83rErzA85DyI3CmOmUCVMV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 187}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26SZLMFQdHxZBqV8IZ+azt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 189}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f5/L9+yZPOpQCutvp7hNF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0bdlW75TlAfrRKnlFKG612", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": {"__id__": 192}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72yx82UDtJTpaX7iiHFDDk"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 112}, "_enabled": true, "__prefab": {"__id__": 194}, "_contentSize": {"__type__": "cc.Size", "width": 1060, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aOf+sTJZFM7n9toruecZq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8L8yOYTNOJJDznF59Vh2L", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 197}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0amBIy4GBDvLkmJgNatXn7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 199}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fP3sWbBFF9oNXncTSPang"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccIwc/nHBDGrPgMuEM84RX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 202}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 26.5, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 893, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63Ib5J4nxBZ4lxI5e1NVVs"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 204}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5QRlAfx1O04oeE4K0xJSe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 206}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15lJQNAXFL9rXk4TpnJjnX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 208}, "_contentSize": {"__type__": "cc.Size", "width": 1029.8, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.47}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fOXCZAHlJ1pgRSBuvRvJj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fRBot3ztFrqHsWP+NUnv3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 211}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4w73Q22JJ3rhikltjzRb+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 213}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 943, "_originalHeight": 400, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0ewiE9ZpN+o0z+5X76aaT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 215}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1ikQtjkRFvpfMFyqpfvVD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 217}, "_contentSize": {"__type__": "cc.Size", "width": 1029.8, "height": 555}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beSqKqkGxA0rAOZJyHjzsQ"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 219}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43Wg1W/69GlpYWjjN7NDMa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1YEjG8zlL/LjV16dsduAN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 222}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 110}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88uNpbdztAQ4i2NbekpsLJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 224}, "_alignFlags": 45, "_target": null, "_left": 44.600000000000044, "_right": 44.600000000000016, "_top": 183.78700000000006, "_bottom": 95.21299999999998, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1006, "_originalHeight": 415, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4eC+h711CuqpRJUC/JTsG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 226}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8rtUvjjRJHJjcSt6uWSKX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 228}, "_contentSize": {"__type__": "cc.Size", "width": 1029.8, "height": 555}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81auzj7FVKYbe6ptN835r0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4W072925E4YQJrp3xOWyE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "paginationContainer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 231}, {"__id__": 247}, {"__id__": 266}, {"__id__": 306}, {"__id__": 325}], "_active": true, "_components": [{"__id__": 341}, {"__id__": 343}, {"__id__": 345}, {"__id__": 347}], "_prefab": {"__id__": 349}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -367, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnPreLast", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [{"__id__": 232}], "_active": true, "_components": [{"__id__": 240}, {"__id__": 242}, {"__id__": 244}], "_prefab": {"__id__": 246}, "_lpos": {"__type__": "cc.Vec3", "x": -202.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 233}, {"__id__": 235}, {"__id__": 237}], "_prefab": {"__id__": 239}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 234}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "12e2cc10-7ab1-4218-8a90-3170f1571c67@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eUKMVhYFLP59t+LdyNIiq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 236}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "536Ex9bJdCJ52UeUOD3HEb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 238}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9lYrUPdJGdKzezWnn5vFe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cIRVoRmNPH5zvOmrrWR9C", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 241}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3bec0c7e-32b3-4366-be72-906ad5cd4d4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96foiFhoZDtoSDs921GRdG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 243}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11YD7rhshF17skqcnMmvWu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 245}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b74KhMIWBLRarzZsh9srRS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1XHtAYxZCn7Ww6Mf4H/wE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnPre", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [{"__id__": 248}], "_active": true, "_components": [{"__id__": 256}, {"__id__": 258}, {"__id__": 261}, {"__id__": 263}], "_prefab": {"__id__": 265}, "_lpos": {"__type__": "cc.Vec3", "x": -121.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 247}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 250}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ccee1232-246b-4f2c-9e3a-34f1c15d94ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aRM6zgfpINpRYB5dJsptW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 252}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23Mkg6dRtIX45umQsT+0Va"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 254}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5faVLPwFBTYbWC1nXJmQp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5fjoKEilxJCqr7Jkx5GnOk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 257}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3bec0c7e-32b3-4366-be72-906ad5cd4d4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dj/T3xlVG+78twpslj9lR"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 259}, "clickEvents": [{"__id__": 260}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 247}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bf5nuona9JArIs54v3TFwY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "025abAO3LFGoLkkuQts3VcB", "handler": "onPrevPage", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 262}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adOTyKC75NgIrDFQ1YP78Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 264}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daBsOg7lpCSYLyYzdlUZl4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27PZb2Dd1Jxb/54IzcL9zu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "listPage", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [{"__id__": 267}, {"__id__": 283}], "_active": true, "_components": [{"__id__": 299}, {"__id__": 301}, {"__id__": 303}], "_prefab": {"__id__": 305}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "btnPaginationActive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 268}], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}, {"__id__": 280}], "_prefab": {"__id__": 282}, "_lpos": {"__type__": "cc.Vec3", "x": -40.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 270}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 37.6875, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "1b4debab-0a3c-4188-b920-5af7df3c4aaa", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01PPY/RWNFdo2HWw3K0waF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 272}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aoushgthEk59cUHHdNS9M"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 274}, "_contentSize": {"__type__": "cc.Size", "width": 10.61788226834577, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25nyyFxlRIip5LOeZSlDqv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dL43P+UhC3bsnPeRsmcip", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 277}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "24350126-94bb-4af7-8dd9-d137ca66748e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92fp5PgshETpSzH5ESoUE5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 279}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2qGQlZWlAVJBzG6D9+E5p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 281}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7uNy8llNLKLC54tXK/EPC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "42sZA7bHpATLJGUi4C9kZh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnPaginationInActive", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 266}, "_children": [{"__id__": 284}], "_active": true, "_components": [{"__id__": 292}, {"__id__": 294}, {"__id__": 296}], "_prefab": {"__id__": 298}, "_lpos": {"__type__": "cc.Vec3", "x": 40.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 283}, "_children": [], "_active": true, "_components": [{"__id__": 285}, {"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 286}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "2", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 37.6875, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "1b4debab-0a3c-4188-b920-5af7df3c4aaa", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bkop2cYVG0ZDmrL6yre9m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 288}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30TW9EVGlPSb5eYKRvveS3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 284}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 16.796656366604477, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "936WU6iKZL/7er10jHQM10"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "13Cqypo4JO5YLookfIIX+j", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": {"__id__": 293}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3bec0c7e-32b3-4366-be72-906ad5cd4d4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4cFE47atICpCSEgw9ZlKl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": {"__id__": 295}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5Yzk3o3FLfJcH2JpxyHMv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 283}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64zZK17tBBapFXCPEQVrxY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fxLAsRFBKx7oNeafUCoHZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": {"__id__": 300}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 20, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c05DICGdhGxaBHrxE4VMkH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": {"__id__": 302}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00RfG5XiBFeq1AdgM9Qt+e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 266}, "_enabled": true, "__prefab": {"__id__": 304}, "_contentSize": {"__type__": "cc.Size", "width": 142, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edvpi4wZJIcbrpI6fCNpae"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dd3xdlFHJB2YYmYsig/Paj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnNext", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [{"__id__": 307}], "_active": true, "_components": [{"__id__": 315}, {"__id__": 317}, {"__id__": 320}, {"__id__": 322}], "_prefab": {"__id__": 324}, "_lpos": {"__type__": "cc.Vec3", "x": 121.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 306}, "_children": [], "_active": true, "_components": [{"__id__": 308}, {"__id__": 310}, {"__id__": 312}], "_prefab": {"__id__": 314}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "70fd2cc3-0693-4d65-b1fc-cd6cac4f55e2@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cLx+bEb9BZYWA4ZlA04cI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 311}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4ix33ZcdK6IBt+54sMc7d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 307}, "_enabled": true, "__prefab": {"__id__": 313}, "_contentSize": {"__type__": "cc.Size", "width": 16, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ddLlLu85Nba+8KqA3Nm6n"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6ekO/b4W1ARZqDUU/2sq/H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": {"__id__": 316}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3bec0c7e-32b3-4366-be72-906ad5cd4d4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdf2bbP1tJRb3QvTJ1EuAV"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": {"__id__": 318}, "clickEvents": [{"__id__": 319}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0xis3vwhHArMTOl6sBSTD"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "f8a59+MmfFAUqVceDsVUr5u", "handler": "onNextPage", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": {"__id__": 321}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac2DV/pfVDcas9urE2KIgi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 306}, "_enabled": true, "__prefab": {"__id__": 323}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afs8VcYb1Gs6aZNCQGJr4Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f76TIdNndMJIqgiaoqKE1g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "btnNextLast", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 230}, "_children": [{"__id__": 326}], "_active": true, "_components": [{"__id__": 334}, {"__id__": 336}, {"__id__": 338}], "_prefab": {"__id__": 340}, "_lpos": {"__type__": "cc.Vec3", "x": 202.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "icon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 325}, "_children": [], "_active": true, "_components": [{"__id__": 327}, {"__id__": 329}, {"__id__": 331}], "_prefab": {"__id__": 333}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5.749, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 328}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3c8a49ae-3ac6-4a88-a54c-d329bea885ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10Z7Y+4yJE7L74rhOQM2oe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 330}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3xE0+OodO9Jgmazz6losa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 326}, "_enabled": true, "__prefab": {"__id__": 332}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bBVI8fxhAO4PzU17aL3dF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29CD6D+upDRon/++QWfnfu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": {"__id__": 335}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3bec0c7e-32b3-4366-be72-906ad5cd4d4e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65FeIJY0RGaq81isM5+nat"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": {"__id__": 337}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7JMDoqAdIf7/c5TVBtXcM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 325}, "_enabled": true, "__prefab": {"__id__": 339}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eNAh1lcRIooW/TtSfONC+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56cNJvGzZMLYqGsXIZ8uRU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 342}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 10, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92DMuH1oNCE65Bt/qo+A0C"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 344}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 20, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54ilyEr95Gy4OVuXMFzyoz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 346}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "862Ugo3ABOmrlyO8USwHyv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 230}, "_enabled": true, "__prefab": {"__id__": 348}, "_contentSize": {"__type__": "cc.Size", "width": 466, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87M+RvEhxOZZUNOAgc1xaN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39VPXbXoxEj5L293OkmO1P", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 351}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "73dfca6e-5b1b-4d76-9aa4-6cf55d751908@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62LnH5Y69C0as/es+aWEuY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 353}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b84N9hIJFEtom+H/+qM5mi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 1119, "height": 834}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72IKhdSChCgK4kXWjaD513"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7FUS/R7FOpJNqv3E15fBh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "DetaiHistory", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 358}, {"__id__": 380}, {"__id__": 420}, {"__id__": 482}], "_active": false, "_components": [{"__id__": 503}, {"__id__": 505}], "_prefab": {"__id__": 507}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 225, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "note", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 359}, {"__id__": 365}], "_active": true, "_components": [{"__id__": 377}], "_prefab": {"__id__": 379}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "session", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [], "_active": true, "_components": [{"__id__": 360}, {"__id__": 362}], "_prefab": {"__id__": 364}, "_lpos": {"__type__": "cc.Vec3", "x": -485, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 361}, "_contentSize": {"__type__": "cc.Size", "width": 97.9804590329602, "height": 30.239999999999995}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5njfGKahMYIuwU+N63lBw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 363}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Session #0", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06TAaoHT5MUKPbjwfBNvtO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72yHAdoHBCWamSwAsQZRui", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "result", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [{"__id__": 366}], "_active": true, "_components": [{"__id__": 372}, {"__id__": 374}], "_prefab": {"__id__": 376}, "_lpos": {"__type__": "cc.Vec3", "x": -450, "y": -100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 365}, "_children": [], "_active": true, "_components": [{"__id__": 367}, {"__id__": 369}], "_prefab": {"__id__": 371}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 368}, "_contentSize": {"__type__": "cc.Size", "width": 17.20848880597015, "height": 50.39999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66LnscfWtL1r1fZTicB/ZI"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 366}, "_enabled": true, "__prefab": {"__id__": 370}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 37.6875, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18TykjwW5JFphBLsXkLJ02"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6IQktIOtMDoCYmxtOuIU8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 373}, "_contentSize": {"__type__": "cc.Size", "width": 48, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24kxls+YdNlJx9bDBUgA+8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 365}, "_enabled": true, "__prefab": {"__id__": 375}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "37251e9b-ddb4-4ba1-86c2-fbcd2b22dc2b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37h9aFCqZHQrqfj4+QmdiD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33hpXG8u1LuKa80x7Uwzsp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 378}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2edVslttEbYj3GiPomreR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9aYoSCpNCMrlQ8n0CoGa3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 381}, {"__id__": 411}], "_active": true, "_components": [{"__id__": 417}], "_prefab": {"__id__": 419}, "_lpos": {"__type__": "cc.Vec3", "x": 190, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [{"__id__": 382}, {"__id__": 390}, {"__id__": 398}], "_active": true, "_components": [{"__id__": 406}, {"__id__": 408}], "_prefab": {"__id__": 410}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "gate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 383}, {"__id__": 385}, {"__id__": 387}], "_prefab": {"__id__": 389}, "_lpos": {"__type__": "cc.Vec3", "x": -233.33333333333331, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 384}, "_contentSize": {"__type__": "cc.Size", "width": 66.56670261971392, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adiJ4ldA1JM721KKHxOtSU"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 386}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON><PERSON> đặt", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bNC7m8xhFO5reMsRsKrd/"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 382}, "_enabled": true, "__prefab": {"__id__": 388}, "id": "txt_taixiu_gate", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3coXoC82JG3qqZsoknHHeX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2dycy0HW5FpKUashtDXprd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 391}, {"__id__": 393}, {"__id__": 395}], "_prefab": {"__id__": 397}, "_lpos": {"__type__": "cc.Vec3", "x": 2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 392}, "_contentSize": {"__type__": "cc.Size", "width": 75.84486017179727, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a83ZvVsVERqUa8wc5TmDS"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 394}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "Tổng đặt", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15XpijcQxNd7GFDG1sTik2"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 396}, "id": "sl4", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeAz+uzS1CL4/xygkaJXn0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "221SRLH8FP5Z79nap/4NEC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "prize", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 381}, "_children": [], "_active": true, "_components": [{"__id__": 399}, {"__id__": 401}, {"__id__": 403}], "_prefab": {"__id__": 405}, "_lpos": {"__type__": "cc.Vec3", "x": 233.33333333333337, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 400}, "_contentSize": {"__type__": "cc.Size", "width": 92.82146396921641, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99j90fZoZENZV7i58d1jrw"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> thắng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcQi2lw/NCCbzZgQi3tFhO"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 398}, "_enabled": true, "__prefab": {"__id__": 404}, "id": "hi17", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b68CSPYPxO+rSlGsH4bxRS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43j98Jh9dJg7LZlu1P1uzW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 407}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3w2IaDBdF0qa2y5qZ5JlE"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 409}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8de5+LuNKy6Gzvwa181yK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5aWqBhqhJj6TP/W6PiQ+1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 380}, "_children": [], "_active": true, "_components": [{"__id__": 412}, {"__id__": 414}], "_prefab": {"__id__": 416}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 411}, "_enabled": true, "__prefab": {"__id__": 413}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdsaqwVe5ALorakCoDi9S5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 411}, "_enabled": true, "__prefab": {"__id__": 415}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42t/93G2JOr6KBOu7x+1ht"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "46MuqYqZ9BdJDggXYCLavv", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 418}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31uWEs0apDj541eDaqURoo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0+RGmv75FtaBUcHuIkRMN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "scroll", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 421}], "_active": true, "_components": [{"__id__": 473}, {"__id__": 475}, {"__id__": 477}, {"__id__": 479}], "_prefab": {"__id__": 481}, "_lpos": {"__type__": "cc.Vec3", "x": 190, "y": -200, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 420}, "_children": [{"__id__": 422}], "_active": true, "_components": [{"__id__": 462}, {"__id__": 464}, {"__id__": 466}, {"__id__": 468}, {"__id__": 470}], "_prefab": {"__id__": 472}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "list", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 421}, "_children": [{"__id__": 423}], "_active": true, "_components": [{"__id__": 457}, {"__id__": 459}], "_prefab": {"__id__": 461}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "template", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 422}, "_children": [{"__id__": 424}, {"__id__": 448}], "_active": false, "_components": [{"__id__": 454}], "_prefab": {"__id__": 456}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 423}, "_children": [{"__id__": 425}, {"__id__": 431}, {"__id__": 437}], "_active": true, "_components": [{"__id__": 443}, {"__id__": 445}], "_prefab": {"__id__": 447}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "gate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 426}, {"__id__": 428}], "_prefab": {"__id__": 430}, "_lpos": {"__type__": "cc.Vec3", "x": -216.66666666666669, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": {"__id__": 427}, "_contentSize": {"__type__": "cc.Size", "width": 216.66666666666666, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5YUEoVKtJYoZeTlpCnHA9"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 425}, "_enabled": true, "__prefab": {"__id__": 429}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_string": "Jackpot", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b7yKKnIBC4LApAOk2mNaj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84nBUKluhGYY7Xq4fbUcdg", "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 432}, {"__id__": 434}], "_prefab": {"__id__": 436}, "_lpos": {"__type__": "cc.Vec3", "x": -2.842170943040401e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 433}, "_contentSize": {"__type__": "cc.Size", "width": 75.84486017179727, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16n2hXIRdM44Dzyv4uWs1H"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 435}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "Tổng đặt", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbz1PDF8NME6QqvbZSu9Nn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dNljU1i5MvpA07RX+t0fZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "prize", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 424}, "_children": [], "_active": true, "_components": [{"__id__": 438}, {"__id__": 440}], "_prefab": {"__id__": 442}, "_lpos": {"__type__": "cc.Vec3", "x": 216.66666666666663, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 439}, "_contentSize": {"__type__": "cc.Size", "width": 92.82146396921641, "height": 25.199999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eJTkhJ/ZBy4yS251qFxnQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 441}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> thắng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 25.125, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 20, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ft2QmNzhNJ7pUSJGSyKpX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "192QuKHyxONI3YEtyJbO5z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 444}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43U0TUD15Em4zqRASvmRHR"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 446}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16xTitX0BOCrKdbVNacRlD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62l8Xmwa9HkI4u1K9DeV3a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "line", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 423}, "_children": [], "_active": true, "_components": [{"__id__": 449}, {"__id__": 451}], "_prefab": {"__id__": 453}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 450}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5WeAn9epC6Zng8IsSOt69"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 452}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 160, "g": 147, "b": 207, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beyNOx3WdDp4omlZhN6WAe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "batdAvW9tGVpus0jMoYKW/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 423}, "_enabled": true, "__prefab": {"__id__": 455}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccIRA59PBBU5RRYfGuS50o"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8m6hsZAtB0abkYbh55phx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 422}, "_enabled": true, "__prefab": {"__id__": 458}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73zz9xCMhAgq2XSveb0Ywv"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 422}, "_enabled": true, "__prefab": {"__id__": 460}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3d12YQJMBLCJ0/9bWlZO3K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02iDR61WdIRb79fxTlpgS3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 463}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2jHEVSVhI0KJ3JGKQnlfV"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 465}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33JtXP2bRLf6NogOpp+jZC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 467}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0a7f31Y1BARq2vqb3Y1lYK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 469}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1TLK0C6RNBaYpmgLGkIf9"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 421}, "_enabled": true, "__prefab": {"__id__": 471}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceTGFTGnZODKCTqNTCGE3l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ead44C7XRLO70PhBHE02x+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 474}, "bounceDuration": 0.23, "brake": 0.75, "elastic": false, "inertia": false, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 422}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6wYlKiAdIJLQAfxPKwKz5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 476}, "_alignFlags": 45, "_target": null, "_left": 380.5, "_right": 0.5, "_top": 49.99999999999997, "_bottom": 100, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 850, "_originalHeight": 400, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68ghjgEHBBtbmbIcO0uVza"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 478}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6/SIq+L5MuI4+q7CkrRfq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 480}, "_contentSize": {"__type__": "cc.Size", "width": 650, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dNdRO3LBNebk28+tp1zFX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4RDTEKDBIJb2+OTDbCMIX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 483}], "_active": true, "_components": [{"__id__": 500}], "_prefab": {"__id__": 502}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -380, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Close", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 482}, "_children": [{"__id__": 484}], "_active": true, "_components": [{"__id__": 492}, {"__id__": 494}, {"__id__": 496}], "_prefab": {"__id__": 499}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 483}, "_children": [], "_active": true, "_components": [{"__id__": 485}, {"__id__": 487}, {"__id__": 489}], "_prefab": {"__id__": 491}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 486}, "_contentSize": {"__type__": "cc.Size", "width": 53.59941017568408, "height": 30.240000000000002}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8hGUCq3xN4ag/qNS1EmeP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 488}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Close", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27.637500000000003, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "372efeb8-2cad-455f-8a2d-b31f6ba265f5", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0N2zg8h5PcLcvAsG50pnu"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 490}, "id": "txt_close", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57Lk5EUkpAwak7r49d4m97"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c8m2Y4GS1I7LrqBHWsexYy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 483}, "_enabled": true, "__prefab": {"__id__": 493}, "_contentSize": {"__type__": "cc.Size", "width": 173, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13DiiKQ/FImo79zVA2HPVC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 483}, "_enabled": true, "__prefab": {"__id__": 495}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dd8e3ba2-15a6-451e-bb70-a8203f9a1b1c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8V2O8PnNP4r80vZb1B+Ql"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 483}, "_enabled": true, "__prefab": {"__id__": 497}, "clickEvents": [{"__id__": 498}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 483}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99Vs0ox+9AlZ8OKNyog8Lw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "f8a59+MmfFAUqVceDsVUr5u", "handler": "hideDetail", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdhLJ2q5ZKW5R9IThme6GC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 482}, "_enabled": true, "__prefab": {"__id__": 501}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 120}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0uWSi3rVFzKBj3+hz1MaY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "54WjfuXfZC7asnkX+fwTVi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 504}, "_contentSize": {"__type__": "cc.Size", "width": 1031, "height": 450}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eUk6AXiBEzrKFNrqjoJ9T"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 506}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "27fb79c5-23fd-4dae-9786-9d787d881da0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "744VNmdfVArIA6dm4rFsX/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f82oKqDPhCH6DN6gwSG/F/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 509}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31peD5INNCOb7BUyr7nhWc"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 511}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cej/XWrM9KvqE4hs8B7csp"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 513}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bWXpa5IhL2olLoGqb7nM3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 515}, "_contentSize": {"__type__": "cc.Size", "width": 1919.9999999999998, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95rGiJWDBKj5WDH7BZ5ywP"}, {"__type__": "f8a59+MmfFAUqVceDsVUr5u", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 517}, "bg": {"__id__": 2}, "container": {"__id__": 12}, "prefab": {"__id__": 112}, "scroll": {"__id__": 221}, "prevPage": {"__id__": 247}, "nextPage": {"__id__": 306}, "containerPage": {"__id__": 230}, "listPage": {"__id__": 266}, "activePage": {"__id__": 267}, "inactivePage": {"__id__": 283}, "detailSession": {"__id__": 357}, "lblDetailSession": {"__id__": 362}, "detailResultNode": {"__id__": 365}, "detailItemList": {"__id__": 422}, "detailItemTemplate": {"__id__": 423}, "resultSprites": [{"__uuid__": "37251e9b-ddb4-4ba1-86c2-fbcd2b22dc2b@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "17b775c2-09a1-4999-bb8c-9e63d1387c9a@f9941", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "68e3aef3-06de-47ce-b9d6-65f1ff330491@f9941", "__expectedType__": "cc.SpriteFrame"}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3p1wn8aFMlL1aLomPU6G0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3famVVTnBMmIR8IOESXUou", "instance": null, "targetOverrides": null}]