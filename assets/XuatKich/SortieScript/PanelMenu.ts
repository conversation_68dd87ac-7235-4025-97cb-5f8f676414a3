import { _decorator, Component, Node, Sprite, Vec3, tween, sys } from 'cc';
import Play from './Play';
const { ccclass, property } = _decorator;

@ccclass('PanelMenu')
export default class PanelMenu extends Component {

    public static instance: PanelMenu = null;

    @property(Node)
    arrow: Node = null;

    @property(Sprite)
    sprSoundMusicOn: Sprite = null;

    @property(Sprite)
    sprSoundMusicOff: Sprite = null;

    @property(Sprite)
    sprSoundBgOn: Sprite = null;

    @property(Sprite)
    sprSoundBgOff: Sprite = null;

    private isShow = false;
    private soundState = 1;
    private musicState = 1;

    onLoad() {
        PanelMenu.instance = this;
    }

    show(isShow: boolean) {
        this.isShow = isShow;

        if (this.isShow) {
            tween(this.node)
                .to(0.3, { position: new Vec3(-150, 0, 0) })
                .start();

            tween(this.arrow)
                .to(0.3, { angle: 180 })
                .start();
        } else {
            tween(this.node)
                .to(0.3, { position: new Vec3(300, 0, 0) })
                .start();

            tween(this.arrow)
                .to(0.3, { angle: 0 })
                .start();
        }
    }

    hide() {
        this.isShow = false;
        this.node.setPosition(new Vec3(300, 0, 0));
        this.arrow.angle = 0;
    }

    toggleShow() {
        this.show(!this.isShow);
    }

    getSound(): number {
        const soundSave = sys.localStorage.getItem("XK_sound_fish_shot");
        if (soundSave != null) {
            this.soundState = parseInt(soundSave);
        }
        return this.soundState;
    }

    getMusic(): number {
        const musicSave = sys.localStorage.getItem("XK_music_fish_shot");
        if (musicSave != null) {
            this.musicState = parseInt(musicSave);
        }
        return this.musicState;
    }

    toggleSoundMusic() {
        Play.instance.togglePlayMusic();
        const isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing") || "false");

        this.sprSoundBgOn.node.active = isPlaying;
        this.sprSoundBgOff.node.active = !isPlaying;
    }

    toggleSoundEffect() {
        let isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on") || "true");
        isEffectOn = !isEffectOn;
        sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(isEffectOn));

        // Assuming you use a global AudioSource or volume management utility
        const volume = isEffectOn ? 1 : 0;
        // You may want to call a central method like:
        // AudioManager.setEffectVolume(volume);

        this.sprSoundMusicOn.node.active = isEffectOn;
        this.sprSoundMusicOff.node.active = !isEffectOn;
    }

    updateSoundEffect() {
        const isEffectOn = JSON.parse(sys.localStorage.getItem("XK_is_effect_on") || "true");
        const isPlaying = JSON.parse(sys.localStorage.getItem("XK_is_playing") || "false");

        this.sprSoundBgOn.node.active = isPlaying;
        this.sprSoundBgOff.node.active = !isPlaying;
        this.sprSoundMusicOn.node.active = isEffectOn;
        this.sprSoundMusicOff.node.active = !isEffectOn;
    }
}