[{"__type__": "cc.Prefab", "_name": "Catte", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Catte", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 28}, {"__id__": 44}, {"__id__": 447}, {"__id__": 753}, {"__id__": 910}], "_active": true, "_components": [{"__id__": 932}, {"__id__": 934}, {"__id__": 936}, {"__id__": 938}, {"__id__": 940}], "_prefab": {"__id__": 942}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "87323e18-3343-45f2-ad23-bfea1d93a942@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Q1J2YkxB3JucuigRadb+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1920, "_originalHeight": 1080, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dse95IqRCIo/kF9ZEWHlg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adLtFQX2RLCLi5fSeUy0Rz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ed+w0+zr9Gz76s8PCTh985"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1eZroV+9KoqayxYSSP0jZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BackgroundVip", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}, {"__id__": 25}], "_prefab": {"__id__": 27}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bobai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}], "_prefab": {"__id__": 20}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 53, "b": 51, "a": 255}, "_string": "CATTE", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "788Qdj3qRDjrVRNhEaaMFJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_opacity": 76, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72t974lRtAqoJ36nTCes7W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 162.5390625, "height": 80.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acA9yfI61MHJa9+TVIN9r/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6cGR7qO6xN2aWZUd7njbd4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 22}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "29fed6fc-09b9-4515-8d9c-7af2fbbecf86@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3Lie2Nq1K9LwPj/1arqif"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 24}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1Q6Iy9RxJVYWGwe1dLxfO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 1617, "height": 745}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eTcYwtANIH4HGl0MWhJI3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7yDFYDHJCbbo+/kA9q2uY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BackgroundNormal", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 29}], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bobai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 28}, "_children": [], "_active": true, "_components": [{"__id__": 30}, {"__id__": 32}, {"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 81, "g": 59, "b": 33, "a": 255}, "_string": "CATTE", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4ede755a-d4bb-48f3-848d-e4aaf1ac74c0", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5SB4TigpAd7TboNn2wDze"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 33}, "_opacity": 76, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aIWi2ZB5IB7EMKcTWZ6GE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 162.5390625, "height": 80.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71MVMYFRhNXJY8wv7lk1Pf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5d0dPzs4FIr4YC94S0JX1A", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e8b970d-eaf6-4eaf-b29e-79358596b914@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36R2k8QXpLMbDfPkcNT2xk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eRIr+q49HgLa/PBh+juNe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 1616, "height": 745}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b5Ux9NGNHCYspwNc5gwX7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34ZrCCpipMEJA16Np0JZ7w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Game", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 45}, {"__id__": 55}, {"__id__": 158}, {"__id__": 232}, {"__id__": 303}, {"__id__": 375}], "_active": true, "_components": [{"__id__": 440}, {"__id__": 442}, {"__id__": 444}], "_prefab": {"__id__": 446}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "dealer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 44}, "_children": [], "_active": true, "_components": [{"__id__": 46}, {"__id__": 48}, {"__id__": 50}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 330, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 47}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1f1813af-0785-4a4c-b796-d54321fb1474@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7bf6IN6ZBeICMazdiuKwa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 49}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6biqb4WFtHDqFskPbUgEQZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 51}, "_contentSize": {"__type__": "cc.Size", "width": 260, "height": 390}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91EZ33ndNBMI4YP7nVfxsZ"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 53}, "playOnLoad": false, "_clips": [{"__uuid__": "22a47194-60c5-484d-aab0-67cc27bd13fb", "__expectedType__": "cc.AnimationClip"}, {"__uuid__": "dd950f4b-0078-497a-bafc-d4989f49cd87", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "22a47194-60c5-484d-aab0-67cc27bd13fb", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fUnKU4H5GdqVLoauAG8F9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81WyJC9KdMAqiLvqpcsuA4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 56}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 55}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 57}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bcOZNl7ZRKbrFKBg9k9dNH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 58}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 63}, {"__id__": 65}, {"__id__": 67}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 73}, {"__id__": 75}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}, {"__id__": 81}, {"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 97}, {"__id__": 99}, {"__id__": 101}, {"__id__": 103}, {"__id__": 105}, {"__id__": 107}, {"__id__": 109}, {"__id__": 111}, {"__id__": 113}, {"__id__": 115}, {"__id__": 117}, {"__id__": 118}, {"__id__": 119}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 123}, {"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 127}, {"__id__": 128}, {"__id__": 129}, {"__id__": 130}, {"__id__": 131}, {"__id__": 132}, {"__id__": 133}, {"__id__": 134}, {"__id__": 135}, {"__id__": 136}, {"__id__": 137}, {"__id__": 138}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}, {"__id__": 143}, {"__id__": 144}, {"__id__": 146}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 155}, {"__id__": 157}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_name"], "value": "SlotItem1"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -330, "y": -245, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 64}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 125, "y": 130, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 125, "y": -35, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["831fTUJ3tPHpreXJK7iBEo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_font"], "value": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 64}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 175, "y": -10, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["bbLdfwIaFLCo3zH41Je1Ov"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 65, "height": 200}}, {"__type__": "cc.TargetInfo", "localID": ["34JoavnIBPP5C+gS/n8c8s"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["926lzFEP1B0bM8Lbcr/1mo"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["b6nVjTwNdLcK5snlWTG4Tr"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a5T4Z1c6dInK9NZd8GQfpl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 90}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["63UJuEp2VEwYYWf3ayu/HY"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["0cT2gczOFAIJM2G5CE7x8c"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 100}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 106}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["bcLRFwVRhPB5kG3DSYPPWI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 108}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["03LOPbQQRNZZqF74xomHbA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["1aGeUvm7tCYo07BHezrKNK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["030pk9FplGUb6+EFQ6TuYy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["345sBfDY1OZah1yFW0Ey/q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["dbGqwFyFVKD7AoZ8q+SlH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 106}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 108}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 112}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 114}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 100}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 100}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 104}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 88}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 90}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 395, "height": 200}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 145}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["13KOQMHuZHNr0Mltc+tebx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["89/A0yl/1NeL5n5+wLgzDh"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_string"], "value": ""}, {"__type__": "cc.TargetInfo", "localID": ["6cAXLSLwpHkrW7t1Pt5r9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebLybIwLNHjIr8sl6sVEla"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_fontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_actualFontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lineHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 156}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 150, "height": 44.32}}, {"__type__": "cc.TargetInfo", "localID": ["14AMVFAPRDQZ4352Pnxr79"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 159}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 158}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 160}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a7YIgD+0RPDZUwlUL7MTym", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 161}, {"__id__": 163}, {"__id__": 164}, {"__id__": 165}, {"__id__": 166}, {"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 173}, {"__id__": 174}, {"__id__": 176}, {"__id__": 178}, {"__id__": 179}, {"__id__": 181}, {"__id__": 183}, {"__id__": 185}, {"__id__": 187}, {"__id__": 189}, {"__id__": 191}, {"__id__": 193}, {"__id__": 194}, {"__id__": 196}, {"__id__": 198}, {"__id__": 200}, {"__id__": 202}, {"__id__": 204}, {"__id__": 206}, {"__id__": 207}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}, {"__id__": 211}, {"__id__": 212}, {"__id__": 213}, {"__id__": 214}, {"__id__": 216}, {"__id__": 217}, {"__id__": 218}, {"__id__": 219}, {"__id__": 220}, {"__id__": 221}, {"__id__": 222}, {"__id__": 224}, {"__id__": 226}, {"__id__": 227}, {"__id__": 228}, {"__id__": 230}, {"__id__": 231}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_name"], "value": "SlotItem2"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 500, "y": 330, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 162}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -100, "y": -120, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -60, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 171}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 175}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 177}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -140, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 180}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 186}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 188}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 195}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["bcLRFwVRhPB5kG3DSYPPWI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["03LOPbQQRNZZqF74xomHbA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 199}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["1aGeUvm7tCYo07BHezrKNK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 201}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["030pk9FplGUb6+EFQ6TuYy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["345sBfDY1OZah1yFW0Ey/q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 205}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "7c415f2b-1317-48a9-aea5-1f71e611135b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["dbGqwFyFVKD7AoZ8q+SlH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 195}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 199}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 201}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 205}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 192}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 180}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 65, "height": 200}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 215}, "propertyPath": ["_horizontalDirection"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["54R+RCSiFLMrWFUwAVHBlZ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 184}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 186}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 188}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 190}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 182}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 169}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebLybIwLNHjIr8sl6sVEla"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_fontSize"], "value": 32}, {"__type__": "cc.TargetInfo", "localID": ["6cAXLSLwpHkrW7t1Pt5r9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_actualFontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_lineHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 229}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 150, "height": 44.32}}, {"__type__": "cc.TargetInfo", "localID": ["14AMVFAPRDQZ4352Pnxr79"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_string"], "value": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 233}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 232}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 234}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d4FsyA6JhE9b9MVi+jBdZE", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 235}, {"__id__": 237}, {"__id__": 238}, {"__id__": 239}, {"__id__": 240}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}, {"__id__": 247}, {"__id__": 248}, {"__id__": 250}, {"__id__": 252}, {"__id__": 253}, {"__id__": 255}, {"__id__": 257}, {"__id__": 259}, {"__id__": 261}, {"__id__": 263}, {"__id__": 265}, {"__id__": 267}, {"__id__": 268}, {"__id__": 270}, {"__id__": 272}, {"__id__": 274}, {"__id__": 276}, {"__id__": 278}, {"__id__": 280}, {"__id__": 281}, {"__id__": 282}, {"__id__": 283}, {"__id__": 284}, {"__id__": 285}, {"__id__": 286}, {"__id__": 287}, {"__id__": 288}, {"__id__": 289}, {"__id__": 290}, {"__id__": 291}, {"__id__": 292}, {"__id__": 293}, {"__id__": 295}, {"__id__": 297}, {"__id__": 298}, {"__id__": 299}, {"__id__": 301}, {"__id__": 302}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_name"], "value": "SlotItem3"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -500, "y": 330, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 236}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": -120, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 249}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 251}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 145, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 254}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 256}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 258}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 260}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 262}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 264}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 266}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 269}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["bcLRFwVRhPB5kG3DSYPPWI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["03LOPbQQRNZZqF74xomHbA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 273}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["1aGeUvm7tCYo07BHezrKNK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["030pk9FplGUb6+EFQ6TuYy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 277}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["345sBfDY1OZah1yFW0Ey/q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["dbGqwFyFVKD7AoZ8q+SlH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 269}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 271}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 273}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 275}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 277}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 256}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 258}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 260}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 262}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 264}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 266}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 294}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebLybIwLNHjIr8sl6sVEla"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 296}, "propertyPath": ["_fontSize"], "value": 32}, {"__type__": "cc.TargetInfo", "localID": ["6cAXLSLwpHkrW7t1Pt5r9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 296}, "propertyPath": ["_actualFontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 296}, "propertyPath": ["_lineHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 300}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 150, "height": 44.32}}, {"__type__": "cc.TargetInfo", "localID": ["14AMVFAPRDQZ4352Pnxr79"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 296}, "propertyPath": ["_string"], "value": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 294}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 304}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 303}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 305}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7bAKd7r3pIkqyF5Hr7hBNI", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 306}, {"__id__": 308}, {"__id__": 309}, {"__id__": 310}, {"__id__": 311}, {"__id__": 313}, {"__id__": 315}, {"__id__": 317}, {"__id__": 318}, {"__id__": 319}, {"__id__": 321}, {"__id__": 323}, {"__id__": 324}, {"__id__": 326}, {"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}, {"__id__": 336}, {"__id__": 338}, {"__id__": 339}, {"__id__": 340}, {"__id__": 342}, {"__id__": 344}, {"__id__": 346}, {"__id__": 348}, {"__id__": 350}, {"__id__": 352}, {"__id__": 353}, {"__id__": 354}, {"__id__": 355}, {"__id__": 356}, {"__id__": 357}, {"__id__": 358}, {"__id__": 359}, {"__id__": 360}, {"__id__": 361}, {"__id__": 362}, {"__id__": 363}, {"__id__": 364}, {"__id__": 365}, {"__id__": 367}, {"__id__": 369}, {"__id__": 370}, {"__id__": 371}, {"__id__": 373}, {"__id__": 374}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_name"], "value": "SlotItem4"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 750, "y": 45, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 307}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -100, "y": -125.339, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 316}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 316}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 320}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 322}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -140, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 325}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 327}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -335, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 329}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -280, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 331}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -225, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 333}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -170, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 335}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -115, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 337}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -60, "y": 15.552, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["bcLRFwVRhPB5kG3DSYPPWI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["03LOPbQQRNZZqF74xomHbA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 345}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["1aGeUvm7tCYo07BHezrKNK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["030pk9FplGUb6+EFQ6TuYy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 349}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["345sBfDY1OZah1yFW0Ey/q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 351}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["dbGqwFyFVKD7AoZ8q+SlH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 343}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 345}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 347}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 349}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 351}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 327}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 325}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 65, "height": 200}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 329}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 331}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 333}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 335}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 337}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 366}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebLybIwLNHjIr8sl6sVEla"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 368}, "propertyPath": ["_fontSize"], "value": 32}, {"__type__": "cc.TargetInfo", "localID": ["6cAXLSLwpHkrW7t1Pt5r9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 368}, "propertyPath": ["_actualFontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 368}, "propertyPath": ["_lineHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 372}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 150, "height": 44.32}}, {"__type__": "cc.TargetInfo", "localID": ["14AMVFAPRDQZ4352Pnxr79"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 368}, "propertyPath": ["_string"], "value": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 366}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 44}, "_prefab": {"__id__": 376}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 375}, "asset": {"__uuid__": "85975dbe-6ce4-45b8-95b6-764406af485e", "__expectedType__": "cc.Prefab"}, "fileId": "1aB+MOgstFj4C90Ld77OnH", "instance": {"__id__": 377}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "328xwqi51OPI4RZ3gJKVky", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 378}, {"__id__": 380}, {"__id__": 381}, {"__id__": 382}, {"__id__": 383}, {"__id__": 385}, {"__id__": 387}, {"__id__": 389}, {"__id__": 390}, {"__id__": 391}, {"__id__": 393}, {"__id__": 395}, {"__id__": 396}, {"__id__": 398}, {"__id__": 400}, {"__id__": 402}, {"__id__": 404}, {"__id__": 406}, {"__id__": 408}, {"__id__": 410}, {"__id__": 411}, {"__id__": 412}, {"__id__": 414}, {"__id__": 416}, {"__id__": 418}, {"__id__": 420}, {"__id__": 422}, {"__id__": 424}, {"__id__": 425}, {"__id__": 426}, {"__id__": 427}, {"__id__": 428}, {"__id__": 429}, {"__id__": 430}, {"__id__": 432}, {"__id__": 434}, {"__id__": 435}, {"__id__": 436}, {"__id__": 438}, {"__id__": 439}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 379}, "propertyPath": ["_name"], "value": "SlotItem5"}, {"__type__": "cc.TargetInfo", "localID": ["1aB+MOgstFj4C90Ld77OnH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 379}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -760, "y": 45, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 379}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 379}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 384}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["77p5Qk1/ZAz65u2/+OXMk8"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 386}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 105, "y": -113.994, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["3ct5D2wWdMqpb/qEb4M8nm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 388}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["57MealDrxIvIfO4RM+b4hC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 388}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 386}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 392}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["a2X9LXUxFPPaR811BVbq70"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 394}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["d808IevUZK+LSitvQAHaOB"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 384}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 140, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 397}, "propertyPath": ["_anchorPoint"], "value": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.TargetInfo", "localID": ["e4GiYgvrVP0ZmQXio+pSQ9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 399}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["7b+TD6IYlHcJCbpOrGL6Me"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 401}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["4e3drhVPtDXaJn44I/qIqQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b91MYXqC9PYpAzrpxonGFS"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 405}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["b8V9eOa6FAjqYjIgMANLu9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 407}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8aPS94B7FFu57BtTjsTBtt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ebxTbGDcNNDpgzeCYhnmwt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 384}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 386}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 0.6}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 413}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["bcLRFwVRhPB5kG3DSYPPWI"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 415}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["03LOPbQQRNZZqF74xomHbA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 417}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["1aGeUvm7tCYo07BHezrKNK"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["030pk9FplGUb6+EFQ6TuYy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 421}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["345sBfDY1OZah1yFW0Ey/q"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 423}, "propertyPath": ["_spriteFrame"], "value": null}, {"__type__": "cc.TargetInfo", "localID": ["dbGqwFyFVKD7AoZ8q+SlH4"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 413}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 415}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 417}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 419}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 421}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 423}, "propertyPath": ["_atlas"], "value": {"__uuid__": "90641739-5b0c-4385-94a6-b6037b4104d6", "__expectedType__": "cc.SpriteAtlas"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 431}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["ebLybIwLNHjIr8sl6sVEla"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 433}, "propertyPath": ["_fontSize"], "value": 32}, {"__type__": "cc.TargetInfo", "localID": ["6cAXLSLwpHkrW7t1Pt5r9F"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 433}, "propertyPath": ["_actualFontSize"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 433}, "propertyPath": ["_lineHeight"], "value": 32}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 437}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 150, "height": 44.32}}, {"__type__": "cc.TargetInfo", "localID": ["14AMVFAPRDQZ4352Pnxr79"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 433}, "propertyPath": ["_string"], "value": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 431}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 50, "z": 0}}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 441}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cRfka37FMhJGQT/wznImy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 443}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0an9AL8bdFSbgidpGIQcEY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 445}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18sO1VAw1IQKmb8sP4NeM3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36bF2ytExCC5uwHl94fumS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "HUD", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 448}, {"__id__": 458}, {"__id__": 471}, {"__id__": 487}, {"__id__": 500}, {"__id__": 521}, {"__id__": 573}, {"__id__": 697}, {"__id__": 725}], "_active": true, "_components": [{"__id__": 743}, {"__id__": 745}, {"__id__": 747}, {"__id__": 749}], "_prefab": {"__id__": 909}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "InfoLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [], "_active": true, "_components": [{"__id__": 449}, {"__id__": 451}, {"__id__": 453}, {"__id__": 455}], "_prefab": {"__id__": 457}, "_lpos": {"__type__": "cc.Vec3", "x": -810, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 450}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 211, "g": 141, "b": 214, "a": 255}, "_string": "BÀN VIP: 1\nMệnh giá: 1000 tipzo\nPhiên: #15", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "77a4a87c-c228-4ff2-8375-030dfcdd81bf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97U4sctTRBxIGPub7m6qXn"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 452}, "_alignFlags": 9, "_target": null, "_left": 150, "_right": 0, "_top": 20, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfGzfD1JtGCagOVG1juKd6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 454}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1mEYy2DRKl7LkfHuhBA4P"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 448}, "_enabled": true, "__prefab": {"__id__": 456}, "_contentSize": {"__type__": "cc.Size", "width": 183.390625, "height": 91.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4E0V672xK1ZBI6MM8WrVf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26iNol4a1Eao51rhUqz6FR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "backBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [], "_active": true, "_components": [{"__id__": 459}, {"__id__": 461}, {"__id__": 464}, {"__id__": 466}, {"__id__": 468}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": -885.5, "y": 470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 460}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dd1c9c80-7f26-4fea-935d-4aa8b00e2eda@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffMMLv1mxNdbrLTnPqnfjl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 462}, "clickEvents": [{"__id__": 463}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "dd1c9c80-7f26-4fea-935d-4aa8b00e2eda@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "67c14213-1e00-41eb-8420-38802d0a5b5e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4/TUD7MNBqbPx58PkOeGB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 465}, "_alignFlags": 9, "_target": null, "_left": 25, "_right": 0, "_top": 20, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9kzJncjJHpqMbK/90yN5r"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 467}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fff9otksJBfbjHsAPHVvom"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 469}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3ZDDecPNLbJaOMuMllXMb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feO+gkze1PxrpfC8CnVMi+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg_thongbao", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 472}], "_active": true, "_components": [{"__id__": 480}, {"__id__": 482}, {"__id__": 484}], "_prefab": {"__id__": 486}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 80, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 471}, "_children": [], "_active": true, "_components": [{"__id__": 473}, {"__id__": 475}, {"__id__": 477}], "_prefab": {"__id__": 479}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 474}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbnmBbXtVEVJQtN+ckKXjS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 476}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73n08HFlFMi7UoNT9h4l63"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 472}, "_enabled": true, "__prefab": {"__id__": 478}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bWQPbijNMfZjxiR6rnCEo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddzghziztC5qc7GgZZHO2r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 481}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47e96ed3-1fb9-413a-8ef3-6644419c877d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08lumLi8hMwKXxC13KpsSD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 483}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dS5wc4WxBYa1ka+NkA4Ex"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 485}, "_contentSize": {"__type__": "cc.Size", "width": 791, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dVUO9S89AjYYy0N1Ysfng"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceQMHHeuBNj7jiDxnC9U6F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "chatBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [], "_active": true, "_components": [{"__id__": 488}, {"__id__": 490}, {"__id__": 493}, {"__id__": 495}, {"__id__": 497}], "_prefab": {"__id__": 499}, "_lpos": {"__type__": "cc.Vec3", "x": -885.5, "y": -470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 487}, "_enabled": true, "__prefab": {"__id__": 489}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dfcfbe4a-d1d3-452c-9ba1-b69639f4373f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0ljxV4xJFtqrmN/m6S4Nl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 487}, "_enabled": true, "__prefab": {"__id__": 491}, "clickEvents": [{"__id__": 492}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "dfcfbe4a-d1d3-452c-9ba1-b69639f4373f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "e22011c9-685b-423c-be30-68921134d7c0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdH0uDfA9GjJUHmbfQxl1s"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "actChat", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 487}, "_enabled": true, "__prefab": {"__id__": 494}, "_alignFlags": 12, "_target": null, "_left": 25, "_right": 0, "_top": 15, "_bottom": 20, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2bq6zoTNKAYw59DnSTKsa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 487}, "_enabled": true, "__prefab": {"__id__": 496}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8ksoO58JJar94bHgEHort"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 487}, "_enabled": true, "__prefab": {"__id__": 498}, "_contentSize": {"__type__": "cc.Size", "width": 99, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3/cDCqfxAG4rv+2HJ9TyE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dcL7VCLxEO5NnkhJUnqeM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "soundBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 501}], "_active": false, "_components": [{"__id__": 509}, {"__id__": 511}, {"__id__": 514}, {"__id__": 516}, {"__id__": 518}], "_prefab": {"__id__": 520}, "_lpos": {"__type__": "cc.Vec3", "x": -489, "y": -309, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "soundICon", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 500}, "_children": [], "_active": true, "_components": [{"__id__": 502}, {"__id__": 504}, {"__id__": 506}], "_prefab": {"__id__": 508}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 503}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeqORDpRtJBJ8mwcL/RdFW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 505}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2LwO2XSpODbhM+WXpn1Fy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 507}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 34}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "678oJMQYZLRbkClTsE6Lsb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4F7K+lTNI5ITgd66LFLjo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 510}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60FTkJJehGjIIU0wZuSb0i"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 512}, "clickEvents": [{"__id__": 513}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dhP6cHoxFLLi7bkuRV9D6"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 515}, "_alignFlags": 12, "_target": null, "_left": 115, "_right": 0, "_top": 15, "_bottom": 15, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85BoLiSFdPhLOxPVpyDeac"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 517}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc/XAEsahG25UMNbREAf0T"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 519}, "_contentSize": {"__type__": "cc.Size", "width": 72, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33gXlkWL5Bk63lGrRY0eDX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eIUZa+kNAo6FEbwBAfs+a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GroupBtns", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 522}, {"__id__": 543}], "_active": true, "_components": [{"__id__": 564}, {"__id__": 566}, {"__id__": 568}, {"__id__": 570}], "_prefab": {"__id__": 572}, "_lpos": {"__type__": "cc.Vec3", "x": 935, "y": -470, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "buttonUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 521}, "_children": [{"__id__": 523}], "_active": true, "_components": [{"__id__": 533}, {"__id__": 535}, {"__id__": 538}, {"__id__": 540}], "_prefab": {"__id__": 542}, "_lpos": {"__type__": "cc.Vec3", "x": -298, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 522}, "_children": [], "_active": true, "_components": [{"__id__": 524}, {"__id__": 526}, {"__id__": 528}, {"__id__": 530}], "_prefab": {"__id__": 532}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 523}, "_enabled": true, "__prefab": {"__id__": 525}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ÚP", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14sW1WjZ1D07FM2p1MiiHf"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 523}, "_enabled": true, "__prefab": {"__id__": 527}, "id": "ca03_1", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96GNx9v69HmKW58R2m2j0L"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 523}, "_enabled": true, "__prefab": {"__id__": 529}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90GuyRyDNLJ67gNRlypy5z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 523}, "_enabled": true, "__prefab": {"__id__": 531}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4qjlspTtFm7HP0CBuW2Qz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11huTu83ZCt6v9E/AFTSLn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": {"__id__": 534}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84sN4lQNdH5qFAxAmGeZwA"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": {"__id__": 536}, "clickEvents": [{"__id__": 537}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c9dsMeKhKf7K4EJG1CoVf"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "faceDownCard", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": {"__id__": 539}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27ZULvFjRLMZJ8vktWqLc+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 522}, "_enabled": true, "__prefab": {"__id__": 541}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "achznTbZtBj5S+U9S8uGVI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebd6lLm1FO8p/UVEwagvea", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "buttonDanh", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 521}, "_children": [{"__id__": 544}], "_active": true, "_components": [{"__id__": 554}, {"__id__": 556}, {"__id__": 559}, {"__id__": 561}], "_prefab": {"__id__": 563}, "_lpos": {"__type__": "cc.Vec3", "x": -91, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 543}, "_children": [], "_active": true, "_components": [{"__id__": 545}, {"__id__": 547}, {"__id__": 549}, {"__id__": 551}], "_prefab": {"__id__": 553}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 546}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ĐÁNH", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": false, "_font": {"__uuid__": "7c24553e-a5a9-4fca-85eb-bd4b3814664d", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47MJpSI2pOi6zTjVXmi2be"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 548}, "id": "ca70", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eehP/m1m5BKZN9aEHquKVr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 550}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81O6JRshFK6bHYZgO2n6CL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 552}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d47WXESYxIZqMVH61p4Cm/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bf0yLjqOxD+pI3wNdJT/zq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 555}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cN7+pX5BNxZzrpf2sPjBd"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 557}, "clickEvents": [{"__id__": 558}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "23283d76-dba3-4853-be16-7211dd47e573@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_pressedSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_disabledSprite": {"__uuid__": "22905e4d-6a9a-47b6-a3d0-a311226dad08@f9941", "__expectedType__": "cc.SpriteFrame"}, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 543}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dc2l9UXRMEpHUwl7JGtxt"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "playCard", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 560}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25riCFlLpFioNCpfOnP8q7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 562}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38ybiyoVlI7p9OM5Gt/UgY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80Z7YSf+ZGOZ7qIIo1cZZx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 565}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfqPgXjn1L45MfL9rtalMo"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 567}, "_alignFlags": 36, "_target": null, "_left": 0, "_right": 25, "_top": 0, "_bottom": 20, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26miHVoJZIKLZdEx3t1hWB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 569}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f78EH0ax1HtZMX66iowXbI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 571}, "_contentSize": {"__type__": "cc.Size", "width": 389, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "366hKTpc1KWrrypoXOkOkK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "945Jf2UqpBGr+Q/RqgfJGL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "WinPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 574}, {"__id__": 652}, {"__id__": 676}], "_active": true, "_components": [{"__id__": 690}, {"__id__": 692}, {"__id__": 694}], "_prefab": {"__id__": 696}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "toitrangPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 573}, "_children": [{"__id__": 575}], "_active": false, "_components": [{"__id__": 643}, {"__id__": 645}, {"__id__": 647}, {"__id__": 649}], "_prefab": {"__id__": 651}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "CardsLayout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 574}, "_children": [{"__id__": 576}, {"__id__": 586}, {"__id__": 596}, {"__id__": 606}, {"__id__": 616}, {"__id__": 626}], "_active": true, "_components": [{"__id__": 636}, {"__id__": 638}, {"__id__": 640}], "_prefab": {"__id__": 642}, "_lpos": {"__type__": "cc.Vec3", "x": -150, "y": 125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 0.8}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "card1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 577}, {"__id__": 579}, {"__id__": 581}, {"__id__": 583}], "_prefab": {"__id__": 585}, "_lpos": {"__type__": "cc.Vec3", "x": 60, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 578}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05/T/OeXhAjIAOx50PBW8l"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 580}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41qoeJQLdGor4JgH4yuFLc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 582}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65BS8gdY9PqaTAKgLjCgb0"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 576}, "_enabled": true, "__prefab": {"__id__": 584}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aIIG+glZNzaE8Kb7ifGno"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81L0SzbtJDr63i5K0Y+Cra", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 587}, {"__id__": 589}, {"__id__": 591}, {"__id__": 593}], "_prefab": {"__id__": 595}, "_lpos": {"__type__": "cc.Vec3", "x": 115, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 588}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a62iP5G0lM8ZVMov83R3p8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 590}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0PB7F88lK7oZkQSWeDx2t"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 592}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "devH3fD+FD+pWlKeOxFrPD"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 586}, "_enabled": true, "__prefab": {"__id__": 594}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20wmZRjbtKWZQ1rqbh2E+A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3JgkA3mpGUbfks6ysEG+v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 597}, {"__id__": 599}, {"__id__": 601}, {"__id__": 603}], "_prefab": {"__id__": 605}, "_lpos": {"__type__": "cc.Vec3", "x": 170, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 596}, "_enabled": true, "__prefab": {"__id__": 598}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84d97QVnREDbsyDeIHCNgN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 596}, "_enabled": true, "__prefab": {"__id__": 600}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f52T6dB7pOe59wthe2MP9p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 596}, "_enabled": true, "__prefab": {"__id__": 602}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47jOapZbhKkItMDX9SiOnt"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 596}, "_enabled": true, "__prefab": {"__id__": 604}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f31R6GsWhHQ7Ui04fhbMn5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7aTZy58MBI1J5rSEHYc1Vt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 607}, {"__id__": 609}, {"__id__": 611}, {"__id__": 613}], "_prefab": {"__id__": 615}, "_lpos": {"__type__": "cc.Vec3", "x": 225, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 606}, "_enabled": true, "__prefab": {"__id__": 608}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddqkmnKQxFQbNU6FSsAHS1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 606}, "_enabled": true, "__prefab": {"__id__": 610}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9ekfH3fVBdJd7n2SbArEU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 606}, "_enabled": true, "__prefab": {"__id__": 612}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef4W57AwZPT7PwAnAX23Sq"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 606}, "_enabled": true, "__prefab": {"__id__": 614}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8izTUYUpEZ4yyk2Wgs/ue"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "adsJNxjpZDRLthcgbOMJH9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 617}, {"__id__": 619}, {"__id__": 621}, {"__id__": 623}], "_prefab": {"__id__": 625}, "_lpos": {"__type__": "cc.Vec3", "x": 280, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 618}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89U0UtqcJCqYqU5EHwdDFd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 620}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8J966JH9GhZ1WrgXvLS2z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 622}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "431HwhO9hBnIUj77fsWTHY"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 624}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03ZohU27ZKn61ORwCKmlWI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17ix7jZ5xBK4IE/5w3HWTt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "card6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 575}, "_children": [], "_active": true, "_components": [{"__id__": 627}, {"__id__": 629}, {"__id__": 631}, {"__id__": 633}], "_prefab": {"__id__": 635}, "_lpos": {"__type__": "cc.Vec3", "x": 335, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 628}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95rvvLKxlESpNhL9n+t5Qv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 630}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78VxQCAnNL4ZdVY08KwUSj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 632}, "_contentSize": {"__type__": "cc.Size", "width": 120, "height": 170}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84cZTcjQJNdb1WhJjlDIH+"}, {"__type__": "c8d57L55P9JmJOS7QKAmKlk", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 626}, "_enabled": true, "__prefab": {"__id__": 634}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8CIoLAFNK1ZLEGn2pgHly"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fai0A0xvxGUovE9DIBr1DM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 637}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": -65, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46DK4YdCNEFZ+Wl0Ird3UD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 639}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "800O73R+NF8qnCjbzOtyRM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 575}, "_enabled": true, "__prefab": {"__id__": 641}, "_contentSize": {"__type__": "cc.Size", "width": 395, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72rX7IpP9I8blwNBD3b0FL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b56WZ0BHpI4ZjYyFa770nw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 574}, "_enabled": true, "__prefab": {"__id__": 644}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9aa1e84b-7541-425b-b545-b73761181d8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36D/2EJ99I26BWTlX9QR95"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 574}, "_enabled": true, "__prefab": {"__id__": 646}, "viet": {"__uuid__": "5df6cdcf-76ef-4072-a2db-f89d69f9fd20@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "9aa1e84b-7541-425b-b545-b73761181d8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "bb36ff7d-b89a-41da-bd19-083a3aac0eab@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "40500fd0-17cc-4d97-b3ef-02a9be29edfb@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "12a1a7bb-31d8-4413-b511-aa2a492853c0@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "ed7931d6-038d-4fc2-a9ee-31c3237e1e9b@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f7XRV8Z9EKa2o/ltsMlcv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 574}, "_enabled": true, "__prefab": {"__id__": 648}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41xvhZ6CxH1ZD6Mqh16h51"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 574}, "_enabled": true, "__prefab": {"__id__": 650}, "_contentSize": {"__type__": "cc.Size", "width": 618, "height": 251}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08wmWxp9NLFp08g5LKZ5et"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26wmy2n/FJuZjLrHJQySwO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "thangtungPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 573}, "_children": [{"__id__": 653}, {"__id__": 661}], "_active": false, "_components": [{"__id__": 671}, {"__id__": 673}], "_prefab": {"__id__": 675}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Firework", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 652}, "_children": [], "_active": true, "_components": [{"__id__": 654}, {"__id__": 656}, {"__id__": 658}], "_prefab": {"__id__": 660}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 2, "y": 2, "z": 2}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 655}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcEgmT3P5DILD2+5T/Ddxo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 657}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "989V3NZgBC45DOQ953sXmk"}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 653}, "_enabled": true, "__prefab": {"__id__": 659}, "playOnLoad": true, "_clips": [{"__uuid__": "52ec27b4-0514-4d3d-8507-92d5cf61476e", "__expectedType__": "cc.AnimationClip"}], "_defaultClip": {"__uuid__": "52ec27b4-0514-4d3d-8507-92d5cf61476e", "__expectedType__": "cc.AnimationClip"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fd/6acFI1JfI2f639r5rCh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bGOeYsatDSpxd6XyRmzxO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "thangtungPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 652}, "_children": [], "_active": true, "_components": [{"__id__": 662}, {"__id__": 664}, {"__id__": 666}, {"__id__": 668}], "_prefab": {"__id__": 670}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 663}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71O6pu/UJCAYgTsOZ27K3s"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 665}, "viet": {"__uuid__": "4cc456e7-bb08-4b25-8b67-7106570ffc0a@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "e3e64b38-bf08-4d15-8bd2-932bbdc0f5ab@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "fe57cddc-7675-4277-9f2d-cf000c738f03@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "1c43c923-a918-4d7c-99b6-6dd1cd65990a@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "ea8c9fb9-15a8-40bc-b34f-7bde814db7da@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "727ce31f-292a-4038-93d0-2e11315c9650@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15uk7auFxEapY3bjS3x8GA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 667}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c28P0yUvJBKKi3J7ec1dHm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 661}, "_enabled": true, "__prefab": {"__id__": 669}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4p/7SFwJOuZgbIoDzh+JG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e4ktnNFJBOUIJL8U3DxTMC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 652}, "_enabled": true, "__prefab": {"__id__": 672}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1aKEjNwtJ0JTjsshRH7ND"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 652}, "_enabled": true, "__prefab": {"__id__": 674}, "_contentSize": {"__type__": "cc.Size", "width": 635, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2KA/Jp4tM/YanaaG+BADI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51cfrgM9hF6Ljg+4/3D2zy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "NormalWinPopUp", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 573}, "_children": [{"__id__": 677}], "_active": false, "_components": [{"__id__": 685}, {"__id__": 687}], "_prefab": {"__id__": 689}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thang-en", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 678}, {"__id__": 680}, {"__id__": 682}], "_prefab": {"__id__": 684}, "_lpos": {"__type__": "cc.Vec3", "x": -30, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 679}, "_contentSize": {"__type__": "cc.Size", "width": 222, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3vEvhaydN6pAw97GWVjAr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 681}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "79adf5ed-29bc-4582-9c40-8958c530f22a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "de/A4I1aJByqRxgqHhlcX2"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 683}, "viet": {"__uuid__": "b3a775ae-8a6f-47fd-b3ee-d8109c1d8208@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "79adf5ed-29bc-4582-9c40-8958c530f22a@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "a36f266d-7d35-493f-89ad-b84f6f09af8f@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "80b144be-955f-4a55-9262-d2e5e1c7efc0@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "df3e3d9d-2315-4740-a523-00a88f033074@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "3ccf85ab-c0ef-407e-b4b6-cd265c64c062@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdTjwEgwhBQ6tNbweZrHRg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0yU9C+KhD3qkrfrdBN8u1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 686}, "_contentSize": {"__type__": "cc.Size", "width": 546, "height": 385}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8aGoIpDJNY6MMgq5GMdMu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 688}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "14f87202-600a-4d45-9866-0a6aa7b27d88@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfm+nJqIdO37zqKe3lQr5K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1swQReUhAzKw4oyO5i3ox", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 691}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b91cIC//VAY4mKDAKVcKr/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 693}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6dxqr6p5EPoT7HE0mvGkJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 573}, "_enabled": true, "__prefab": {"__id__": 695}, "_contentSize": {"__type__": "cc.Size", "width": 460, "height": 510}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79gWgoPRBBu7DqSkb1jnws"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fGkellBpPlKh40pzcQxBc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "NotifyBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 698}], "_active": true, "_components": [{"__id__": 718}, {"__id__": 720}, {"__id__": 722}], "_prefab": {"__id__": 724}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 480, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1.2}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "NotifyBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 697}, "_children": [{"__id__": 699}], "_active": true, "_components": [{"__id__": 707}, {"__id__": 709}, {"__id__": 711}, {"__id__": 713}, {"__id__": 715}], "_prefab": {"__id__": 717}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 23.17, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RichText", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 698}, "_children": [], "_active": true, "_components": [{"__id__": 700}, {"__id__": 702}, {"__id__": 704}], "_prefab": {"__id__": 706}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 701}, "_lineHeight": 50, "_string": "<color=#00ff00>Rich</c><color=#0fffff>Text</color>", "_horizontalAlign": 0, "_verticalAlign": 1, "_fontSize": 24, "_fontColor": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_maxWidth": 0, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 1, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43sn5JjndAQIFxj3dNmZgr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 703}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62GSoviO1LTaonTjGfM01q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 705}, "_contentSize": {"__type__": "cc.Size", "width": 94.27734375, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5Z45PtRBPZIrFaLD8syZL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e6AbQJlZDZqD9PoGdn5vI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 708}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44gIJCtStKub1/r7zCYQve"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 710}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccEX/LQvFPGr5+RDL7iCPL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 712}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff7TPohmRHka0cyBQ+Z3Jd"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 714}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16pzs73wFFyIMTHN+4VAl9"}, {"__type__": "3517fCmVSFNPbA/UUpGCaAd", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 716}, "notity": {"__id__": 699}, "maxNotifies": 5, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51KkYCEqlJNJ9Bf7gTlWp1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1RjRnHNFKvqhNpP6u57zY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 719}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8c8fbfcc-e88b-48d4-a48e-3853883a0e34@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98txSYSoBEnatbiZRjkEQe"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 721}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26AfpmOHZAq7d8tdsj7V7p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 723}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fevR8jvbRCrrzt5Zo3vDBo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3f2Yriq/FJz40dUwiilAoN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "NoticeCountPlayer", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 726}], "_active": false, "_components": [{"__id__": 736}, {"__id__": 738}, {"__id__": 740}], "_prefab": {"__id__": 742}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -135, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 725}, "_children": [], "_active": true, "_components": [{"__id__": 727}, {"__id__": 729}, {"__id__": 731}, {"__id__": 733}], "_prefab": {"__id__": 735}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 726}, "_enabled": true, "__prefab": {"__id__": 728}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON>ng đủ người chơi để thực hiện v<PERSON> chơi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49qmwoPrZH1YZnCNsOkXiR"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 726}, "_enabled": true, "__prefab": {"__id__": 730}, "id": "me10009", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65OAWGZcdAOI023J7aXuVx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 726}, "_enabled": true, "__prefab": {"__id__": 732}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bCBPpWUlJu4N9d+nbFIBp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 726}, "_enabled": true, "__prefab": {"__id__": 734}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68wgpU2oNJyo6+YOQLceET"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04Gpn4s55EI6NK0liZOFD+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 737}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "383G48DERC3K39s9vjgxJE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 739}, "_contentSize": {"__type__": "cc.Size", "width": 1582, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95hk17NTpHtL1sKAtDm1U3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 725}, "_enabled": true, "__prefab": {"__id__": 741}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47e96ed3-1fb9-413a-8ef3-6644419c877d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91UmIxcDpMxZ3s3jeCjW4v"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aPi46UdpEFKrhoSOdvcDq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 744}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "batq4iwZxGZ5Tvm5f3gDSB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 746}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b24EeNDuBHZKF0IhDgwi49"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 748}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4s24tOjVHkpnIumSVoUBO"}, {"__type__": "0873c4ISlxNYIF/p5zupACP", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 750}, "InfoLabel": {"__id__": 449}, "BackgroundNormal": {"__id__": 28}, "BackgroundVip": {"__id__": 12}, "GroupButtons": {"__id__": 521}, "WinPopUp": {"__id__": 573}, "Effect": {"__id__": 751}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47o78VSjdA3rOZbe5vHyrr"}, {"__type__": "cc.ParticleSystem2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 752}, "_enabled": true, "__prefab": {"__id__": 908}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "duration": 5, "emissionRate": 100, "life": 1.5, "lifeVar": 0.5, "angle": 0, "angleVar": 0, "startSize": 40, "startSizeVar": 0, "endSize": -1, "endSizeVar": 0, "startSpin": 0, "startSpinVar": 51, "endSpin": 500, "endSpinVar": 0, "sourcePos": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "posVar": {"__type__": "cc.Vec2", "x": 608, "y": 1}, "emitterMode": 0, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -906}, "speed": 0, "speedVar": 337, "tangentialAccel": 0, "tangentialAccelVar": 0, "radialAccel": 0, "radialAccelVar": 50, "rotationIsDir": false, "startRadius": 0, "startRadiusVar": 0, "endRadius": 0, "endRadiusVar": 0, "rotatePerS": 0, "rotatePerSVar": 0, "playOnLoad": false, "autoRemoveOnFinish": false, "_preview": true, "preview": true, "_custom": true, "_file": null, "_spriteFrame": {"__uuid__": "931a8295-8875-403e-8b9b-5bb857cb58e5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_totalParticles": 150, "_startColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_startColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_endColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 191, "a": 255}, "_endColorVar": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_positionType": 1, "_id": ""}, {"__type__": "cc.Node", "_name": "Effect", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 753}, "_children": [], "_active": false, "_components": [{"__id__": 751}, {"__id__": 903}, {"__id__": 905}], "_prefab": {"__id__": 907}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 520, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "UI", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 754}, {"__id__": 752}, {"__id__": 762}, {"__id__": 876}], "_active": true, "_components": [{"__id__": 896}, {"__id__": 898}, {"__id__": 900}], "_prefab": {"__id__": 902}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "PopUp", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 753}, "_children": [], "_active": true, "_components": [{"__id__": 755}, {"__id__": 757}, {"__id__": 759}], "_prefab": {"__id__": 761}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 754}, "_enabled": true, "__prefab": {"__id__": 756}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94X90jcMdN4I8FigjTpvyB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 754}, "_enabled": true, "__prefab": {"__id__": 758}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aWwN3Z91AHLrOow5qKhhl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 754}, "_enabled": true, "__prefab": {"__id__": 760}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bTnLt0j9EdZH6jc3WSAj6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60KlIEnyNPeokCHQSPvAm3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "AlertDialog", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 753}, "_children": [{"__id__": 763}, {"__id__": 775}], "_active": false, "_components": [{"__id__": 867}, {"__id__": 869}, {"__id__": 871}, {"__id__": 873}], "_prefab": {"__id__": 875}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 762}, "_children": [], "_active": true, "_components": [{"__id__": 764}, {"__id__": 766}, {"__id__": 768}, {"__id__": 770}, {"__id__": 772}], "_prefab": {"__id__": 774}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 765}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eQ8BVFSxGG7U8FvPmSQsG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 767}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6nPMPAZhF+JunrzRIMq1w"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 769}, "_opacity": 128, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48slddS5BDW4kVtImdMlON"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 771}, "_contentSize": {"__type__": "cc.Size", "width": 1919.9999999999998, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66njch3bVHy7dnt/fBuJJ/"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 763}, "_enabled": true, "__prefab": {"__id__": 773}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7azBS+6eZIaLI5uhwLdEBF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9c3CwSGn5LMYeh9fiDZEez", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 762}, "_children": [{"__id__": 776}], "_active": true, "_components": [{"__id__": 860}, {"__id__": 862}, {"__id__": 864}], "_prefab": {"__id__": 866}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg_popup", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 775}, "_children": [{"__id__": 777}, {"__id__": 789}, {"__id__": 797}, {"__id__": 807}, {"__id__": 830}], "_active": true, "_components": [{"__id__": 853}, {"__id__": 855}, {"__id__": 857}], "_prefab": {"__id__": 859}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 776}, "_children": [], "_active": true, "_components": [{"__id__": 778}, {"__id__": 780}, {"__id__": 782}, {"__id__": 784}, {"__id__": 786}], "_prefab": {"__id__": 788}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 180.6903957256298, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 777}, "_enabled": true, "__prefab": {"__id__": 779}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "THÔNG BÁO", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40.95, "_fontSize": 40.95, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40.95, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28Rxqr1iJD6KmBEMp6i33J"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 777}, "_enabled": true, "__prefab": {"__id__": 781}, "id": "txt_notify", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7uY0APmdPOKD3Q3XhhTNE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 777}, "_enabled": true, "__prefab": {"__id__": 783}, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 45, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18xyr7tIlIyJCwjkAgTKR3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 777}, "_enabled": true, "__prefab": {"__id__": 785}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "471g1K9rtKy5Ut6NVSED7L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 777}, "_enabled": true, "__prefab": {"__id__": 787}, "_contentSize": {"__type__": "cc.Size", "width": 227.3644256591797, "height": 51.597}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5094752742529568}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31A/t4MShKP4Ok2EPiLJJC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0UoGjdNlMToAgH9FHGFcJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 776}, "_children": [], "_active": true, "_components": [{"__id__": 790}, {"__id__": 792}, {"__id__": 794}], "_prefab": {"__id__": 796}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 791}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 230}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0uhJ2LGVFe7uyGflP6SYq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 793}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 150}, "_spriteFrame": {"__uuid__": "e7a3605e-96e3-4428-ad19-71f8367752d6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3DPVAhZVA2Y2OWCck8zyz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 789}, "_enabled": true, "__prefab": {"__id__": 795}, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19JytJdulFJJqnVyjXys4m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c7NEznvI5GALHQrmc3IvdL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "LblMsg", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 776}, "_children": [], "_active": true, "_components": [{"__id__": 798}, {"__id__": 800}, {"__id__": 802}, {"__id__": 804}], "_prefab": {"__id__": 806}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 799}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> báo tình hình dịch đang căng thẳng. <PERSON> ở đâu ở yên đấy", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 1, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58YPKGBbNN1aFle5gVbjjk"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 801}, "id": "ca247", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd/FYifj1DTZ7keQpESoGk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 803}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8VdJONLZA9ZLmK47Io12o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 797}, "_enabled": true, "__prefab": {"__id__": 805}, "_contentSize": {"__type__": "cc.Size", "width": 900, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "502fgDhX9KwZ+VC7vUV61i"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "03pJblQ2VOXJSix4a75zp6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Confirm", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 776}, "_children": [{"__id__": 808}], "_active": true, "_components": [{"__id__": 818}, {"__id__": 820}, {"__id__": 823}, {"__id__": 825}, {"__id__": 827}], "_prefab": {"__id__": 829}, "_lpos": {"__type__": "cc.Vec3", "x": 250, "y": -181, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "LblDone", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 807}, "_children": [], "_active": true, "_components": [{"__id__": 809}, {"__id__": 811}, {"__id__": 813}, {"__id__": 815}], "_prefab": {"__id__": 817}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.379, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 808}, "_enabled": true, "__prefab": {"__id__": 810}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ĐÓNG", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 36, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecJmDJJUZKLYBB7m6FJL4/"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 808}, "_enabled": true, "__prefab": {"__id__": 812}, "id": "txt_confirm", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "af64nAT2FEzqgrudgZ5EHS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 808}, "_enabled": true, "__prefab": {"__id__": 814}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88JkH1HrVMaLsLFagL81sX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 808}, "_enabled": true, "__prefab": {"__id__": 816}, "_contentSize": {"__type__": "cc.Size", "width": 98.26171875, "height": 45.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5094752742529568}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caURfI23NF/LVzOY1kXkDe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6aLb0GO+RHCqdVEj4Fzik4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 819}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "50f50bd1-931e-4d97-9e51-169085a1dfc0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7a0gftb5NBzZ/SqGnRPUhF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 821}, "clickEvents": [{"__id__": 822}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 807}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11rwYR661FB6g0kfR6esSg"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "yesClick", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 824}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 15, "_horizontalCenter": 250, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfW+FYOh5LBqmY8ewayDoM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 826}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9Xq3q+YhLbZm7mhAkk0lm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 807}, "_enabled": true, "__prefab": {"__id__": 828}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34sbQ+cKJLNalERUhQgtjm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36jIbX4llCh4rIXqyZq0g/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 776}, "_children": [{"__id__": 831}], "_active": true, "_components": [{"__id__": 841}, {"__id__": 843}, {"__id__": 846}, {"__id__": 848}, {"__id__": 850}], "_prefab": {"__id__": 852}, "_lpos": {"__type__": "cc.Vec3", "x": -250, "y": -181, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "LblDone", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 830}, "_children": [], "_active": true, "_components": [{"__id__": 832}, {"__id__": 834}, {"__id__": 836}, {"__id__": 838}], "_prefab": {"__id__": 840}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.379, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 831}, "_enabled": true, "__prefab": {"__id__": 833}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ĐÓNG", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 36, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07SdDLEM9FrId54WlcENdZ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 831}, "_enabled": true, "__prefab": {"__id__": 835}, "id": "txt_close", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9C+hfteZOGozr5B7DxyN9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 831}, "_enabled": true, "__prefab": {"__id__": 837}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56W87xr8xF1beTHKU4Gbij"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 831}, "_enabled": true, "__prefab": {"__id__": 839}, "_contentSize": {"__type__": "cc.Size", "width": 98.26171875, "height": 45.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5094752742529568}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1WfHhVrpJs4dpGlngSncu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfAjiSektObKUMGoZvxl2t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 842}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "50f50bd1-931e-4d97-9e51-169085a1dfc0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96f4x6capMBKrVRPCOW9ls"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 844}, "clickEvents": [{"__id__": 845}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 830}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9v5Ycq6VGVLHKdlpEczZn"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "8eaa0litndK2LZGqpdr4g8Z", "handler": "noClick", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 847}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 15, "_horizontalCenter": -250, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fu4AON85PyJLLY7jQg4Bs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 849}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18M22l2QhIv52nnfpmHpRu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 830}, "_enabled": true, "__prefab": {"__id__": 851}, "_contentSize": {"__type__": "cc.Size", "width": 326, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eblp0nG5F8oE00gOUZ6Fi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86dU2D2aVOCK8O8Np3eEYt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 776}, "_enabled": true, "__prefab": {"__id__": 854}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "dbdbf5df-99ca-4366-ade6-eecd436f94bc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b5fDPwAJLXpBjlJpzeozy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 776}, "_enabled": true, "__prefab": {"__id__": 856}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4N3PXvBZP95Laobvt7Zab"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 776}, "_enabled": true, "__prefab": {"__id__": 858}, "_contentSize": {"__type__": "cc.Size", "width": 1122, "height": 502}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7eTBhRghOjJd/B50M66NM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96LEgvHCtF5b76YjxMEXbo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 775}, "_enabled": true, "__prefab": {"__id__": 861}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0exNOqT4xLOIz5iE54WKxb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 775}, "_enabled": true, "__prefab": {"__id__": 863}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eYzskPGFMU5sTXxKnUhAv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 775}, "_enabled": true, "__prefab": {"__id__": 865}, "_contentSize": {"__type__": "cc.Size", "width": 933, "height": 672}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfd8eRs7JCWbuTXyzlzp4V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6biljv7NlAEYrp6ODXT3kr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 868}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eDSVr9QVNz62LJQyaI7E+"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 870}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88Zs4qZw9OTIRLIfCwhrQu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 872}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31EPjk0X5GaJo3GDGTLgQx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 874}, "_contentSize": {"__type__": "cc.Size", "width": 1919.9999999999998, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8097WRHn1KMKxTgPTt62rm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "287aiF7LlELKxKNT3coXvz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Toast", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 753}, "_children": [{"__id__": 877}, {"__id__": 883}], "_active": false, "_components": [{"__id__": 889}, {"__id__": 891}, {"__id__": 893}], "_prefab": {"__id__": 895}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 876}, "_children": [], "_active": true, "_components": [{"__id__": 878}, {"__id__": 880}], "_prefab": {"__id__": 882}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 879}, "_contentSize": {"__type__": "cc.Size", "width": 1582, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7BzdmHo1G1Ils3ioafrB2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 877}, "_enabled": true, "__prefab": {"__id__": 881}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "47e96ed3-1fb9-413a-8ef3-6644419c877d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88APXMNVtDvIl6lZsw5qCn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8eGNQkeQtLS4i59qJrJbxX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 876}, "_children": [], "_active": true, "_components": [{"__id__": 884}, {"__id__": 886}], "_prefab": {"__id__": 888}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 883}, "_enabled": true, "__prefab": {"__id__": 885}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 40.32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dFnwruS5Po7XA2sqWFYUg"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 883}, "_enabled": true, "__prefab": {"__id__": 887}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 32, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fsplKtYZFdrUtj/XslqMn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4W2idH9NPYKo634JPRqZe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 876}, "_enabled": true, "__prefab": {"__id__": 890}, "_contentSize": {"__type__": "cc.Size", "width": 1919.9999999999998, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08Wjm/TP1IfpUuzXxxvJpz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 876}, "_enabled": true, "__prefab": {"__id__": 892}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22YmmTcXtIwancR2Cvooh4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 876}, "_enabled": true, "__prefab": {"__id__": 894}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "faL5xxsQdAeaHPFYtbP4uc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "235ydtjvlBJYjuDJ61YEvn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 753}, "_enabled": true, "__prefab": {"__id__": 897}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c159Rjgi5LfYGqrOl0MhH5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 753}, "_enabled": true, "__prefab": {"__id__": 899}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cpAsJzO9KkLS8y3OpYhe1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 753}, "_enabled": true, "__prefab": {"__id__": 901}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39T/eFk6BEwadlduGRFY2Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eQ/OnJv5KQYVhkvmmT6Ir", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 752}, "_enabled": true, "__prefab": {"__id__": 904}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cQujw2sNOjZBSXbJuH36u"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 752}, "_enabled": true, "__prefab": {"__id__": 906}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49znqlActFWrV8hMA3sB0O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "11wPnbaZ1GIa+aBbuIAlqk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "8781W6aRxNYIyGKNGN1O2V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2/Hf0RdhHwrezO08imwTD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 911}, {"__id__": 919}], "_active": true, "_components": [{"__id__": 927}, {"__id__": 929}], "_prefab": {"__id__": 931}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "MusicSource", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 910}, "_children": [], "_active": true, "_components": [{"__id__": 912}, {"__id__": 914}, {"__id__": 916}], "_prefab": {"__id__": 918}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 913}, "_clip": null, "_loop": true, "_playOnAwake": true, "_volume": 0.5, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03KudbTYhPuoeaB0wwBb50"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 915}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96VezGD69GGIRlziRWrThi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 917}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddt3oda+ZO8a2hv0ag/t8G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bBg3/QxVCwYt+dBrqgm7E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "EffectSource", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 910}, "_children": [], "_active": true, "_components": [{"__id__": 920}, {"__id__": 922}, {"__id__": 924}], "_prefab": {"__id__": 926}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.AudioSource", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 919}, "_enabled": true, "__prefab": {"__id__": 921}, "_clip": null, "_loop": false, "_playOnAwake": true, "_volume": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9w+hyGQtIJ4jD1dUVPTHK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 919}, "_enabled": true, "__prefab": {"__id__": 923}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dIy9mU7ZNbbIGwHEKjS0E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 919}, "_enabled": true, "__prefab": {"__id__": 925}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0C9Q6VEBCQqPE6GnMqhSB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27SaXc7KxNEKisi+7pMpJt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 910}, "_enabled": true, "__prefab": {"__id__": 928}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d7o9gPd1Jz5x62qdsDVMK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 910}, "_enabled": true, "__prefab": {"__id__": 930}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d85wYRV3RGu5uKntdZlg1A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9zH3bXq9NaKL3u8VV56lF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 933}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fEStC0e9IEbV9z/9NPsH7"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 935}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbQYIXd5pPqK7O05noaWXW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 937}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b72vw33W9OkZbKJ6WaCSAy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 939}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90n8AW34lL6a3UluT5b4uf"}, {"__type__": "8eaa0litndK2LZGqpdr4g8Z", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 941}, "HUD": {"__id__": 749}, "PlayerList": [null, null, null, null, null], "dealer": {"__id__": 46}, "WinPopUp": {"__id__": 573}, "Detail": {"__id__": 471}, "NoityPlayerCount": {"__id__": 725}, "CheckPlayerActive": {"__id__": 762}, "Toast": {"__id__": 876}, "CardShare": {"__uuid__": "89670068-818c-41bf-96ed-1bd3db981024", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b4zouaIZJvLQw23JBeDVF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daYKDXVI9LsbQKW5AYVecb", "instance": null, "targetOverrides": [{"__id__": 943}, {"__id__": 945}, {"__id__": 947}, {"__id__": 949}, {"__id__": 951}, {"__id__": 953}], "nestedPrefabInstanceRoots": [{"__id__": 375}, {"__id__": 303}, {"__id__": 232}, {"__id__": 158}, {"__id__": 55}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["PlayerList", "0"], "target": {"__id__": 55}, "targetInfo": {"__id__": 944}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["PlayerList", "1"], "target": {"__id__": 158}, "targetInfo": {"__id__": 946}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["PlayerList", "2"], "target": {"__id__": 232}, "targetInfo": {"__id__": 948}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["PlayerList", "3"], "target": {"__id__": 303}, "targetInfo": {"__id__": 950}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["PlayerList", "4"], "target": {"__id__": 375}, "targetInfo": {"__id__": 952}}, {"__type__": "cc.TargetInfo", "localID": ["56vahmnydCxJFfN3VPm6/A"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 940}, "sourceInfo": null, "propertyPath": ["TestCard"], "target": null, "targetInfo": {"__id__": 954}}, {"__type__": "cc.TargetInfo", "localID": ["75MSLtmRBA8JJTficdOyqF"]}]