
import { _decorator,<PERSON><PERSON>,director,instantiate,Label,log,Node, Sprite, tween, UIOpacity, v3, Vec3,Animation, AnimationClip, AudioSource } from "cc";
import { MiniGame } from "../../Lobby/scripts/common/MiniGame";
import SlotLayer777 from "./Slot3x3.SlotLayer";
import SlotLayer777X from "./Slot3x3.SlotLayerX";
import MiniGameSignalRClient from "../../Lobby/scripts/common/networks/MiniGameSignalRClient";
import { Utils } from "../../Lobby/scripts/common/Utils";
import Configs from "../../Lobby/scripts/common/Config";
import App from "../../Lobby/scripts/common/App";
import Item77 from "./Slot3x3.ItemSlot";
import BundleControl from "../../Loading/scripts/BundleControl";
import { BroadcastReceiver } from "../../Lobby/scripts/common/BroadcastListener";
import AnimationNumber from "../../Lobby/scripts/common/AnimationNumber";
import PopupHistory from "./Slot3x3.PopupHistory";
import Dialog from "../../Lobby/scripts/common/Dialog";
import PopupHonors from "./Slot3x3.PopupHonors";
import { NumberCounter } from "./NumberCounter";

const { ccclass, property } = _decorator;



@ccclass
export default class Slot3x3Controller extends MiniGame {



  @property(Node)
  winNode: Node = null;

    @property(Node)
    bettingNode: Node = null;
    @property(Label)
    lblJackpot: Label = null;
    @property(Label)
    lblPhien: Label = null;

    @property(Label)
    lblNumberAuto: Label = null;

    @property(Label)
    lblSpeed: Label = null;

    @property(Label)
    lblMoney: Label = null;
    isSpinning:boolean = false;
   @property(AnimationClip)
   aniClipWild: AnimationClip = null;
  @property(SlotLayer777)
  slotLayer: SlotLayer777;
  @property(Node)
  itemsNodeX: Node = null;

  @property(SlotLayer777X)
  slotLayerX: SlotLayer777X;
   
  indexBetting:number = 1;

  @property(Node)
  itemsNode: Node = null;



  @property(Node)
  autoNode: Node = null;
  @property(Node)
  nodeGold: Node = null;
  

  BetType:number = 1;

  @property(Node)
  spinNode: Node = null;

  @property(Node)
  stopNode: Node = null;

  @property(Node)
  bigwinNode: Node = null;
  
  @property(Node)
  jackpotNode: Node = null;
  betValue = [0,1000,5000,10000];
  autoNumber = [10,20,50,100,1000,5000];

  @property(Node)
  lineWin: Node = null;

    @property(Node)
    nhapNhap: Node = null;

       @property(Animation)
    wildAni: Animation = null;

 speed = 1;
    start() {
      this.lineWin.active = false;
      this.lblSpeed.string = "X"+this.speed.toString();
      this.spinNode.active = true;
      this.spinNode.getComponent(Button).interactable = true;
      this.stopNode.active = false;
      this.autoNode.parent.active = false;
     this.slotLayer.initRandom();
     this.slotLayerX.maxrandom = 3;
     this.slotLayerX.initRandom();
    var thiz = this;
     for (let index = 0; index < this.autoNode.children.length; index++) {
      
      this.autoNode.children[index].on("click", () => {
        thiz.onClickAutoNumber(thiz.autoNumber[5-index]);
                        });
      
        
      }
     

    var thiz = this;
    this.slotLayer.finishedHandler = function () {
      thiz.onFinishQuay();

    };
     this.slotLayer.playsoundStopItem = function (index) {
    log("finish line cot" + index);

    };
    this.slotLayerX.finishedHandler = function () {
      thiz.onFinishQuayX();

    };
     MiniGameSignalRClient.getInstance().receive('resultLWSpin', (data: any) => {
              // log(JSON.stringify(data));
              this.onResuft(data);
         });
           MiniGameSignalRClient.getInstance().receive('jackpotLW', (data: any) => {
                    this.onJackpot(data);
               });

         this.initBetting();
    }
    onJackpot(data){
      this.lblJackpot.string = Utils.formatNumber(data.JackpotFund);
    }
  initBetting(){
    for (let index = 0; index < this.bettingNode.children.length; index++) {
      
    this.bettingNode.children[index].on("click", () => {
      this.onClickBetting(index+1);
                      });
    
      
    }
    this.onClickBetting(1);
  }
onDestroy() {
      
        MiniGameSignalRClient.getInstance().dontReceive();
    }
  onClickCoin(target,data){
      if(this.isSpinning) return;
      if(this.BetType == 1){
        this.BetType = 2;
      }else{
        this.BetType = 1;
      }
        this.nodeGold.active = this.BetType == 1?true:false;
  
      MiniGameSignalRClient.getInstance().send('LWGetJackpot', [Configs.Login.CurrencyID, this.BetType, this.indexBetting], (data) => {
          this.lblPhien.string = "#";
      });
    }

 isMute = false;
actSound(event: Event){
      const buttonNode = event.target as unknown as Node;
       console.log("Button clicked: " + buttonNode.name);
       this.isMute = !this.isMute;
     buttonNode.children[0].active = this.isMute;
     this.stopSound(this.isMute);
    }

  onClickBetting(index1){
     this.playSound("Click",false);
    if(this.isSpinning) return;
    if(this.numberAuto>0) return;
    this.indexBetting = index1;
    for (let index = 0; index < this.bettingNode.children.length; index++) {
      
      this.bettingNode.children[index].children[1].active = (this.indexBetting-1) == index ? true:false;
      
      }
      MiniGameSignalRClient.getInstance().send('LWGetJackpot', [Configs.Login.CurrencyID, this.BetType, this.indexBetting], (data) => {
    
        });
  }

    numberAuto = 0;

    onClickSpeed(){
       this.playSound("Click",false);
      // if(this.isSpinning) return;
      this.speed++;
      if(this.speed>3){
        this.speed = 1;
      }
      this.slotLayer.setSpeed(this.speed);
      this.slotLayerX.setSpeed(this.speed);
      
      this.lblSpeed.string = "X"+this.speed.toString();
    }


    onClickAuto(){
       this.playSound("Click",false);
      if(this.numberAuto>0) return;
      this.autoNode.parent.active = true;
    
    }
    onClickHidenAuto(){
      this.autoNode.parent.active = false;
    }

    onClickAutoNumber(index1){
       this.playSound("Click",false);
      if(this.isSpinning) return;
      this.numberAuto = index1;
      this.autoNode.parent.active = false;
      this.lblNumberAuto.string = Utils.formatNumber(this.numberAuto);
      this.spinNode.active = false;
      this.stopNode.active = true;
      this.actSpin();
      
      }

      onClickStop(){

        this.numberAuto = 0;
        this.autoNode.parent.active = false;
        this.lblNumberAuto.string =Utils.formatNumber(this.numberAuto);
        this.stopNode.active = false;
                this.spinNode.active = true;
        this.spinNode.getComponent(Button).interactable = false;
            
       
      }
  
    mydata:any;
    getFrameCard(idz){
      // log("aaa " + idz);
      return this.itemsNode.children[Number(idz-1 )].getComponent(Sprite).spriteFrame;
    }

 
    // {"type":1,"target":"resultLWSpin","arguments":[{"PortalID":15,"CurrencyID":1,"BetType":1,
    //   "AccountID":100053,"Nickname":"tommy2","RoomID":1,"SpinID":20888,"BetValue":1000,
    //   "SlotData":"5,4,7,6,7,6,4,5,7","WildData":"","WildMultiplier":0,"PrizeValue":0,"IsJackPot":0,
    //   "JackPotFund":********,"Balance":********,"ResponseStatus":0}]}

     ConvertResulft(str){
     
      const numbers = str.split(",").map(Number);
    
      // Tạo ma trận 3x3 ban đầu
      const size = 3;
      const matrix: number[][] = [];
      for (let i = 0; i < size; i++) {
          matrix.push(numbers.slice(i * size, (i + 1) * size));
      }
      
      // Xoay ma trận 90 độ theo chiều kim đồng hồ
      const rotatedMatrix: number[][] = [];
      for (let col = 0; col < size; col++) {
          rotatedMatrix.push([]);
          for (let row = size - 1; row >= 0; row--) {
              rotatedMatrix[col].push(matrix[row][col]);
          }
      }
      return rotatedMatrix;
    }
    @property(Node)
    soundNode: Node;
    playSound(nameSound,isloop){

        var chidd = this.soundNode.getChildByName(nameSound);
        if(chidd){
         var audioSrc  =  chidd.getComponent(AudioSource);
            audioSrc.loop = isloop;
            audioSrc.play();
        }
 
    }
    stopSoundSpin(){

        var chidd = this.soundNode.getChildByName("MainSpin");
        if(chidd){
         var audioSrc  =  chidd.getComponent(AudioSource);
         
           audioSrc.stop();
        }
 
    }
  stopSound(isStop){
      this.soundNode.children.forEach(element => {
       var audioSrc =  element.getComponent(AudioSource);
       audioSrc.volume = isStop?0:1;
      });
       
    }

onResuft(data){
    if(data.ResponseStatus<0){
         App.instance.showErrLoading(App.instance.getTextLang("me-999002"));
      return;
    }

    this.lblPhien.string = "#"+ data.SpinID;

    //tesst
  //  data.SlotData = "5,4,7,1,7,1,4,5,7";
  //   data.WildData = "1,3";
  //  data.PrizeValue = 10000000;
    // data.IsJackPot = 1;
    this.mydata = data;
    setTimeout(() => {
            this.slotLayer.stopSlotWithResuft(this.ConvertResulft(data.SlotData));
    }, 800);
   // if(!this.isSieuToc){

  //  }else{
   //   this.slotLayer.stopNow(this.ConvertResulftPoker(valuesArray));
  //  }
  
  
   

  }
  // moveWild(idWild){
  //   instantiate(this.itemsNodeX.children[Number(idWild-1 )]);
  // }



  onFinishQuayX(){
 this.arrWildNode.forEach(element => {
         element.node.getComponent(Animation).stop();
       });
       this.nhapNhap.active = false;
            this.wildAni.enabled = false;

    log("stopX");
    var zzz1 = this.arrWildNode[0];
    var zzz2 =  instantiate(this.slotLayerX.arrResuftNew[0][0].node);
    zzz2.setScale(0.6,0.6,0.6);
    zzz2.position = new Vec3(40,-20,0);
    zzz2.parent = zzz1.node;
    // zzz1.node.getComponent(Animation).enable = true;
  //   zzz1.node.getComponent(Animation).play();
    log("stopX");
     this.playSound("GetWild",false);
    // this.arrWildNode.shift(); 
   
  }

  arrWildNode =[];

  findWild(){
    var arr = [];
    for (let index = 0; index < 3; index++) {

       const element = this.slotLayer.arrResuftNew[index][1];
       if(element.getComponent(Item77).idItemNew==1){
        arr.push(element);
       }
    
    }
    return arr;
  }


    showNhapNhay(isShow){
      this.winNode.children.forEach(element => {
          element.getComponent(UIOpacity).opacity = isShow?255:0;
      });
    }
    

    showEffectMoney(playsoud = true){
      var thiz = this;
      if(playsoud){
        this.playSound("Win",false);
      }
      thiz.lineWin.children[1].getComponent(Label).string = "0";
        thiz.lineWin.active = true;
                   thiz.lineWin.children[1].getComponent(AnimationNumber).runAction(2,thiz.mydata.PrizeValue,() => {
                    console.log("fnis anuimaon");
                 thiz.lblMoney.string  = Utils.formatNumber(thiz.mydata.PrizeValue);
    });
                   setTimeout(() => {
                  
                          thiz.lineWin.active = false;
                   }, 3000);
                    
    }

    onFinishQuay(){
        this.stopSoundSpin();
     var thiz = this;
           thiz.showNhapNhay(false);
        var arrWild =  this.mydata.WildData.split(",").filter(item => item.trim() !== "").map(Number);
 var ishaveWild = arrWild.length>0?true:false;
        this.arrWildNode = this.findWild();
      var timeDelay = 0;
       var timeBigwin = 0;
      if(this.mydata.PrizeValue>=this.betValue[this.indexBetting]*20 || this.mydata.IsJackPot > 0 ){
        //big win
        timeBigwin = 0.75;
      }
      if(thiz.mydata.PrizeValue>0){  // thắng sau 2s stop cai ben kia
        
        // if(!ishaveWild && timeBigwin == 0){
        //   thiz.showEffectMoney();
        // }
         for (let index = 0; index < thiz.winNode.children.length; index++) {
          const element =  thiz.winNode.children[index];
          setTimeout(() => {
             element.getComponent(Animation).play();
          }, 250*index);
          
        }

      
        for (let index = 0; index < arrWild.length; index++) {       
          var afterr = timeDelay;
          timeDelay  = timeDelay+2;
           this.scheduleOnce(() => {
            this.nhapNhap.active = true;
            this.wildAni.enabled = true;
                    this.slotLayerX.rotate();
                      var zzz1 = this.arrWildNode[index];
                        zzz1.node.getComponent(Animation).play();
                    this.slotLayerX.scheduleOnce(() => {
                     this.slotLayerX.stopSlotWithResuft(this.ConvertResulftWild([arrWild[index],1,1]));
                      
                    }, 1);
                  }, afterr+1);
         }
      }

      

       var timeWin = 0;
       if(this.mydata.PrizeValue>0){
        //win effect
        timeWin = 1;
  
      }
    
    
 tween(this.node)
            .call(() => {
                if (thiz.mydata.PrizeValue > 0) {
                    //thiz.winNode.active = true;
                }
            })
            .delay(timeWin + timeDelay)
            .call(() => {
                
               
                if(thiz.mydata.PrizeValue>0){
              //  if(ishaveWild){
                    thiz.showEffectMoney(ishaveWild?false:true);
              //  }   
                 
                   
 director.getScene().emit('ShowMoneyAuto', {
                                            money: thiz.mydata.PrizeValue,
                                            game: "luckyWild"
                                        });
                }
               


                if (timeBigwin > 0) {
                    if (thiz.mydata.IsJackPot > 0) {
                        thiz.jackpotNode.active = true;
                        thiz.jackpotNode.children[0].getComponent(Label).string = "0";
                        this.playSound("Jackpot",false);
                        thiz.jackpotNode.children[0].getComponent(AnimationNumber).runAction(1,thiz.mydata.PrizeValue);
                        setTimeout(() => {
                             thiz.jackpotNode.active = false;
                        }, 5000);
                        thiz.numberAuto = 0;
                        thiz.autoNode.parent.active = false;
                        thiz.spinNode.active = true;
                           thiz.spinNode.getComponent(Button).interactable = true;
                        thiz.stopNode.active = false;
                        thiz.lblNumberAuto.string = Utils.formatNumber(thiz.numberAuto);
                    } else {
                       thiz.bigwinNode.active = true;
                        thiz.bigwinNode.children[0].getComponent(Label).string = "0";
                        thiz.bigwinNode.children[0].getComponent(AnimationNumber).runAction(1,thiz.mydata.PrizeValue);
                            this.playSound("Bigwin",false);
                        setTimeout(() => {
                             thiz.bigwinNode.active = false;
                        }, 5000);
                    }
                }
            })
            .delay(0.25 + timeBigwin+(thiz.mydata.PrizeValue>0?3.5:0.5))
            .call(() => {
                BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
                thiz.isSpinning = false;
                thiz.spinNode.getComponent(Button).interactable = true;
              //  thiz.winNode.active = false;
                thiz.lblJackpot.string = Utils.formatNumber(thiz.mydata.JackPotFund);

                console.log("finish============");

                if (thiz.numberAuto > 1) {
                    thiz.numberAuto--;
                    thiz.lblNumberAuto.string = Utils.formatNumber(thiz.numberAuto);
                    thiz.actSpin();
                } else {
                    thiz.numberAuto = 0;
                    thiz.autoNode.parent.active = false;
                    thiz.spinNode.active = true;
                    thiz.spinNode.getComponent(Button).interactable = true;
                    thiz.stopNode.active = false;
                    thiz.lblNumberAuto.string = Utils.formatNumber(thiz.numberAuto);
                }
            })
            .start();
       
        
      }

    show() {
      if(!this.isMute){
          this.stopSound(false);
      }

if (this.node.active && this.node.position.x != -10000 ) {
      this.reOrder();
      return;
    }
    // App.instance.showBgMiniGame("MiniPoker");
    super.show();
    this.node.setPosition( v3(0,0,0));

        // if (this.node.active) {
        //     this.reOrder();
        //     return;
        // }
        // // App.instance.showBgMiniGame("Slot3x3");
        // super.show();
      
    }

    
    dismiss() {
      this.stopSound(true);
        if(this.numberAuto>0){
                 this.node.setPosition(v3(-10000,0,0));
             }else{
               super.dismiss();
                  MiniGameSignalRClient.getInstance().send('LWHide', [], (data) => {
               log(data);
             
               // handle callback
             });
             }
            
          
          //  for (let i = 0; i < this.popups.length; i++) {
          //    this.popups[i].active = false;
          //  }
      
    }

    public reOrder() {
        super.reOrder();

    }

    actHis(){
    BundleControl.loadPrefabGame(
        "Slot3x3",
        "PopupHistory",
        (finish, total) => {
          // App.instance.showErrLoading(App.instance.getTextLang('txt_loading1') + parseInt((finish / total) * 100) + "%");
        },
        (prefab) => {
          App.instance.showLoading(false);
          var his = instantiate(prefab)
            .getComponent(PopupHistory);
             his.node.parent = this.gamePlay;
            his.showCustom(this.BetType==1?0:1);
          // this.popups.push(this.popupHistory.node);
        }
      );
    }
    ConvertResulftWild(items){
      var arrItems: number[][] = [];
      for (var i = 0; i < 5; i++) {
          var tem:number[]= [];
          for (var j = 0; j <3; j++) {
            // var tem:number[]= [];
            tem.push(items[i]);
          
        }
         
          arrItems.push(tem);
      }
      return arrItems;
    }

    // getFrameWild(idz){
    //   log("aaa " + idz);
    //   return this.itemsNodeX.children[Number(idz-1 )].getComponent(Sprite).spriteFrame;
    // }
    

    actTut(){
    this.playSound("Click",false);

    

      BundleControl.loadPrefabGame(
        "Slot3x3",
        "PopupGuide",
        (finish, total) => {
          // App.instance.showErrLoading(App.instance.getTextLang('txt_loading1') + parseInt((finish / total) * 100) + "%");
        },
        (prefab) => {
          App.instance.showLoading(false);
          var his = instantiate(prefab)
            .getComponent(Dialog);
          his.node.parent = this.gamePlay;
          his.show();
          // this.popups.push(his.node);
        }
      );
    }

    actBxh(){
   this.playSound("Click",false);
 BundleControl.loadPrefabGame(
        "Slot3x3",
        "PopupHonors",
        (finish, total) => {
          // App.instance.showErrLoading(App.instance.getTextLang('txt_loading1') + parseInt((finish / total) * 100) + "%");
        },
        (prefab) => {
          App.instance.showLoading(false);
          var his = instantiate(prefab)
            .getComponent(PopupHonors);
          his.node.parent = this.gamePlay;
          his.show();
          // this.popups.push(his.node);
        }
      );
    }

    actSpin(even = null) {
       this.arrWildNode =[];
       this.playSound("MainSpin",false);
      log("=========actSpin");
      this.lineWin.active = false;
      this.bigwinNode.active = false;
      this.jackpotNode.active = false;
      this.lblMoney.string = "0";
      this.slotLayerX.initRandom();
      
        this.spinNode.getComponent(Button).interactable = false;
              this.showNhapNhay(true);
        this.slotLayer.rotate();
        this.isSpinning = true;
           var thiz = this;
           MiniGameSignalRClient.getInstance().send('LWSpin', [Configs.Login.CurrencyID, this.BetType, this.indexBetting], (data) => {
               log(data);
               if(data<0){
                 App.instance.ShowAlertDialog(App.instance.getTextLang("me"+data));
                 thiz.isSpinning = false;
                      thiz.spinNode.getComponent(Button).interactable = true;
                //  thiz.onClickStop(null,null);LWSpin
                 if (thiz.slotLayer.arrItemRandom != undefined && thiz.slotLayer.arrItemRandom.length != 0) {
                   thiz.slotLayer.showNotEffect(thiz.slotLayer.arrItemRandom);
                 }
                 else {
                   thiz.slotLayer.initRandom();
                   thiz.slotLayerX.initRandom();
                 }
               }
               // handle callback
             });
    }

   
}
