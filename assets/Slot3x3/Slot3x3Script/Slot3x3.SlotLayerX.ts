
import { _decorator,instantiate,log,Node, Sprite } from "cc";
import SlotLayer from "../../Lobby/scripts/common/SlotLayer";
import Item77X from "./Slot3x3.ItemSlotX";

const { ccclass, property } = _decorator;


@ccclass("SlotLayer777X")
export default class SlotLayer777X extends SlotLayer {
  indexBet:number = 1;
arrItemRandom:number[][] = [];
    newItem(idItem:string) {
        
        var newNode = instantiate(this.itemPrefab);
        newNode.active = true;
        newNode.getComponent(Item77X).moveSpeed = this.speed*1000;
        newNode.getComponent(Item77X).initItemDG( 107,150,idItem,this.indexBet);
        newNode.getComponent(Item77X).soCot = this.soCot;
        return newNode;
    }
    

    initRandom(){
        this.clearAll();
        this.arrItemRandom = [];
        for (var i = 0; i < this.soCot; i++) { // cot

            var subItem = [];
            var subItemRan = [];
            for (var j = 0 ; j < 4; j++) { // hang
                var randomItem = "1";
                var item = this.newItem(randomItem.toString());
                var itemTemp = item.getComponent(Item77X);
                itemTemp.createItem(i,j,0);
                this.nodeSlot.addChild(item);
                subItemRan.push(randomItem);
                subItem.push(item);
                itemTemp.isRunning = false;

            }
             this.arrItemRandom.push(subItemRan);
            this.arrItems.push(subItem);
        }
    }
    showWild(arrItemWild) {
        console.log("showWild");
        for(var i = 0; i < arrItemWild.length; i++){
            for(var j = 0 ; j < 3; j++){
             //   this.arrResuft[arrItemWild[i]*3+j].getComponent(ItemPK).setWild();
            }
        }
    }

    showLineWin(line:number,mask:number) {

    }
    clearAllItemInLine() {

        for(var i = 0; i <  this.arrResuft.length; i++)
        {

            this.arrResuft[i].getComponent(Item77X).setWin(false,false);
            // this.arrResuft[i].spriteHoaQua.setOpacity(255);
        }

    }
}
