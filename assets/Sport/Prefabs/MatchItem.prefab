[{"__type__": "cc.Prefab", "_name": "MatchItem", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "MatchItem", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 137}, {"__id__": 1041}], "_active": true, "_components": [{"__id__": 1051}, {"__id__": 1053}, {"__id__": 1055}, {"__id__": 1057}, {"__id__": 1059}], "_prefab": {"__id__": 1061}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 882.114, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "QuickView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}, {"__id__": 23}, {"__id__": 33}, {"__id__": 43}, {"__id__": 53}, {"__id__": 63}, {"__id__": 82}, {"__id__": 101}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 128}, {"__id__": 130}, {"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 229.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "star", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": -904, "y": 44.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "605e070c-fad9-437f-a3e8-d775b9acb318@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cr+UdqHtBto1bl0ilaDnK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_alignFlags": 9, "_target": null, "_left": 35, "_right": 0, "_top": 35, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eBSo2i2xFCaNG8BlPbzT9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5F/XIGy1LAYgjx2aCpCZ4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ece1MgArBCP5P/RXJFUe4g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7P1jm5NJPyY319zDatlxJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TimeLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": -850, "y": 43.620000000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 46, "g": 161, "b": 103, "a": 255}, "_string": "Thu 23:00", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5XjlDnFFOd5oH+w2K3wkl"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 9, "_target": null, "_left": 110, "_right": 0, "_top": 40, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1B5J9/MlLE7PFcipHQ60K"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5ZqCeJ/VMqpspQT94O0aO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1drNSzhONM/o3zrL9GKIJK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23RYvPL21PRL6X+ZSLoDNr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "MatchTitle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": -595, "y": 39.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "FC Rapperwil-jona\nFC Sion", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11XkNf5nRJua65OKWLfrmJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 27}, "_alignFlags": 9, "_target": null, "_left": 365, "_right": 0, "_top": 15, "_bottom": 0, "_horizontalCenter": -45, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eahbzF8dMirG3q5yg9uaF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 29}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2e4xSKBYVKvohr00YRHqaN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 31}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 90.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd6BYZ3hFM1IHMkymGpp25"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "06R2JmGRpIArzLQkRTVgDu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ResultLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": -70.71900000000005, "y": 39.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 35}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_string": "0\n0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9/WeCln9ITIZyYNQcDhHB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 37}, "_alignFlags": 9, "_target": null, "_left": 889.281, "_right": 0, "_top": 15, "_bottom": 0, "_horizontalCenter": -45, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84Mj0QG+BKoJDE0pMnR7vh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 39}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccpxu7SyVEMpP++GAzLVY2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 41}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 90.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dONBerVZCbqS7CnYf05pl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfXqjjAQNPMJM1UIOozUGI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Line", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 50}], "_prefab": {"__id__": 52}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -31, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 45}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 102, "g": 102, "b": 102, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59771gQmtPbKID0uyeT7U9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 47}, "_alignFlags": 41, "_target": null, "_left": 50, "_right": 50, "_top": 130, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22Pj7PijhG17aj242SLk/F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 49}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ddozBDLZNAJgX0kZVQ/1N"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 43}, "_enabled": true, "__prefab": {"__id__": 51}, "_contentSize": {"__type__": "cc.Size", "width": 1820, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81N53ZJ8BL2Irtp2a40MYj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dAj9PyHZIVbN/ueThNywG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "OtherBets", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": -315, "y": -65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 55}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 155, "g": 163, "b": 180, "a": 255}, "_string": "<PERSON><PERSON> c<PERSON> Châu <PERSON>c", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8c0tVu3z5HQayl1ykjbW2C"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 57}, "id": "spr124", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "99oS0qe5RBPqDmyj1Vv7ds"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 59}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6emdColb5HdqSfL7bxC3hB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 61}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 33}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a2crI/uhJja+cU89gtHSX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80TcfOyz1GVpHWUycZ20Wh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 64}], "_active": true, "_components": [{"__id__": 72}, {"__id__": 74}, {"__id__": 77}, {"__id__": 79}], "_prefab": {"__id__": 81}, "_lpos": {"__type__": "cc.Vec3", "x": -155, "y": -68, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 63}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 67}, {"__id__": 69}], "_prefab": {"__id__": 71}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 66}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cyIx9zv9NrLLC8Q5cWH7q"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 68}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4GkdLh9lJbZhUi8aG9RSp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58jQ4qz0BJ1aO6MuyeBEIo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebM/QcW/lLQq8Lk1CACW5k", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 73}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7b9Yvuf0xNLYhNKXq83JjJ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 75}, "clickEvents": [{"__id__": 76}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f65/3GQuhG7pa74J9UlfwX"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "openPreMatch", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 78}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2Z+Fih0RJerZ7Ai0TkmNa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 80}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82SttbcxRLUY21JPDAet+m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50KjaNQ6VHu6/KrEnPsXwM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 83}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": 92.583, "y": -68, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 82}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 85}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eUDaG/P9GVJ+0mFkPYJ44"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 87}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3GNZYE51BmJqA8HumtgnD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaVF85+gZEtLWgMJldApWb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99GxbkalNNx4KtQ8qpTfSe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 92}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2anFqoqhN1IS+tp341f5c"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 94}, "clickEvents": [{"__id__": 95}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3Ta3Q0YhM3J4duAuBBP8Y"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "openLiveMatch", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 97}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66tMFrMDVJeKdMmLW2e6ys"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 99}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09iBewCBFICZadEU8nqNKw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75xH+ou/5O0Z62uJehio+l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "OtherBets", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}, {"__id__": 106}], "_prefab": {"__id__": 108}, "_lpos": {"__type__": "cc.Vec3", "x": 13.557, "y": -65, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 155, "g": 163, "b": 180, "a": 255}, "_string": "179+", "_horizontalAlign": 2, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3W3d5u7tDTrcglNP37sop"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71v8buOjZIs6HoVMV5R/3I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 107}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69giitwNtJi5xlBPhb7CFK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35AvOpFGpD/adTSRB4ZoNY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 110}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 120}, {"__id__": 123}, {"__id__": 125}], "_prefab": {"__id__": 127}, "_lpos": {"__type__": "cc.Vec3", "x": 425, "y": -68, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}, {"__id__": 115}], "_prefab": {"__id__": 117}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "313WBXbTVKjLFvf3WGLTVv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15TSSbh+NC6rL7TiqXjQ2R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 116}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94uzMrLMRAJ7z6WhmOPXbx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bdgsHfeRAAI5OSP5NYCn9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 119}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fbGQco6hCYY+t5QLAsNZU"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 121}, "clickEvents": [{"__id__": 122}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": {"__id__": 109}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85ReQviRNLibi+z0Fghneq"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "activeDetail", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 124}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22pV9BNl1IVICqfBYbgo+W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 126}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "966yew4TZG4668xxO3OQqI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c04lYS/UNDbLnzAbLZ3DH8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 129}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8FHxA30ROf7AQ2VsVWkbO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 131}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 38, "g": 39, "b": 43, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a95SER/X9FY5cm96hEnMU/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 133}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ayGWnTpJJG6AlU1De6QyK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 135}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c90hQOpX5Ea6Z0xmmQbHNt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceM0W7biNN07HhQh5wzLZV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "DetailsView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 138}, {"__id__": 267}, {"__id__": 336}, {"__id__": 597}, {"__id__": 858}, {"__id__": 949}], "_active": true, "_components": [{"__id__": 1032}, {"__id__": 1034}, {"__id__": 1036}, {"__id__": 1038}], "_prefab": {"__id__": 1040}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 129.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 139}, {"__id__": 160}, {"__id__": 170}, {"__id__": 180}, {"__id__": 196}, {"__id__": 214}, {"__id__": 232}], "_active": true, "_components": [{"__id__": 260}, {"__id__": 262}, {"__id__": 264}], "_prefab": {"__id__": 266}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "star", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 140}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 155}, {"__id__": 157}], "_prefab": {"__id__": 159}, "_lpos": {"__type__": "cc.Vec3", "x": -904, "y": 10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "checked", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 139}, "_children": [], "_active": false, "_components": [{"__id__": 141}, {"__id__": 143}, {"__id__": 145}], "_prefab": {"__id__": 147}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 142}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0ca9da82-2ce6-4e44-ba87-215f27d744fa@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29rBiL1xBKKLR3r7xv1Chg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 144}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fKqEp5tlJt77A644C1ZfN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 140}, "_enabled": true, "__prefab": {"__id__": 146}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fNxiBH+pJcIlT6FKGDnW6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86kL7pnb9NJ7bF4Pa3TeGI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "605e070c-fad9-437f-a3e8-d775b9acb318@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aGkhmMHFEUrxg8uSwuUaO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 151}, "_alignFlags": 9, "_target": null, "_left": 35, "_right": 0, "_top": 4.500000000000001, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05078bGRxETYEmuA0bVASN"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 153}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 139}, "checkEvents": [{"__id__": 154}], "_isChecked": false, "_checkMark": {"__id__": 141}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48ESY7tYtNg6f/GCqoyDEC"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "onClickAddFavortite", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 156}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeNhe/KntC2qeqAFdhCamy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 139}, "_enabled": true, "__prefab": {"__id__": 158}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 41}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71flk+/e1DWZOhvyMVIk4Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c24eizYipOL6XTuwkiMBoF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TimeLabel", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 163}, {"__id__": 165}, {"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": -850, "y": 9.999999999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 162}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 46, "g": 161, "b": 103, "a": 255}, "_string": "Thu 23:00", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dkhDzUTBKRaRuU3opfNow"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 164}, "_alignFlags": 9, "_target": null, "_left": 110, "_right": 0, "_top": 8.620000000000005, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7gIRqGYtM8J8g//RND5mA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 166}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cLOcpOfpG7Ygpo72i1its"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 168}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bNkTW8eFGc41eEFO1d5x3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbl6TaMuhGP7KJqdPkBsp8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Line", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}, {"__id__": 175}, {"__id__": 177}], "_prefab": {"__id__": 179}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 172}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 102, "g": 102, "b": 102, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d44jNVRJVGRpIbbRkpCNC3"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 174}, "_alignFlags": 41, "_target": null, "_left": 50, "_right": 50, "_top": 59, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c2zmjuRlCFbgBH7danwsx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 176}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cf1Am/FDBELqMd+uF5dITf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 178}, "_contentSize": {"__type__": "cc.Size", "width": 1820, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e22WbEZ+5GG5iUwvm7Lkrc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ad49ZHqhMn42YuQH4P1SB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Bet1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 181}], "_active": true, "_components": [{"__id__": 189}, {"__id__": 191}, {"__id__": 193}], "_prefab": {"__id__": 195}, "_lpos": {"__type__": "cc.Vec3", "x": 233.315, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetValue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 180}, "_children": [], "_active": true, "_components": [{"__id__": 182}, {"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 183}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 117, "g": 122, "b": 132, "a": 255}, "_string": "1x2", "_horizontalAlign": 1, "_verticalAlign": 2, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1fEGHHIBAlpVpbteamFCg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 185}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7087KJ/NlOGbyHQydlOjel"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 181}, "_enabled": true, "__prefab": {"__id__": 187}, "_contentSize": {"__type__": "cc.Size", "width": 35.470703125, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27oA/GsF1FfJA8q0CQenGC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9etrzQbp5NkKqsYw65CWxt", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 190}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON> trận", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1H2CAYSJP/rfhYULauv2L"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 192}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfD0vBT/FKU70u0er7GZ6/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 194}, "_contentSize": {"__type__": "cc.Size", "width": 91.7275390625, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fi7fhcdBA0763AoofdFGG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e7Gg6qi/ZHW4j6NqkwZMkN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Bet2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 197}], "_active": true, "_components": [{"__id__": 207}, {"__id__": 209}, {"__id__": 211}], "_prefab": {"__id__": 213}, "_lpos": {"__type__": "cc.Vec3", "x": 353.315, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetValue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 196}, "_children": [], "_active": true, "_components": [{"__id__": 198}, {"__id__": 200}, {"__id__": 202}, {"__id__": 204}], "_prefab": {"__id__": 206}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 199}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 117, "g": 122, "b": 132, "a": 255}, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 2, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "932CkTuUlMmo4EpZoDdo6l"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 201}, "id": "spr126", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80VT0aR/5PUYeA+sz9jQ9+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 203}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cWd18gGpMZq13K2PEzTcx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 205}, "_contentSize": {"__type__": "cc.Size", "width": 52.59375, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3biJhcsVlNhpRP7+PYLtM2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27SkfQun5JMqYoAnstSijc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 208}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON> trận", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3qNHcXrxMI5u3xaLrwpY3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 210}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9GNsCBe9F3LcL+HNNoG9e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 196}, "_enabled": true, "__prefab": {"__id__": 212}, "_contentSize": {"__type__": "cc.Size", "width": 91.7275390625, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f/c3Hs89Hl5uqH5N9cMwJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89Y5uuz7ZPa6Nx4wq12Hkc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Bet3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 215}], "_active": true, "_components": [{"__id__": 225}, {"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 463.315, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetValue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 214}, "_children": [], "_active": true, "_components": [{"__id__": 216}, {"__id__": 218}, {"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -27, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 217}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 117, "g": 122, "b": 132, "a": 255}, "_string": "Tài/Xỉu", "_horizontalAlign": 1, "_verticalAlign": 2, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0/KEBXMlBFbSTbPnak93S"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 219}, "id": "spr127", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10gGl6ZZ1LUouDtmZ1XuT4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 221}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8e4J1oUqBAIZx5/Cqj4k5S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 215}, "_enabled": true, "__prefab": {"__id__": 223}, "_contentSize": {"__type__": "cc.Size", "width": 68.470703125, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1BZ3CevhGbIhfktKX852u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2M6JukOtKe6mgR3pxwqUi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 226}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON> trận", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21iJA2EGxCN6Z/3TvWPsJP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 228}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5uL5blelIpoVrhnmcABvE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": {"__id__": 230}, "_contentSize": {"__type__": "cc.Size", "width": 91.7275390625, "height": 31.5}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c1t5FMn5NlIIKkRqEXO6I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72TUtZtkNDAL5HQZTaNhK9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ButtonLayout", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 138}, "_children": [{"__id__": 233}, {"__id__": 244}], "_active": true, "_components": [{"__id__": 255}, {"__id__": 257}], "_prefab": {"__id__": 259}, "_lpos": {"__type__": "cc.Vec3", "x": -100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Button Live", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 232}, "_children": [], "_active": false, "_components": [{"__id__": 234}, {"__id__": 236}, {"__id__": 239}, {"__id__": 241}], "_prefab": {"__id__": 243}, "_lpos": {"__type__": "cc.Vec3", "x": 29.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 235}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd9f53e6-76de-4268-8712-61169142b216@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e00c6UH19PiJK3p6vtfX5S"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 237}, "clickEvents": [{"__id__": 238}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fehKGUdx1Fi7LNsc6AwU9V"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "watchLive", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 240}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98IW22PN9JSLXYGfAIIDem"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 242}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1YymTatZLNaqsVn90alYo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56PVhK2O5N37tzSpXrWTSU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Button Statics", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 232}, "_children": [], "_active": false, "_components": [{"__id__": 245}, {"__id__": 247}, {"__id__": 250}, {"__id__": 252}], "_prefab": {"__id__": 254}, "_lpos": {"__type__": "cc.Vec3", "x": 110, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": {"__id__": 246}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7043ad01-74d7-4b76-bc2c-0e99e7093d88@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3k62vf1hFLpErawjg4bD/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": {"__id__": 248}, "clickEvents": [{"__id__": 249}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55RIwZuCBOu58hHciWeI2/"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "openStat", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": {"__id__": 251}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01Ycy2ZfBIWrjU7VRUNtgt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 244}, "_enabled": true, "__prefab": {"__id__": 253}, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7R8ObIB1Dda41R3+oBEeb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1d7zBqQdlCqLbZVykjjGv3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 256}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0UVTeZx5Dor2+Iydpx9zR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 258}, "_contentSize": {"__type__": "cc.Size", "width": 131, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48V61QvwVFWYfBe3kb98EB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56Z0sOAe5DfbEkyjG2xvFO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 261}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1J3wcky5OPZJYorZgXaLk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 263}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9LkCUwXJAFa2zHIQjmhOJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0brHiczT1JJ4mPxboafIS4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2gALnUQxN8aVo2NgB+t/K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Slide<PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 268}], "_active": true, "_components": [{"__id__": 329}, {"__id__": 331}, {"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -77.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 267}, "_children": [{"__id__": 269}, {"__id__": 311}], "_active": true, "_components": [{"__id__": 319}, {"__id__": 322}, {"__id__": 324}, {"__id__": 326}], "_prefab": {"__id__": 328}, "_lpos": {"__type__": "cc.Vec3", "x": 760, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 268}, "_children": [{"__id__": 270}], "_active": true, "_components": [{"__id__": 302}, {"__id__": 304}, {"__id__": 306}, {"__id__": 308}], "_prefab": {"__id__": 310}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 269}, "_children": [{"__id__": 271}, {"__id__": 279}, {"__id__": 287}], "_active": true, "_components": [{"__id__": 295}, {"__id__": 297}, {"__id__": 299}], "_prefab": {"__id__": 301}, "_lpos": {"__type__": "cc.Vec3", "x": -200, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "page_1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 270}, "_children": [], "_active": true, "_components": [{"__id__": 272}, {"__id__": 274}, {"__id__": 276}], "_prefab": {"__id__": 278}, "_lpos": {"__type__": "cc.Vec3", "x": 200, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 273}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 200, "b": 200, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37X3hDOORMfqesjFAjiBMA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 275}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1A+nz1b5L/LOvTr9xdf1z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 290}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e5TKBLFfZPC4zjqsuyXs5x"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0b3RH995MDKRMBsR70iVB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "page_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 270}, "_children": [], "_active": true, "_components": [{"__id__": 280}, {"__id__": 282}, {"__id__": 284}], "_prefab": {"__id__": 286}, "_lpos": {"__type__": "cc.Vec3", "x": 600, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 281}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 255, "b": 200, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bakUM5LhRAP5ZVpTuigHP+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 283}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23wcKUQYROL7Ah8iVQmjZr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 279}, "_enabled": true, "__prefab": {"__id__": 285}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 290}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61SyOF1tFOpo5eACbaU4B+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d05730IuJH94DgoUqaOFUm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "page_3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 270}, "_children": [], "_active": true, "_components": [{"__id__": 288}, {"__id__": 290}, {"__id__": 292}], "_prefab": {"__id__": 294}, "_lpos": {"__type__": "cc.Vec3", "x": 1000, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 289}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08+pYvdRpL+7f0B/2SeO9c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 291}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6nJF9aIxAr5a19uRjeRoS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 293}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 290}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcSaNpc0lL6KTex41lKIiG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75jWd95VpO+JwcyKycCetk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 296}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 15, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "85sjozlqFMpb1TOHDG2TdM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 298}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dbs3Gj2RNpagcZ67xGlDE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 300}, "_contentSize": {"__type__": "cc.Size", "width": 1200, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09WAYPKL5CroFtPZKRvf2b"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fb2QgCywlF7b11Fz0c7J6U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 303}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e0fgO2hFN1rszIkZJIWMY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 305}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73DshuOcBCNKy7gQyozyc2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 307}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43VcbtwTJPjo838UxuUssR"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 269}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 205, "g": 190, "b": 228, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "089/c9R6ZBQJMLgQ896NoT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bv1qQWuxNH4LV9dDDysVq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "indicator", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 268}, "_children": [], "_active": true, "_components": [{"__id__": 312}, {"__id__": 314}, {"__id__": 316}], "_prefab": {"__id__": 318}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -310, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.PageViewIndicator", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 313}, "spacing": 10, "_spriteFrame": {"__uuid__": "5ed98ae1-f38d-4af1-8fe6-447c372ad250@f9941", "__expectedType__": "cc.SpriteFrame"}, "_direction": 0, "_cellSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fatfVuCRGvoCWXLY0JiO4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 315}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "057nIWnrRP8I8k810Uj/CZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 311}, "_enabled": true, "__prefab": {"__id__": 317}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11zpnIn/BLnbg3cit+hku2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04meHiSVxOkpmF774UwjjX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON>.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 320}, "bounceDuration": 0.5, "brake": 0.5, "elastic": false, "inertia": false, "horizontal": true, "vertical": true, "cancelInnerEvents": false, "scrollEvents": [], "_content": {"__id__": 270}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "autoPageTurningThreshold": 100, "pageTurningSpeed": 0.3, "pageEvents": [{"__id__": 321}], "_sizeMode": 0, "_direction": 0, "_scrollThreshold": 0.5, "_pageTurningEventTiming": 0.1, "_indicator": {"__id__": 312}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "024Ls8zHxOvIaaXo+O9FBX"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "onSlidePageView", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 323}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d92xy1j/hJmbt3s3qXx8fd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 325}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23z5z/o3NDfpKwjgNZGP6E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 327}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 350}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fJ0JtDORLGqngS26bTvzn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1AVh0Z6VLtrsga6CBMj4l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 330}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73raj/hb1CWKGZHLX8GddZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 332}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92Ms8Axj1ErJkZaQxyoGI5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 267}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bIn0GdvBOxbtNQ11oSt/z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f5nl8t4zdCnL80eAZpDSkr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Team1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 337}, {"__id__": 347}, {"__id__": 357}, {"__id__": 550}], "_active": true, "_components": [{"__id__": 590}, {"__id__": 592}, {"__id__": 594}], "_prefab": {"__id__": 596}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -135, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [], "_active": true, "_components": [{"__id__": 338}, {"__id__": 340}, {"__id__": 342}, {"__id__": 344}], "_prefab": {"__id__": 346}, "_lpos": {"__type__": "cc.Vec3", "x": -920, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 339}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "FC Rapperwill-Jona", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eQXLQ945FNru6uW9+pvzL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 341}, "_alignFlags": 8, "_target": null, "_left": 40, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8ZKZhjG5PEoz6HZY7fm1D"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 343}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32UbqvPj1L/6THUBUDkqQw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 345}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7k0SdTGZOCqD4if+4S5RW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05OrG6OTxMhrRJBlOk4yll", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Score", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [], "_active": true, "_components": [{"__id__": 348}, {"__id__": 350}, {"__id__": 352}, {"__id__": 354}], "_prefab": {"__id__": 356}, "_lpos": {"__type__": "cc.Vec3", "x": -387.62199999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 349}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afjVRWDLFGSLlvMrUCvJ+c"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 351}, "_alignFlags": 8, "_target": null, "_left": 522.378, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6a6cwrXX1ERaYLMBo6bDFN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 353}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01Lc/TL+dNQ6p6PBBX4J/G"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 347}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e8eLpgoROnoNHglsizrat"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1+5ZViwVJGL1VpBI9iYFS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "LayoutBet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [{"__id__": 358}, {"__id__": 419}, {"__id__": 480}], "_active": true, "_components": [{"__id__": 541}, {"__id__": 543}, {"__id__": 545}, {"__id__": 547}], "_prefab": {"__id__": 549}, "_lpos": {"__type__": "cc.Vec3", "x": 626, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 359}, {"__id__": 367}, {"__id__": 375}, {"__id__": 391}, {"__id__": 399}], "_active": true, "_components": [{"__id__": 407}, {"__id__": 410}, {"__id__": 412}, {"__id__": 414}, {"__id__": 416}], "_prefab": {"__id__": 418}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [], "_active": true, "_components": [{"__id__": 360}, {"__id__": 362}, {"__id__": 364}], "_prefab": {"__id__": 366}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 361}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65qANv+PBHlIZ9a0iDCntZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 363}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aKOYR+ChNWbR71t9MTDIf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 359}, "_enabled": true, "__prefab": {"__id__": 365}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "79krrH3TtBZLHXIASS73FK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "110dH+h+ZDpoaT9jLQ2X2a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [], "_active": true, "_components": [{"__id__": 368}, {"__id__": 370}, {"__id__": 372}], "_prefab": {"__id__": 374}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 369}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ehxvzJa5BRbJm69Xgh4Re"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 371}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbnI1i311IV6tJMHw9KSfx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 367}, "_enabled": true, "__prefab": {"__id__": 373}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9H3EIEHFOnJZsTuukgv/Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "baUa4aQjpL65xVyhMmcM1O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [{"__id__": 376}], "_active": false, "_components": [{"__id__": 384}, {"__id__": 386}, {"__id__": 388}], "_prefab": {"__id__": 390}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 375}, "_children": [], "_active": true, "_components": [{"__id__": 377}, {"__id__": 379}, {"__id__": 381}], "_prefab": {"__id__": 383}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 378}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98p/CsKPBM9YfHaotMKbsd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 380}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1iYm7mv9EV5sM8k8u9+NV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 376}, "_enabled": true, "__prefab": {"__id__": 382}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12AsSgMSdAsq4AsZ+dJ3SW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69Eqvb8x1Mno+9FbYpiDAR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 385}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6fjvWemtAG4s1DgHhI6Uu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 387}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bkN0Ubb5Dj5l1BNLMDrU0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 375}, "_enabled": true, "__prefab": {"__id__": 389}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aS2qHbIJPjIXeGbAPnYuf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4b8EGbIVGqoLL4Np45jsL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [], "_active": false, "_components": [{"__id__": 392}, {"__id__": 394}, {"__id__": 396}], "_prefab": {"__id__": 398}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08aQC4GxBM/ZuuR8MwlJ3Y"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 395}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61VN8REYNL1bybMyfkmbGa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 397}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14H+BJnt5I0ay/PWoIUIUf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1pIC9ixlPvYhV+mqTCaAE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 358}, "_children": [], "_active": false, "_components": [{"__id__": 400}, {"__id__": 402}, {"__id__": 404}], "_prefab": {"__id__": 406}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 401}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5Y6bOIslO0Z1AWL3si5se"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 403}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5656v/QWFPRLKniT5xVP/2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 399}, "_enabled": true, "__prefab": {"__id__": 405}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65YYd1KLZNB67EzCH8BS2Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f13W2nRLZNQJ4ikOE0KIMP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 408}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 359}, "checkEvents": [{"__id__": 409}], "_isChecked": false, "_checkMark": {"__id__": 384}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0aesKNh9N5ohr9KCaPgpn"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 358}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 411}, "Name": null, "BetPoint": {"__id__": 368}, "BetPointActive": {"__id__": 377}, "BetPointActiveWithPrice": {"__id__": 400}, "PriceLabel": {"__id__": 392}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49vUWf6EhLD4XVYWsdRCKX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 413}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92Xd8xBcNOGoqwXY0Ybahw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 415}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7fm77bDxM/o+IvElHoNLs"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 358}, "_enabled": true, "__prefab": {"__id__": 417}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21lqY0BLlPEZi35ndYi8ib"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9erXAOQRhCS6D9ajMMMlo9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 420}, {"__id__": 428}, {"__id__": 436}, {"__id__": 452}, {"__id__": 460}], "_active": true, "_components": [{"__id__": 468}, {"__id__": 471}, {"__id__": 473}, {"__id__": 475}, {"__id__": 477}], "_prefab": {"__id__": 479}, "_lpos": {"__type__": "cc.Vec3", "x": 157, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 419}, "_children": [], "_active": true, "_components": [{"__id__": 421}, {"__id__": 423}, {"__id__": 425}], "_prefab": {"__id__": 427}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 422}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7/kQgKLtBTIjXzhpfbH1Y"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 424}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6a/gLLwOtNUpY/ctg37N9Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 420}, "_enabled": true, "__prefab": {"__id__": 426}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "da0gtmb5dHrYv7+uTx4Bxu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "29aNdFNRtKcrczfEnBkYuJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 419}, "_children": [], "_active": true, "_components": [{"__id__": 429}, {"__id__": 431}, {"__id__": 433}], "_prefab": {"__id__": 435}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 430}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9y2aOv5JD+IBiQnNDwOjd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 432}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ePF9u7ohPi6NTsUBKQsL4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 434}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7a1IPDc8ZJhrBXt/pCHbp+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33yK4FkqhHVaqu9Vly+R0S", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 419}, "_children": [{"__id__": 437}], "_active": false, "_components": [{"__id__": 445}, {"__id__": 447}, {"__id__": 449}], "_prefab": {"__id__": 451}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 436}, "_children": [], "_active": true, "_components": [{"__id__": 438}, {"__id__": 440}, {"__id__": 442}], "_prefab": {"__id__": 444}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 439}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8c3a6L1clNVbgQFA19uPfh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 441}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74J/8fLgZJkqSXhQea26kr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 437}, "_enabled": true, "__prefab": {"__id__": 443}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccEq87IVxBuZ6GW+369WYm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49jesFztlFZKSl+BEUbH8b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 446}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93Khf7XnVKKazVjSWNlTSG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 448}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dvm2yGUdA3ppjVHZgdSz7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 450}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e73F7P5whOGrJJOUaczcGJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8eEzZwdpAd5y9HBUqGUgq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 419}, "_children": [], "_active": false, "_components": [{"__id__": 453}, {"__id__": 455}, {"__id__": 457}], "_prefab": {"__id__": 459}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 454}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe9ngZdqhIFqFzDtOvFaYz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 456}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f3RdBABpLDLI09NxnziRW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 458}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "425b0Id5hAF6QvQIk3fy7t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2er5c58qRG8YvYDuNL2RkP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 419}, "_children": [], "_active": false, "_components": [{"__id__": 461}, {"__id__": 463}, {"__id__": 465}], "_prefab": {"__id__": 467}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 462}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bejFgJwOJFprm9tIYEAt7J"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 464}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87DgcTicZJD4MnfUb8pnUs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 460}, "_enabled": true, "__prefab": {"__id__": 466}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90pa9cNatCo7vOvhIh/ScT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34Wk/85aZCYJVnvHZ8/mpx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": {"__id__": 469}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 420}, "checkEvents": [{"__id__": 470}], "_isChecked": false, "_checkMark": {"__id__": 445}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdvcV+iSdBf7JWCPUm/rqi"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 419}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": {"__id__": 472}, "Name": null, "BetPoint": {"__id__": 429}, "BetPointActive": {"__id__": 438}, "BetPointActiveWithPrice": {"__id__": 461}, "PriceLabel": {"__id__": 453}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c98TC5hRhMd42hcquUqGqC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": {"__id__": 474}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25G/0gomtNc41GAo8hakbN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": {"__id__": 476}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9fYMuY6BCKYR5u8QYBaQP"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 419}, "_enabled": true, "__prefab": {"__id__": 478}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "de8+5mLcVJxpQeGCBnx92g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fesjhyRNIWLGTowR2vQKu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 357}, "_children": [{"__id__": 481}, {"__id__": 489}, {"__id__": 497}, {"__id__": 513}, {"__id__": 521}], "_active": true, "_components": [{"__id__": 529}, {"__id__": 532}, {"__id__": 534}, {"__id__": 536}, {"__id__": 538}], "_prefab": {"__id__": 540}, "_lpos": {"__type__": "cc.Vec3", "x": 270, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 480}, "_children": [], "_active": true, "_components": [{"__id__": 482}, {"__id__": 484}, {"__id__": 486}], "_prefab": {"__id__": 488}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 481}, "_enabled": true, "__prefab": {"__id__": 483}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ePHNZnLRJ37KjZdAu3gdb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 481}, "_enabled": true, "__prefab": {"__id__": 485}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eblh0fARFbpBdaxCgGPsY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 481}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58LBW0+bJOQZKodv/6Bnvt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbrxC3DcNLVraRmKvjl1iK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 480}, "_children": [], "_active": true, "_components": [{"__id__": 490}, {"__id__": 492}, {"__id__": 494}], "_prefab": {"__id__": 496}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 491}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9Fspm7JRBkJI8bd1BcmsZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 493}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91wjckQ/FBdKX0u/1jH+6S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 489}, "_enabled": true, "__prefab": {"__id__": 495}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59GCO7XSFKTIpyf/aU2Mqi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbedzshENLHbw5IqcOu+sq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 480}, "_children": [{"__id__": 498}], "_active": false, "_components": [{"__id__": 506}, {"__id__": 508}, {"__id__": 510}], "_prefab": {"__id__": 512}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 497}, "_children": [], "_active": true, "_components": [{"__id__": 499}, {"__id__": 501}, {"__id__": 503}], "_prefab": {"__id__": 505}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 500}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3K49QY4JB87my/JSl6Swl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 502}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c73btVNuNOhpdo+Y6OC//I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 498}, "_enabled": true, "__prefab": {"__id__": 504}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9qxtaMmJPU5hAqPoDuvIK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8w4MFlQtKUIoQS88xSi0q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 497}, "_enabled": true, "__prefab": {"__id__": 507}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65eDZQsDZH5IBj6XGkmMez"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 497}, "_enabled": true, "__prefab": {"__id__": 509}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbKPECsOtOuIByIJGD8+Pt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 497}, "_enabled": true, "__prefab": {"__id__": 511}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aN0ylT3NAWYk8Avht6tpl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b82h0SqB5D67W//9Ld1YJ/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 480}, "_children": [], "_active": false, "_components": [{"__id__": 514}, {"__id__": 516}, {"__id__": 518}], "_prefab": {"__id__": 520}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 513}, "_enabled": true, "__prefab": {"__id__": 515}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06tVQ7n3ZEGoa6glkjeRvP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 513}, "_enabled": true, "__prefab": {"__id__": 517}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62qLq1bAFE0rCRewNwETFm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 513}, "_enabled": true, "__prefab": {"__id__": 519}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "278eXdM6dLK4RKyoy2p/uE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cHDL6H3BELpg5fgCCAsS9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 480}, "_children": [], "_active": false, "_components": [{"__id__": 522}, {"__id__": 524}, {"__id__": 526}], "_prefab": {"__id__": 528}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 523}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4RWpD2RdGS7lziIZrLoKV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 525}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01HrY5JF5E74XNDUnL9G4J"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 521}, "_enabled": true, "__prefab": {"__id__": 527}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "319RSyzVNDVoiTZx69hKtr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56Ba/WOvpHrqppvNGisLDa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 480}, "_enabled": true, "__prefab": {"__id__": 530}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 481}, "checkEvents": [{"__id__": 531}], "_isChecked": false, "_checkMark": {"__id__": 506}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aFpQizXdHSLlXEmNPgHlJ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 480}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 480}, "_enabled": true, "__prefab": {"__id__": 533}, "Name": null, "BetPoint": {"__id__": 490}, "BetPointActive": {"__id__": 499}, "BetPointActiveWithPrice": {"__id__": 522}, "PriceLabel": {"__id__": 514}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64IsNADyJHF7vD6sXy1d+2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 480}, "_enabled": true, "__prefab": {"__id__": 535}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05kD4WijtMh5/ytMDw6CfW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 480}, "_enabled": true, "__prefab": {"__id__": 537}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36bq32E4lNIaRckbWboAJr"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 480}, "_enabled": true, "__prefab": {"__id__": 539}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dIYgDiLRE9otfW3hySrtH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23PwOp5rtCzbBWyiErd87l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": false, "__prefab": {"__id__": 542}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0W3IMAVVJL7PP2hzZV4fN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 544}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 10, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20+5nldvRDPqsMmnsb+Gdd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 546}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20zGf3Tv9I3as35rgi2BtU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 357}, "_enabled": true, "__prefab": {"__id__": 548}, "_contentSize": {"__type__": "cc.Size", "width": 324, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbw5pRMG9PYrTP86z7tMXW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2etTsTuL1E/45ZgU6kHktK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Card", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 336}, "_children": [{"__id__": 551}, {"__id__": 567}], "_active": true, "_components": [{"__id__": 583}, {"__id__": 585}, {"__id__": 587}], "_prefab": {"__id__": 589}, "_lpos": {"__type__": "cc.Vec3", "x": -100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "YellowCard", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 550}, "_children": [{"__id__": 552}], "_active": true, "_components": [{"__id__": 560}, {"__id__": 562}, {"__id__": 564}], "_prefab": {"__id__": 566}, "_lpos": {"__type__": "cc.Vec3", "x": 16, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "YellowCardCount1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 551}, "_children": [], "_active": true, "_components": [{"__id__": 553}, {"__id__": 555}, {"__id__": 557}], "_prefab": {"__id__": 559}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 554}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 28, "g": 29, "b": 33, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beoOHLsgBJQqLWcq72JpOC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 556}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bzjGQtOVH8YywkmxMiUmT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 558}, "_contentSize": {"__type__": "cc.Size", "width": 15.572265625, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bndq0PYdB4KU7WNHJ1Qxk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10TzWO+VNAYZbe+pWBMuYZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": {"__id__": 561}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 172, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7rUEDoXtFOqZ+qYb7H/DP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": {"__id__": 563}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b85dY24WNIIL5q8Qs53EuB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": {"__id__": 565}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8auT6VwG1Ec423ON87pj2e"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eehMKZb4xPoaoB8idoG8aA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "RedCard", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 550}, "_children": [{"__id__": 568}], "_active": true, "_components": [{"__id__": 576}, {"__id__": 578}, {"__id__": 580}], "_prefab": {"__id__": 582}, "_lpos": {"__type__": "cc.Vec3", "x": 58, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RedCardCount1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 567}, "_children": [], "_active": true, "_components": [{"__id__": 569}, {"__id__": 571}, {"__id__": 573}], "_prefab": {"__id__": 575}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 568}, "_enabled": true, "__prefab": {"__id__": 570}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 28, "g": 29, "b": 33, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5boJ8dN5H6IvpjUeQq3Yk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 568}, "_enabled": true, "__prefab": {"__id__": 572}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcfk+p3ApEJIZTD+Y3NWJj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 568}, "_enabled": true, "__prefab": {"__id__": 574}, "_contentSize": {"__type__": "cc.Size", "width": 15.572265625, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2kxKVZQ1ANruh60LRCW/j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89NPKuVcVPLKgRucm3K/Xj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 577}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 0, "b": 85, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccNYJnLBNHlI8yX+uUJ88U"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 579}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc83JtwHhLI7hEq55ueLuN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 567}, "_enabled": true, "__prefab": {"__id__": 581}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55Qgfs5pRFEJAdC4GB+gx0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3S1WQULxOcZRihGjbJo4a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 584}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52PbtPyChEopLJXsOO1K0U"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 586}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fEjg0VkZAIorMjST0BNMo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 588}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cQqXOfehOVY3VxIFjn1fk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edUeP9piZLK42i5WiKKHxX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 591}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02e/fMinJN7bgJcuTDfxGU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 593}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8bQwTjxpKMpfp6lUO3IDJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 336}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87EbHlgydDwbAVYOJ3X5wY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8204mE1G5Jgr1/cyseuWlh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Team2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 598}, {"__id__": 608}, {"__id__": 618}, {"__id__": 811}], "_active": true, "_components": [{"__id__": 851}, {"__id__": 853}, {"__id__": 855}], "_prefab": {"__id__": 857}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -235, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 597}, "_children": [], "_active": true, "_components": [{"__id__": 599}, {"__id__": 601}, {"__id__": 603}, {"__id__": 605}], "_prefab": {"__id__": 607}, "_lpos": {"__type__": "cc.Vec3", "x": -920, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 600}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "FC Sion", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feW4SIoaVA5L5RNQaJIMvB"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 602}, "_alignFlags": 8, "_target": null, "_left": 40, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02XX2DYLFDv7goWmrxGcwV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 604}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8yMcYlpBOYrK/hgONz6Jv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 598}, "_enabled": true, "__prefab": {"__id__": 606}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5NAYL/G1EHaVaEJ4CbLrM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f95bHlIzNHyKoBQwElb9Vr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Score", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 597}, "_children": [], "_active": true, "_components": [{"__id__": 609}, {"__id__": 611}, {"__id__": 613}, {"__id__": 615}], "_prefab": {"__id__": 617}, "_lpos": {"__type__": "cc.Vec3", "x": -387.62199999999996, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 610}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50rsjNHn1Cro3cOJYhGjVY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 612}, "_alignFlags": 8, "_target": null, "_left": 522.378, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0e+VzqAhVHNZcNLTvXKbTv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 614}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "366/HKEhlEVbbRaxCvBJBH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 608}, "_enabled": true, "__prefab": {"__id__": 616}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "608yzD1xFKgLZxhNTUQrs/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07vnG3ykFEAK6uQrU2K6NT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "LayoutBet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 597}, "_children": [{"__id__": 619}, {"__id__": 680}, {"__id__": 741}], "_active": true, "_components": [{"__id__": 802}, {"__id__": 804}, {"__id__": 806}, {"__id__": 808}], "_prefab": {"__id__": 810}, "_lpos": {"__type__": "cc.Vec3", "x": 626, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 618}, "_children": [{"__id__": 620}, {"__id__": 628}, {"__id__": 636}, {"__id__": 652}, {"__id__": 660}], "_active": true, "_components": [{"__id__": 668}, {"__id__": 671}, {"__id__": 673}, {"__id__": 675}, {"__id__": 677}], "_prefab": {"__id__": 679}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 619}, "_children": [], "_active": true, "_components": [{"__id__": 621}, {"__id__": 623}, {"__id__": 625}], "_prefab": {"__id__": 627}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 620}, "_enabled": true, "__prefab": {"__id__": 622}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e85JDaY81OaYPGHJR8zDPw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 620}, "_enabled": true, "__prefab": {"__id__": 624}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffWQ1yPNBGOaLMo3//6bX2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 620}, "_enabled": true, "__prefab": {"__id__": 626}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adZrSBaYlDn44LQob+eGlu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7a+pF1G7tGHa6HEV5GcMix", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 619}, "_children": [], "_active": true, "_components": [{"__id__": 629}, {"__id__": 631}, {"__id__": 633}], "_prefab": {"__id__": 635}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 628}, "_enabled": true, "__prefab": {"__id__": 630}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fSM4LI/dDF4PHYAFrl4BQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 628}, "_enabled": true, "__prefab": {"__id__": 632}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1el5bd/DdJUqnLqVtRV05n"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 628}, "_enabled": true, "__prefab": {"__id__": 634}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbd/AauDtLtpaSYkj/hoTY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "19+Nc0oOJJdrPKWvtsWE/n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 619}, "_children": [{"__id__": 637}], "_active": false, "_components": [{"__id__": 645}, {"__id__": 647}, {"__id__": 649}], "_prefab": {"__id__": 651}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 636}, "_children": [], "_active": true, "_components": [{"__id__": 638}, {"__id__": 640}, {"__id__": 642}], "_prefab": {"__id__": 644}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 639}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6j/mqRxBCOJ0z65zx3cBS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 641}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8572RdjdRHs6DUkHuLfWDj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 643}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96aluQXMZANI4LTKiJZ6HI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45W8/5n1pM95JbAJ/tJ4uX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 636}, "_enabled": true, "__prefab": {"__id__": 646}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27t8QegXZCWbWvwQIx4qko"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 636}, "_enabled": true, "__prefab": {"__id__": 648}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bYbJPVF5Mx6X28K8k/EPJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 636}, "_enabled": true, "__prefab": {"__id__": 650}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bsAY67dNNKLDZrEpZvFjJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7eLqSAvVFap51mpE4L1S5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 619}, "_children": [], "_active": false, "_components": [{"__id__": 653}, {"__id__": 655}, {"__id__": 657}], "_prefab": {"__id__": 659}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 652}, "_enabled": true, "__prefab": {"__id__": 654}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeOhgc+hFELq+/JwHrC5mn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 652}, "_enabled": true, "__prefab": {"__id__": 656}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "farz1sq2xHSpbUVwsnUpAG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 652}, "_enabled": true, "__prefab": {"__id__": 658}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1Favoj7ZJp52IKMJ+OmBV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53kB4sQz1FCooL0l/32DvO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 619}, "_children": [], "_active": false, "_components": [{"__id__": 661}, {"__id__": 663}, {"__id__": 665}], "_prefab": {"__id__": 667}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 660}, "_enabled": true, "__prefab": {"__id__": 662}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fL+EcY9lJELU7EU4J635w"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 660}, "_enabled": true, "__prefab": {"__id__": 664}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f074TLrrVLZbQzjScd74d+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 660}, "_enabled": true, "__prefab": {"__id__": 666}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42UcujKGBJlLKy3N3WtYUI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16E1ZD5ApGFKxwmmP6uZLk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 669}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 620}, "checkEvents": [{"__id__": 670}], "_isChecked": false, "_checkMark": {"__id__": 645}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90IFL+WDZI2qSdZe8V0dON"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 619}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 672}, "Name": null, "BetPoint": {"__id__": 629}, "BetPointActive": {"__id__": 638}, "BetPointActiveWithPrice": {"__id__": 661}, "PriceLabel": {"__id__": 653}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbYiiQKEhF6o1Z2MxcHnFN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 674}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58Zqnbdl1F25JRcySJAjP7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 676}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73e9u4zXRKh4FAjgZVpUW7"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 619}, "_enabled": true, "__prefab": {"__id__": 678}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d11kW9XkdNxKNcJ2jTBWTu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86/o4ttV5HIIrF0l3QU07M", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 618}, "_children": [{"__id__": 681}, {"__id__": 689}, {"__id__": 697}, {"__id__": 713}, {"__id__": 721}], "_active": true, "_components": [{"__id__": 729}, {"__id__": 732}, {"__id__": 734}, {"__id__": 736}, {"__id__": 738}], "_prefab": {"__id__": 740}, "_lpos": {"__type__": "cc.Vec3", "x": 157, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": true, "_components": [{"__id__": 682}, {"__id__": 684}, {"__id__": 686}], "_prefab": {"__id__": 688}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 681}, "_enabled": true, "__prefab": {"__id__": 683}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60DJH9W85OtpEXjCSI6wQS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 681}, "_enabled": true, "__prefab": {"__id__": 685}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47nTW/I0VIgrR2yAs8OgCP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 681}, "_enabled": true, "__prefab": {"__id__": 687}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f1dMCMiRE0LdFmd+Szbeu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3UBe2dGhIBY1JXBRgd6+K", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": true, "_components": [{"__id__": 690}, {"__id__": 692}, {"__id__": 694}], "_prefab": {"__id__": 696}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 689}, "_enabled": true, "__prefab": {"__id__": 691}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "786FWPIDNBmrwSRzloyfmx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 689}, "_enabled": true, "__prefab": {"__id__": 693}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86Y2obdDpHuYee6yNtxMbI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 689}, "_enabled": true, "__prefab": {"__id__": 695}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76/7PLc0lC449FReedTs0c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2vFum4YJMHI3qVN5rGajr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [{"__id__": 698}], "_active": false, "_components": [{"__id__": 706}, {"__id__": 708}, {"__id__": 710}], "_prefab": {"__id__": 712}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 697}, "_children": [], "_active": true, "_components": [{"__id__": 699}, {"__id__": 701}, {"__id__": 703}], "_prefab": {"__id__": 705}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 700}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74kykmWLJD64w9fyjenLYp"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 702}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2XKjjINJFd42WXlAiZOvI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 698}, "_enabled": true, "__prefab": {"__id__": 704}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dYoRZqmBJSrKNXJ5SHZJV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfh/52ic5DEYvVsJhcpzSw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 707}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50ajO0KV1OuIfDcYpAwNha"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 709}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "13exaUAOFPjp/Tpb53GL2O"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 697}, "_enabled": true, "__prefab": {"__id__": 711}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17SDoOk9BPCYCMZ/Cu9cva"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfCJzXMS5Byokf7H+oUUX+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": false, "_components": [{"__id__": 714}, {"__id__": 716}, {"__id__": 718}], "_prefab": {"__id__": 720}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 713}, "_enabled": true, "__prefab": {"__id__": 715}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1NpmZTphDV5RTvTGPt8Sa"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 713}, "_enabled": true, "__prefab": {"__id__": 717}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "146VQeIyJP5Jn3Xj4PUSbA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 713}, "_enabled": true, "__prefab": {"__id__": 719}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86xkzJMmJKnJYYMTnYQIcl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ee4Yn2bVNMRoapcxcpQjM1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": false, "_components": [{"__id__": 722}, {"__id__": 724}, {"__id__": 726}], "_prefab": {"__id__": 728}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 723}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43cI0AH6NBTJretnJOrx77"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 725}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3LAoCr/tII7jeATWv3ryS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 721}, "_enabled": true, "__prefab": {"__id__": 727}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7de93ce4VBxJJgdjBcMYji"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9jZucxJdJfLofEmEQxZHM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 730}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 681}, "checkEvents": [{"__id__": 731}], "_isChecked": false, "_checkMark": {"__id__": 706}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74/4cyEfpJ4q86kZkwhvz+"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 680}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 733}, "Name": null, "BetPoint": {"__id__": 690}, "BetPointActive": {"__id__": 699}, "BetPointActiveWithPrice": {"__id__": 722}, "PriceLabel": {"__id__": 714}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aAesQxCtJ+Kt6UuRiiiQi"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 735}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b73BXU0dhNCZVlnSJhKppX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 737}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76sNlsRGFO2pGJ+Clw+Lki"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 739}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2erPR79c9McaValHqDkY8X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3AbdBAz5P+bsN2Qo1LJlS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 618}, "_children": [{"__id__": 742}, {"__id__": 750}, {"__id__": 758}, {"__id__": 774}, {"__id__": 782}], "_active": true, "_components": [{"__id__": 790}, {"__id__": 793}, {"__id__": 795}, {"__id__": 797}, {"__id__": 799}], "_prefab": {"__id__": 801}, "_lpos": {"__type__": "cc.Vec3", "x": 270, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [], "_active": true, "_components": [{"__id__": 743}, {"__id__": 745}, {"__id__": 747}], "_prefab": {"__id__": 749}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 744}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45+vJug7dI87PTZ9oIfWD+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 746}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "399sPbBE5NDrSMeDQ0SakN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 742}, "_enabled": true, "__prefab": {"__id__": 748}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69RSROSopBXJJQeQrFutTE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0Fz4gbAhC0a0kwfIhrlnT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [], "_active": true, "_components": [{"__id__": 751}, {"__id__": 753}, {"__id__": 755}], "_prefab": {"__id__": 757}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 752}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70vaCCn2ZPipS3rI2S6Kd8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 754}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "495wtnSNFNo64L2Pzmkh4l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 756}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dPtCb4g1F5JD9RlcfEM9W"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7a/fnH3otHHKcrd/Rg6UZA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [{"__id__": 759}], "_active": false, "_components": [{"__id__": 767}, {"__id__": 769}, {"__id__": 771}], "_prefab": {"__id__": 773}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 758}, "_children": [], "_active": true, "_components": [{"__id__": 760}, {"__id__": 762}, {"__id__": 764}], "_prefab": {"__id__": 766}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 759}, "_enabled": true, "__prefab": {"__id__": 761}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9Wm5ydkNK0o/c4TYCK/1h"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 759}, "_enabled": true, "__prefab": {"__id__": 763}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1jO6MS2FIoq2H4rW9S6ni"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 759}, "_enabled": true, "__prefab": {"__id__": 765}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7B+TtPFdFcJtWWxlM/Tjt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "faZ0/qdOZG55HezPm42zpV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 768}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "063gGYtjhGdrT4pPHgWvvu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 770}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95YcIIzrxDNqeZzJIy+g9Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 758}, "_enabled": true, "__prefab": {"__id__": 772}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7IdQMtlRE8qYeOdoPRFOP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "12Jo/YZMdG6ZyZfEvwDjCd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [], "_active": false, "_components": [{"__id__": 775}, {"__id__": 777}, {"__id__": 779}], "_prefab": {"__id__": 781}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 774}, "_enabled": true, "__prefab": {"__id__": 776}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4FMDc4olEM61gOWjP+Jw8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 774}, "_enabled": true, "__prefab": {"__id__": 778}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdlz7XQuRKDbk2/Ox124Hx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 774}, "_enabled": true, "__prefab": {"__id__": 780}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b1SbPJ3pHspW803G36qta"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eadIA+x+xLZJScj9I/Zw3F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 741}, "_children": [], "_active": false, "_components": [{"__id__": 783}, {"__id__": 785}, {"__id__": 787}], "_prefab": {"__id__": 789}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 784}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fkvDOehhKCpjjI5QYYskW"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 786}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5KUe3d35HI4R2X6XMWOSx"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 782}, "_enabled": true, "__prefab": {"__id__": 788}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5a6pQ02GVAqrRmuiYAoBWd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58I1Tf/BtKmbhcRrpdwBXK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 791}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 742}, "checkEvents": [{"__id__": 792}], "_isChecked": false, "_checkMark": {"__id__": 767}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46lF6pt/hDW7OLEcVAwmF6"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 741}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 794}, "Name": null, "BetPoint": {"__id__": 751}, "BetPointActive": {"__id__": 760}, "BetPointActiveWithPrice": {"__id__": 783}, "PriceLabel": {"__id__": 775}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bRjnMhRVEV48aJSXjxZYD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 796}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6d0NCYBWtDKq1TBYqOrU+U"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 798}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "541MkVtypC0IYR5vqjEieg"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 741}, "_enabled": true, "__prefab": {"__id__": 800}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3poEtIXBME4stGuhGXYzY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9ciR+SMbxKp42GmNm83H8l", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": false, "__prefab": {"__id__": 803}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54KaA9xcFHL6LDUEOp7Tgu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": true, "__prefab": {"__id__": 805}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 10, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2M/W8Hk5GB63tSMma23qJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": true, "__prefab": {"__id__": 807}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bfdLO+c1I75Hmh+mW7vc+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": true, "__prefab": {"__id__": 809}, "_contentSize": {"__type__": "cc.Size", "width": 324, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eXUepfHVFHIAzcdzJy6Or"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fD4aHX5NCFYZXOZSTnEtu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Card", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 597}, "_children": [{"__id__": 812}, {"__id__": 828}], "_active": true, "_components": [{"__id__": 844}, {"__id__": 846}, {"__id__": 848}], "_prefab": {"__id__": 850}, "_lpos": {"__type__": "cc.Vec3", "x": -100, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "YellowCard", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [{"__id__": 813}], "_active": true, "_components": [{"__id__": 821}, {"__id__": 823}, {"__id__": 825}], "_prefab": {"__id__": 827}, "_lpos": {"__type__": "cc.Vec3", "x": 16, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "YellowCardCount2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 812}, "_children": [], "_active": true, "_components": [{"__id__": 814}, {"__id__": 816}, {"__id__": 818}], "_prefab": {"__id__": 820}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 815}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 28, "g": 29, "b": 33, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26kV4c9OdK86vw/nUsdxF2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 817}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fiEZA0KlE67S9cfn6SWPT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 813}, "_enabled": true, "__prefab": {"__id__": 819}, "_contentSize": {"__type__": "cc.Size", "width": 15.572265625, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dj58Yy7pOUq8GPK0Nrr0u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94gBFCztNKsrKoKErpM2SQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 812}, "_enabled": true, "__prefab": {"__id__": 822}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 172, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deZVZUjkFHVIi3KIFtaybZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 812}, "_enabled": true, "__prefab": {"__id__": 824}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8Pzk8hxdMjJY5emB0Ej2p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 812}, "_enabled": true, "__prefab": {"__id__": 826}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7bsxtzTVN3aW3/Xr1BVbt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1erfX4dSZHkauLkMl3lmOl", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "RedCard", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 811}, "_children": [{"__id__": 829}], "_active": true, "_components": [{"__id__": 837}, {"__id__": 839}, {"__id__": 841}], "_prefab": {"__id__": 843}, "_lpos": {"__type__": "cc.Vec3", "x": 58, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "RedCardCount2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 828}, "_children": [], "_active": true, "_components": [{"__id__": 830}, {"__id__": 832}, {"__id__": 834}], "_prefab": {"__id__": 836}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 831}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 28, "g": 29, "b": 33, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcvb36OFVPKLN24M+xAG/x"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 833}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "139gpe4uNO5beoKY9z9sFb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 835}, "_contentSize": {"__type__": "cc.Size", "width": 15.572265625, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e70KGA3x1M/65zTAd7THSa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2xeZD/YVNtZU/d31Jj4pG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 838}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 235, "g": 0, "b": 85, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "633ac390hFPYoR2uSXzPVC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 840}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f83wugNLpI2qO/7DcvWXln"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 842}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5SWZw1QJDR5Yq2xkiqTQS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44Aad6O5pPAYJ1FMpi6/JG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 845}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21S21Jn09EzZ0Lnku+GLy6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 847}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6diUl39ZdNNaGkXoNoYaR1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 849}, "_contentSize": {"__type__": "cc.Size", "width": 74, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91dxqt2ilKX6czVPJh/GWA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f3tPeJxIxKf5N0+3NOf10Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 852}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f8IM0zlhBdow+/VB5bZMz"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 854}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcj9VWwoRLv4/k9hdmXomQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 597}, "_enabled": true, "__prefab": {"__id__": 856}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6bTruS3VFqqVzfMzhm3QN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4aWP5rXQtJM4x5djUPCTX4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Draw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 859}, {"__id__": 871}], "_active": true, "_components": [{"__id__": 942}, {"__id__": 944}, {"__id__": 946}], "_prefab": {"__id__": 948}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -335, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Name", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 858}, "_children": [], "_active": true, "_components": [{"__id__": 860}, {"__id__": 862}, {"__id__": 864}, {"__id__": 866}, {"__id__": 868}], "_prefab": {"__id__": 870}, "_lpos": {"__type__": "cc.Vec3", "x": -920, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 861}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Hòa", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8WR76OFJBHpqi4BrvPQPP"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 863}, "id": "txt_draw", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6AYNHAVVPW5rxkRmhSzn1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 865}, "_alignFlags": 8, "_target": null, "_left": 40, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efGcGiX2hM/r/wlX9qOruC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 867}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aakakwFCpDl7dfGY6sQBHK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 859}, "_enabled": true, "__prefab": {"__id__": 869}, "_contentSize": {"__type__": "cc.Size", "width": 350, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6QfJkeWBPsatLzkPXaeJK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9aNCtSrBBGe49jBb6Xa05W", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "LayoutBet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 858}, "_children": [{"__id__": 872}], "_active": true, "_components": [{"__id__": 933}, {"__id__": 935}, {"__id__": 937}, {"__id__": 939}], "_prefab": {"__id__": 941}, "_lpos": {"__type__": "cc.Vec3", "x": 626, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetToggle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 871}, "_children": [{"__id__": 873}, {"__id__": 881}, {"__id__": 889}, {"__id__": 905}, {"__id__": 913}], "_active": true, "_components": [{"__id__": 921}, {"__id__": 924}, {"__id__": 926}, {"__id__": 928}, {"__id__": 930}], "_prefab": {"__id__": 932}, "_lpos": {"__type__": "cc.Vec3", "x": 44, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 872}, "_children": [], "_active": true, "_components": [{"__id__": 874}, {"__id__": 876}, {"__id__": 878}], "_prefab": {"__id__": 880}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 873}, "_enabled": true, "__prefab": {"__id__": 875}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "70GxnoATVCfaUofmjkYndV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 873}, "_enabled": true, "__prefab": {"__id__": 877}, "_opacity": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5dow9FZVAqoFbNJHv1XKl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 873}, "_enabled": true, "__prefab": {"__id__": 879}, "_contentSize": {"__type__": "cc.Size", "width": 97, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3gHvXI3pEZ4w1Dl3B63GC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6+X+tFMdJ6rhX/G/S2Enf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 872}, "_children": [], "_active": true, "_components": [{"__id__": 882}, {"__id__": 884}, {"__id__": 886}], "_prefab": {"__id__": 888}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 881}, "_enabled": true, "__prefab": {"__id__": 883}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0rq63MEtP+KPM3IlFNSPC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 881}, "_enabled": true, "__prefab": {"__id__": 885}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20IZoohmhKcLEAKVKc1hJF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 881}, "_enabled": true, "__prefab": {"__id__": 887}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68wh9V64NMcKosK8sqijeU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fdcdKsxalPOpgl7epjILaC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "__editorExtras__": {}, "_parent": {"__id__": 872}, "_children": [{"__id__": 890}], "_active": false, "_components": [{"__id__": 898}, {"__id__": 900}, {"__id__": 902}], "_prefab": {"__id__": 904}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 889}, "_children": [], "_active": true, "_components": [{"__id__": 891}, {"__id__": 893}, {"__id__": 895}], "_prefab": {"__id__": 897}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 892}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 22, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9czCVgKRKTJYYiUkqc0+3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 894}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cePxsBbERI+Y9xx/jTgkh8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 890}, "_enabled": true, "__prefab": {"__id__": 896}, "_contentSize": {"__type__": "cc.Size", "width": 57.470703125, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbPnsjJg9Dv7smJjF5MEvw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02wSg97/tEBLSYAlmbQ8c9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 899}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1d19d995-7367-426d-a0f5-58efbc986625@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afMzksrzRGU4Muepj2z/08"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 901}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0LIYEM19Gs5sm4dRJjN1U"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 889}, "_enabled": true, "__prefab": {"__id__": 903}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8992o5I+dPxodxuosmfA7G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3abg5WDBO37WwfAbyDaxi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GoldBonus", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 872}, "_children": [], "_active": false, "_components": [{"__id__": 906}, {"__id__": 908}, {"__id__": 910}], "_prefab": {"__id__": 912}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 905}, "_enabled": true, "__prefab": {"__id__": 907}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 213, "g": 156, "b": 0, "a": 255}, "_string": "1.75", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dejSy3nQBEbpNAkgDDMFCF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 905}, "_enabled": true, "__prefab": {"__id__": 909}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fAbRB5/FGno1gHw3K/GVF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 905}, "_enabled": true, "__prefab": {"__id__": 911}, "_contentSize": {"__type__": "cc.Size", "width": 42.82, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0tC70je1PRby8HBCksnuv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cf9HXbfCNAXp5cdm3wd3E5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BetPoint", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 872}, "_children": [], "_active": false, "_components": [{"__id__": 914}, {"__id__": 916}, {"__id__": 918}], "_prefab": {"__id__": 920}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 15, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 913}, "_enabled": true, "__prefab": {"__id__": 915}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "(1.80)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0bF6wWolLUpcLAlpvTOwC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 913}, "_enabled": true, "__prefab": {"__id__": 917}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f21CAlmxZCrZ3kT2XYDuzv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 913}, "_enabled": true, "__prefab": {"__id__": 919}, "_contentSize": {"__type__": "cc.Size", "width": 57.47, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbc16gkKpKWoGshexl3hDs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d692T88+xDBpzc2xZvXICr", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 872}, "_enabled": true, "__prefab": {"__id__": 922}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 873}, "checkEvents": [{"__id__": 923}], "_isChecked": false, "_checkMark": {"__id__": 898}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccN2IiXGFOzK0dA2rmJ6bv"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 872}, "component": "", "_componentId": "192f3bETexLtZrFjt3ZK/YL", "handler": "onClick", "customEventData": ""}, {"__type__": "192f3bETexLtZrFjt3ZK/YL", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 872}, "_enabled": true, "__prefab": {"__id__": 925}, "Name": null, "BetPoint": {"__id__": 882}, "BetPointActive": {"__id__": 891}, "BetPointActiveWithPrice": {"__id__": 914}, "PriceLabel": {"__id__": 906}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5JSC9xltO77qVDk+2Hzrc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 872}, "_enabled": true, "__prefab": {"__id__": 927}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bXWyWT01FW7xrNCXZ0A2c"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 872}, "_enabled": true, "__prefab": {"__id__": 929}, "_contentSize": {"__type__": "cc.Size", "width": 88, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dxFhys2JLDJa0qF1g3GMA"}, {"__type__": "cc.PrefabLink", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 872}, "_enabled": true, "__prefab": {"__id__": 931}, "prefab": {"__uuid__": "ecf962d6-ff7f-4a61-b825-6546522f8b43", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4awQbl/KJMVqBQIXdOB/MM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ab9JAd9+dOXITKGUle4+vZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 871}, "_enabled": false, "__prefab": {"__id__": 934}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23SvY8TL5IPqnirN+bVyf9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 871}, "_enabled": true, "__prefab": {"__id__": 936}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 236, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1V0qshjNDIJzlWHpikflm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 871}, "_enabled": true, "__prefab": {"__id__": 938}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0Q0zsxRJBfY4MwodCrxWa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 871}, "_enabled": true, "__prefab": {"__id__": 940}, "_contentSize": {"__type__": "cc.Size", "width": 98, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52Qr9wtYVLdKkAe6uaDboT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eeqPU+DKdJWbXpOQc9iJQw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 943}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eefuje+RhOgpTPDb5QNt9j"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 945}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bRIx5pTFBuJMwXGEC9WHT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 947}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aYhKsVL5LXagjORRzKxxx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23KECoZJhMYaPLlUUeB9Gq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Other", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [{"__id__": 950}, {"__id__": 969}, {"__id__": 977}, {"__id__": 996}, {"__id__": 1015}], "_active": true, "_components": [{"__id__": 1025}, {"__id__": 1027}, {"__id__": 1029}], "_prefab": {"__id__": 1031}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -422, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 949}, "_children": [{"__id__": 951}], "_active": true, "_components": [{"__id__": 959}, {"__id__": 961}, {"__id__": 964}, {"__id__": 966}], "_prefab": {"__id__": 968}, "_lpos": {"__type__": "cc.Vec3", "x": 425, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 950}, "_children": [], "_active": true, "_components": [{"__id__": 952}, {"__id__": 954}, {"__id__": 956}], "_prefab": {"__id__": 958}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 951}, "_enabled": true, "__prefab": {"__id__": 953}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eavAvR/G1DnYimkXiGFW2k"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 951}, "_enabled": true, "__prefab": {"__id__": 955}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9ZcEe36RAjqOamjPLyplC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 951}, "_enabled": true, "__prefab": {"__id__": 957}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83TuHv7OVPqLA/a0YmVNBq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecwiNommhJ75xGS7vLbtTP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 950}, "_enabled": true, "__prefab": {"__id__": 960}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dzxBoWVpPD6qqtcHvH1Uh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 950}, "_enabled": true, "__prefab": {"__id__": 962}, "clickEvents": [{"__id__": 963}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dRrABf8ZArK+lPfe6dXqv"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "activeQuickView", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 950}, "_enabled": true, "__prefab": {"__id__": 965}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3jTW1RlpPHqNJHc989JMp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 950}, "_enabled": true, "__prefab": {"__id__": 967}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3AY58o59CbYg8OzeFcz7V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6etHuvNjtLG44JTpnfom8H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "OtherBets", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 949}, "_children": [], "_active": true, "_components": [{"__id__": 970}, {"__id__": 972}, {"__id__": 974}], "_prefab": {"__id__": 976}, "_lpos": {"__type__": "cc.Vec3", "x": 13.557, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 969}, "_enabled": true, "__prefab": {"__id__": 971}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 155, "g": 163, "b": 180, "a": 255}, "_string": "179+", "_horizontalAlign": 2, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b8wz4MJpBFYp5x2EJ6wkn"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 969}, "_enabled": true, "__prefab": {"__id__": 973}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f73Kbf1gZHwp+GGEL8uXKY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 969}, "_enabled": true, "__prefab": {"__id__": 975}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdqgRwRqZIV5La/dJr/thG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9jB5IyjVHFYIhOTFuGM6U", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 949}, "_children": [{"__id__": 978}], "_active": true, "_components": [{"__id__": 986}, {"__id__": 988}, {"__id__": 991}, {"__id__": 993}], "_prefab": {"__id__": 995}, "_lpos": {"__type__": "cc.Vec3", "x": 92.583, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 977}, "_children": [], "_active": true, "_components": [{"__id__": 979}, {"__id__": 981}, {"__id__": 983}], "_prefab": {"__id__": 985}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0.7071067811865475, "w": 0.7071067811865476}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 90}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 978}, "_enabled": true, "__prefab": {"__id__": 980}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dvFbjyBpNorP1WqNflO7M"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 978}, "_enabled": true, "__prefab": {"__id__": 982}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9GTJEr5pBdLPJaLYYKLJU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 978}, "_enabled": true, "__prefab": {"__id__": 984}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35JRBpCxlFzJGZtNKkv8lK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63teJIyOlCXIgqQDf+iRN3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 977}, "_enabled": true, "__prefab": {"__id__": 987}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64zd9nJANJAaos7I0583hL"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 977}, "_enabled": true, "__prefab": {"__id__": 989}, "clickEvents": [{"__id__": 990}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0urPxbDRC07mD967gxV1c"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "openLiveMatch", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 977}, "_enabled": true, "__prefab": {"__id__": 992}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cffxMU6iJCHJc2ljif1c6M"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 977}, "_enabled": true, "__prefab": {"__id__": 994}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1897I4IhdAO5nHWtmHlks2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34VUVIOKJPMqB/5pLPwTeU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "moreBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 949}, "_children": [{"__id__": 997}], "_active": true, "_components": [{"__id__": 1005}, {"__id__": 1007}, {"__id__": 1010}, {"__id__": 1012}], "_prefab": {"__id__": 1014}, "_lpos": {"__type__": "cc.Vec3", "x": -155, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "arrow-down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 996}, "_children": [], "_active": true, "_components": [{"__id__": 998}, {"__id__": 1000}, {"__id__": 1002}], "_prefab": {"__id__": 1004}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 997}, "_enabled": true, "__prefab": {"__id__": 999}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9a9c63e9-1398-4386-88c7-3803da77397e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2ygUYS8lIUKYQPaMWB0XM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 997}, "_enabled": true, "__prefab": {"__id__": 1001}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57YZQ20MRLW6sdGkrcoicu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 997}, "_enabled": true, "__prefab": {"__id__": 1003}, "_contentSize": {"__type__": "cc.Size", "width": 19, "height": 11}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09SiFz/ytNna6YOGCzZvxL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4ee8dVJS1Di5ajbEgkeKDo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 996}, "_enabled": true, "__prefab": {"__id__": 1006}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6ba69185-b8a8-4ef4-ba02-1ae89177c86e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b756YxJENKWLlBcBLHBwn1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 996}, "_enabled": true, "__prefab": {"__id__": 1008}, "clickEvents": [{"__id__": 1009}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2BBY5c1hLD6rvyRnNUMIS"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "a30b9XGSU9Hfo5jE1jiszwg", "handler": "openPreMatch", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 996}, "_enabled": true, "__prefab": {"__id__": 1011}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1IkBYc/9OLo0i0Qn2k5Lt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 996}, "_enabled": true, "__prefab": {"__id__": 1013}, "_contentSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dkN7By2ZIi517jQQPX0qE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fcDh/n4VtEfpMrTSfCI4op", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "OtherBets", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 949}, "_children": [], "_active": true, "_components": [{"__id__": 1016}, {"__id__": 1018}, {"__id__": 1020}, {"__id__": 1022}], "_prefab": {"__id__": 1024}, "_lpos": {"__type__": "cc.Vec3", "x": -315, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1015}, "_enabled": true, "__prefab": {"__id__": 1017}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 155, "g": 163, "b": 180, "a": 255}, "_string": "<PERSON><PERSON> c<PERSON> Châu <PERSON>c", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8obCpRw9BGIcPdENkhbo6"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1015}, "_enabled": true, "__prefab": {"__id__": 1019}, "id": "spr124", "isUpperCase": false, "useCustomFont": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2e7n8Pb1hB9aqJJT2JRXBw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1015}, "_enabled": true, "__prefab": {"__id__": 1021}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83cQguHTFNkL888Jceq0Ik"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1015}, "_enabled": true, "__prefab": {"__id__": 1023}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45vyE47GtETKrzGslumtHl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "15Fivg37dGxrhl32En+T8X", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 1026}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eag4z++9pD0LKzqTO3MRXF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 1028}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24KemdBpBB64tIlPWwgKyP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 949}, "_enabled": true, "__prefab": {"__id__": 1030}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 74}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdpUJ5VodAOZkSpVPAODr1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbWICWnL9CGZsnvTiN4GgR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 1033}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbigvcL3VLupTTmJs+bRIN"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 1035}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33a5MoXxBBMJlUd+SdRUx+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 1037}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12wpGSNTpP6LU6cmqo2NJs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 1039}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 459}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8e3zSFPPRDqL4bnIEWeVf/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eb0Ke3wxtGt6s6Q1SdYLL5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "AsianBet", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": false, "_components": [{"__id__": 1042}, {"__id__": 1044}, {"__id__": 1046}, {"__id__": 1048}], "_prefab": {"__id__": 1050}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -322, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1041}, "_enabled": true, "__prefab": {"__id__": 1043}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "103rUqPblCILOycrIxInd1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1041}, "_enabled": true, "__prefab": {"__id__": 1045}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 644, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "451KJV1rdB1quFBPI5YPYl"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1041}, "_enabled": true, "__prefab": {"__id__": 1047}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caLKhHfmNNY4F42pfKUwN4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1041}, "_enabled": true, "__prefab": {"__id__": 1049}, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a97AAn6SxE5Lf+GAQXmugl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "59x477XeRAWK5Vdrhs9alu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1052}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93SKepFEJFFLohdVtE7HKy"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1054}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8vqJ6m2RKhYCl+qT7PzMU"}, {"__type__": "a30b9XGSU9Hfo5jE1jiszwg", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1056}, "quickView": {"__id__": 2}, "detailView": {"__id__": 137}, "layoutBet1": {"__id__": 357}, "layoutBet2": {"__id__": 618}, "layoutBet3": {"__id__": 871}, "TeamValue": {"__id__": 34}, "TeamName": {"__id__": 24}, "Team1Name": {"__id__": 338}, "Team2Name": {"__id__": 599}, "Team1Value": {"__id__": 348}, "Team2Value": {"__id__": 609}, "TimeLabel": {"__id__": 14}, "TimeDetailLabel": {"__id__": 161}, "Other": {"__id__": 970}, "OtherQuick": {"__id__": 102}, "AsianBetNode": {"__id__": 1041}, "AsianBetItem": {"__uuid__": "115d6e0d-bf02-4810-8c63-ff5475decdff", "__expectedType__": "cc.Prefab"}, "YellowCardTeam1": {"__id__": 551}, "RedCardTeam1": {"__id__": 567}, "YellowCardTeam2": {"__id__": 812}, "RedCardTeam2": {"__id__": 828}, "YellowCardTeam1Count": {"__id__": 553}, "YellowCardTeam2Count": {"__id__": 814}, "RedCardTeam1Count": {"__id__": 569}, "RedCardTeam2Count": {"__id__": 830}, "LiveButton": {"__id__": 233}, "StatButton": {"__id__": 244}, "addFavorite": {"__id__": 152}, "pageView": {"__id__": 319}, "TotalBetLabel": {"__id__": 189}, "FirstMatchBetLabel": {"__id__": 207}, "SeacondMatchBetLabel": {"__id__": 225}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cAG/1S9FAtqHragc2YUY9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1058}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0azapO25K7aKjPzyVf765"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1060}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 659}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0573MZi6NJcJ5qOfH+hvQV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfI/y7AHpG1q6neO+mHD0D", "instance": null, "targetOverrides": [{"__id__": 1062}, {"__id__": 1064}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 1055}, "sourceInfo": null, "propertyPath": ["YellowCardTeam2"], "target": {"__id__": 812}, "targetInfo": {"__id__": 1063}}, {"__type__": "cc.TargetInfo", "localID": ["cfMM9BlzhPZrHRKcTlOtmU"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 1055}, "sourceInfo": null, "propertyPath": ["YellowCardTeam2Count"], "target": {"__id__": 812}, "targetInfo": {"__id__": 1065}}, {"__type__": "cc.TargetInfo", "localID": ["aaEijG9C5Mu721Gw7abJFo"]}]