[{"__type__": "cc.Prefab", "_name": "BetPage", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "BetPage", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 1118}], "_active": true, "_components": [{"__id__": 1365}, {"__id__": 1367}, {"__id__": 1369}, {"__id__": 1371}, {"__id__": 1373}], "_prefab": {"__id__": 1375}, "_lpos": {"__type__": "cc.Vec3", "x": 960, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "576tGfpW1Ccav1vuE7jBGA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 128, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7/Z3JCIBA0629FPwrexNH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 100}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1/giqQTNKPaxjEJ9HKnhM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ccSedd4JC+oXFn+9Y178t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d45PwjWahK8am1n1Ptngdt"}, {"__type__": "cc.Node", "_name": "Main", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 23}, {"__id__": 33}, {"__id__": 94}, {"__id__": 115}, {"__id__": 136}, {"__id__": 157}, {"__id__": 337}, {"__id__": 474}, {"__id__": 504}, {"__id__": 676}, {"__id__": 779}, {"__id__": 911}, {"__id__": 937}, {"__id__": 960}], "_active": true, "_components": [{"__id__": 1109}, {"__id__": 1111}, {"__id__": 1113}, {"__id__": 1115}], "_prefab": {"__id__": 1117}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 257, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 22}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 38, "g": 39, "b": 43, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aeoXUPk5Ej7C6CY1IOnxr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 250, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 128, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22vfTAD1xFq4xq1JlsKo4i"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "038RTu3s1A7qTd5TZx5IAe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 21}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 1344}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2Of6qm/RNn7ZUwFTcYmrc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "78DRx7xbxByIhqaFCQ9bOC"}, {"__type__": "cc.Node", "_name": "Line", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 642.069, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 25}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06MTB1PmVPrICi6qCXIDtm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 27}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 154.43100000000004, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25uEryHxxLTJRouhyf/lP4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 29}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "03Aih1bYJEmJ4pPEthiu7K"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "__prefab": {"__id__": 31}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1+6QC/81PmImIDGniYfAe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2fgFHpC/9DHp6CBveDM8Gj"}, {"__type__": "cc.Node", "_name": "LabelFreeTicket", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 34}, {"__id__": 50}, {"__id__": 58}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 89}, {"__id__": 91}], "_prefab": {"__id__": 93}, "_lpos": {"__type__": "cc.Vec3", "x": -724.615, "y": 701.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "badge", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 35}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 45}, {"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": 111.239, "y": 19.295, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "notice<PERSON>abel", "_objFlags": 0, "_parent": {"__id__": 34}, "_children": [], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "_prefab": {"__id__": 42}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0aEBvCRNNULNacl2E+apN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 39}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "96V7SHU4hHHIX0ny1Ggfyt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 41}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8Vds4/B1OorI55NDUDjxn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92AlOl/0tLD6uYq3SX/9xQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5ed98ae1-f38d-4af1-8fe6-447c372ad250@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7Lvv1a0BCnaqGcaOmdhuf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 46}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "75Bfnk0LBCLoq2EsWYS2VR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 48}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 32}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6/Yo8jTVC041B6TmDN76u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0au9ybGc1OfqHYocJ0aW08"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 51}, {"__id__": 53}, {"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": -160, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 52}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2d952bd0-7d56-4a9f-b3a0-8ff4903747cf@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fddXsdtpNH4JMti6xbW31V"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 54}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "52kr8Ex+JAErBP6cYCXpwI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "__prefab": {"__id__": 56}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "efkT4GM95Ewb8jxQ78RMBK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49weHcjulN6bYS6Xe/REJ1"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [{"__id__": 59}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": -159.845, "y": 0.2, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Ellipse 3", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": -22, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 61}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "06023c31-d698-41a1-abdb-ebec9fd4cb5f@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22KS34ilJIAavPjt9bce0P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 63}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebIsxWJxpFeLU6Ancfr9vY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "__prefab": {"__id__": 65}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9NY2Wx+JDHpTJOMw5YCle"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4amiGti9VJn4CNVrF2FbxE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 68}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3076a55-499c-4a71-a4c0-60e735bc5fc9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfFrIDTkBFZI9A2tKqrNBv"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 70}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c5VHed/1G852WJECwOL/R"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "__prefab": {"__id__": 72}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0f2Ikuj9lH2oA9n52n7b0m"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98oM9+9wlH8rvRXWh1UvZp"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 78}, {"__id__": 80}], "_prefab": {"__id__": 82}, "_lpos": {"__type__": "cc.Vec3", "x": -157.311, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "clickEvents": [{"__id__": 77}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fEEQMnDBJTr9q0ZRc3ZSM"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "useFree", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 79}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dgSegq/tBOq2sxkCE5WjL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 81}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3264FSn0FOUYR6bhH/x7DN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5F+1h8kZFDbjbjHWV+QDf"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 84}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> mi<PERSON>n phí", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "af7VCSj9dDdJl+ZORiBxnb"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 86}, "_alignFlags": 9, "_target": null, "_left": 200, "_right": 0, "_top": 70, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90TO/zEopHCK6Nywaxko02"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "id": "spr074", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 88}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6hf+gY0NHcZ163ZKI+CbC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 90}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2/NGgFntMrYyB+s9m1Y7C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 92}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 170.77, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "faWS0MXKZKC4ov9PoSW/6r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c79DQVDBxP/5dwDQu6jRrT"}, {"__type__": "cc.Node", "_name": "SettingBtn", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 95}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 105}, {"__id__": 107}, {"__id__": 110}, {"__id__": 112}], "_prefab": {"__id__": 114}, "_lpos": {"__type__": "cc.Vec3", "x": 795.5, "y": 695.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 98}, {"__id__": 100}], "_prefab": {"__id__": 102}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 97}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "303c7b44-9b46-41a6-854c-ffa2e8ae5798@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9oPmPEvdJT6wP4Ya7CFe8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 99}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "48/Bj+C31JDZz/qP2Rduo7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 101}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "17RmH090xPO7nGMZsEk08r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60jpYqtUZK7qmc22//RRGp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 104}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b03e6462-965c-4ba2-8f2a-9d84f2915da9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22j5Y07EpEha7rmydhYYh8"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 106}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 180, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "616QFBMxhEyZbBT0ouXTKd"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 108}, "clickEvents": [{"__id__": 109}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58Gjjid2VHjZNMYhyHYCtI"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "activeSelectOdd", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 111}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "363YoFrOtNQpUSXJ/hWB20"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 113}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96uNQ2NZlMx52lu+/MKOVL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f40LjGE7NNKpWxXA1nK6Hd"}, {"__type__": "cc.Node", "_name": "QuickBtn", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 116}], "_active": true, "_components": [{"__id__": 124}, {"__id__": 126}, {"__id__": 128}, {"__id__": 131}, {"__id__": 133}], "_prefab": {"__id__": 135}, "_lpos": {"__type__": "cc.Vec3", "x": 695.5, "y": 695.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 115}, "_children": [], "_active": true, "_components": [{"__id__": 117}, {"__id__": 119}, {"__id__": 121}], "_prefab": {"__id__": 123}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 118}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "fe129766-d333-4c03-909b-917fbd67969d@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "906f0CBSBHCo9XnMgcxB5A"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 120}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cVdtibTVGJ4HaX7orUsV2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "__prefab": {"__id__": 122}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 45, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1e3FMFTQFGa6Ge8AcaFF9R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0XWGTTzdBUYYIyjCkXJJi"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 125}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b03e6462-965c-4ba2-8f2a-9d84f2915da9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12oRYAboNLroXlyrdA4nab"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 127}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 280, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4coFBdB1lP/KH6ZO4GKPsQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 129}, "clickEvents": [{"__id__": 130}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dmEriCkBCFqmPf+l9K4Dt"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "setQuickBet", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 132}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fBaqPyrpCx5HRzTbGDe8P"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "__prefab": {"__id__": 134}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dfDIXvHtGzLJ1I1hRsbHE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39ZFIP5INJ4qwlTVCpGAN+"}, {"__type__": "cc.Node", "_name": "CloseBtn", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 137}], "_active": true, "_components": [{"__id__": 145}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}], "_prefab": {"__id__": 156}, "_lpos": {"__type__": "cc.Vec3", "x": 895.5, "y": 695.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}, {"__id__": 142}], "_prefab": {"__id__": 144}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 139}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "efe93bdd-59ae-4d01-be41-cabbf88ac4b2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56cX7AVwJA8p5IvtlIRbYL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 141}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3hidy7wFHmJA6lOam1anf"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 143}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bXqZ7jDpELo7TeRH7k3uf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69hnWxHWFFK6C67OwreJo5"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 146}, "clickEvents": [{"__id__": 147}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c1iKQ41JDtIta+oW3mEP6"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "deactivePage", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b03e6462-965c-4ba2-8f2a-9d84f2915da9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddoo6sHDhAXoxK+1X2vwH+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 151}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 80, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61wfM6Y9pGNbko0hIfbKwm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 153}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fCoELPPtH5KifK93cQzy0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 136}, "_enabled": true, "__prefab": {"__id__": 155}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9u+hzKY5Aw6bAUDRtxpiI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c0MPlNlH5JxZixGwNyQHDg"}, {"__type__": "cc.Node", "_name": "layoutBtn", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 158}, {"__id__": 202}, {"__id__": 246}, {"__id__": 286}], "_active": true, "_components": [{"__id__": 326}, {"__id__": 328}, {"__id__": 330}, {"__id__": 332}, {"__id__": 334}], "_prefab": {"__id__": 336}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 594, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "SingleToggle", "_objFlags": 0, "_parent": {"__id__": 157}, "_children": [{"__id__": 159}, {"__id__": 177}], "_active": true, "_components": [{"__id__": 195}, {"__id__": 197}, {"__id__": 199}], "_prefab": {"__id__": 201}, "_lpos": {"__type__": "cc.Vec3", "x": -175, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 158}, "_children": [{"__id__": 160}], "_active": true, "_components": [{"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 159}, "_children": [], "_active": true, "_components": [{"__id__": 161}, {"__id__": 163}, {"__id__": 165}, {"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 162}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Đơn", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "630+xibKJJN4jlAqgarijq"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "id": "spr091", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 164}}, {"__type__": "cc.CompPrefabInfo", "fileId": "50qOPM+qJGZrkuo0whUkDm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 166}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cPNg2o0FJ47uDXEmIWgzS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 160}, "_enabled": true, "__prefab": {"__id__": 168}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9D4LylwNCZYNIUgNK76xw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9fWowQOFM34i6M0M3d++u"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 171}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4A6/uPwlHAICjbRlDZMn0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 173}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "695GRkXiBGx4cbPAhYG4BA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 159}, "_enabled": true, "__prefab": {"__id__": 175}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cpG6wZsxM0ZaupNRoEXGu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbJ4dmeipKLKULVsXtuswI"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 158}, "_children": [{"__id__": 178}], "_active": true, "_components": [{"__id__": 188}, {"__id__": 190}, {"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 177}, "_children": [], "_active": true, "_components": [{"__id__": 179}, {"__id__": 181}, {"__id__": 183}, {"__id__": 185}], "_prefab": {"__id__": 187}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 180}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "Đơn", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51IGRhw/xDkIC9/DdYvzXx"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "id": "spr091", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 182}}, {"__type__": "cc.CompPrefabInfo", "fileId": "93mWQHwslHCqSV3aIR3Eai"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 184}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "55P+0sTGJFXpHF7OV16OSY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 186}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "db0MKK5xVH2KLQwNMccSRf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3cZ9omxnhCqIeYStzytrOE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 189}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dduQ7Ph5Ng5IwFT5wATwB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 191}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "08lsZRFTBL9p6gEMBJ7MuB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 193}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2FWpATbRI3LFkcteO9LtC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bj8Py6nZJ6pUMGQ9Si/es"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 196}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 159}, "checkEvents": [], "_isChecked": true, "_checkMark": {"__id__": 188}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90qoZl4MxJXIV3ouS036l8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 198}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "31K85+y/5K/Yc4YUTWFjdO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 158}, "_enabled": true, "__prefab": {"__id__": 200}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "38egEC0NdPwa0gC9JBT8Bv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1affFKmcdO9LKNXmfQcz1h"}, {"__type__": "cc.Node", "_name": "ManyToggle", "_objFlags": 0, "_parent": {"__id__": 157}, "_children": [{"__id__": 203}, {"__id__": 221}], "_active": true, "_components": [{"__id__": 239}, {"__id__": 241}, {"__id__": 243}], "_prefab": {"__id__": 245}, "_lpos": {"__type__": "cc.Vec3", "x": 175, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 202}, "_children": [{"__id__": 204}], "_active": true, "_components": [{"__id__": 214}, {"__id__": 216}, {"__id__": 218}], "_prefab": {"__id__": 220}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 203}, "_children": [], "_active": true, "_components": [{"__id__": 205}, {"__id__": 207}, {"__id__": 209}, {"__id__": 211}], "_prefab": {"__id__": 213}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 206}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "209Pk7zbRFZ6FfXEy9FheQ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "id": "spr092", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 208}}, {"__type__": "cc.CompPrefabInfo", "fileId": "47KRuHcTVGZ6hchF5FQe7g"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 210}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeU6x5c51IDKVHwF2bSwuH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 204}, "_enabled": true, "__prefab": {"__id__": 212}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c41nREPCxAkJJJyEB5Mp6y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2cqnMdnQ9NnKMUaxbAS94T"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55W12bLLlDPJYQzHiY1seN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 217}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cIY5XDDJPpL5UFD1mfshm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 203}, "_enabled": true, "__prefab": {"__id__": 219}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "88ObUfcilF2JPU5OBDAlMr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "be1qDfwXJHjbsjBmvC3II0"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 202}, "_children": [{"__id__": 222}], "_active": false, "_components": [{"__id__": 232}, {"__id__": 234}, {"__id__": 236}], "_prefab": {"__id__": 238}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 221}, "_children": [], "_active": true, "_components": [{"__id__": 223}, {"__id__": 225}, {"__id__": 227}, {"__id__": 229}], "_prefab": {"__id__": 231}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 224}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "294CzdI2FDU4aI1f/wwY8H"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "id": "spr092", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 226}}, {"__type__": "cc.CompPrefabInfo", "fileId": "04PHrLlrZP+r5FOvP4QDAP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 228}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c6SeQBeBLnKfKD9UmZWc0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 230}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "daepN9b2dG+bLlAzBhHjkA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6cgDYSa9dF4pNC3gtcB8wN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 221}, "_enabled": true, "__prefab": {"__id__": 233}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74HSprdBJGOp6qR58jUd9Z"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 221}, "_enabled": true, "__prefab": {"__id__": 235}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbLP/Sj7VA7qIxsmm0TxV1"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 221}, "_enabled": true, "__prefab": {"__id__": 237}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "31xA+I5h9Ar61EOBti4g+C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28M9z3S+lDR6hC3MaPzX5i"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 202}, "_enabled": true, "__prefab": {"__id__": 240}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 203}, "checkEvents": [], "_isChecked": false, "_checkMark": {"__id__": 232}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fWyRY9s1Kcr30KdSY0kU2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 202}, "_enabled": true, "__prefab": {"__id__": 242}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "adBovGtIhHcYmJxntnrqxE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 202}, "_enabled": true, "__prefab": {"__id__": 244}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2Q7TjESJC46vy37sesiDw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26Np61ty1H8YC8x0YJBLAH"}, {"__type__": "cc.Node", "_name": "SystemToggle", "_objFlags": 0, "_parent": {"__id__": 157}, "_children": [{"__id__": 247}, {"__id__": 263}], "_active": false, "_components": [{"__id__": 279}, {"__id__": 281}, {"__id__": 283}], "_prefab": {"__id__": 285}, "_lpos": {"__type__": "cc.Vec3", "x": 125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 246}, "_children": [{"__id__": 248}], "_active": true, "_components": [{"__id__": 256}, {"__id__": 258}, {"__id__": 260}], "_prefab": {"__id__": 262}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 247}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 250}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> th<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71K7akotVGnIqVqAG2Td3P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 252}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bexAklLklOnL6Nx00yisB/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 254}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecN2ksGkFBKodOyfyp9hx+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70FV2sro5HaY5+//UtBjzI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 257}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0QHxOOmpBtbsQIszkOW7+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 259}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6e3CHgTzNDA6vMrs1bkbZ+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "__prefab": {"__id__": 261}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8XEwHsfBG8a5obQH7+vXF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abaXJhbi1FIIQ87+9xKRx1"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 246}, "_children": [{"__id__": 264}], "_active": false, "_components": [{"__id__": 272}, {"__id__": 274}, {"__id__": 276}], "_prefab": {"__id__": 278}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 263}, "_children": [], "_active": true, "_components": [{"__id__": 265}, {"__id__": 267}, {"__id__": 269}], "_prefab": {"__id__": 271}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 266}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> th<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8YaSiawRJVqIql9rtqd9f"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 268}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dHKytDNBLyL7H8gY6PMVa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 270}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6OJo4jyZA0YZMZTOvubGM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eRkwxVIFM+7wS0KU5baH+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 273}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b0njL6qtLtLTtOYCur8OP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 275}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8wsb40+BBdpK+eVX4Miqu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 263}, "_enabled": true, "__prefab": {"__id__": 277}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d54C6IrYFElJNdUlq6HY7b"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0aI/YSCEtA1L0LjECWH3I1"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 280}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 247}, "checkEvents": [], "_isChecked": false, "_checkMark": {"__id__": 272}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5QAAtB/ZLLKMheusHqw7P"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 282}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fxPlY1gVPAoUcfQf/LcKy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "__prefab": {"__id__": 284}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eNblrvy5AP4J149ZJyk9r"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bczCKn5sFPvYHt3Ll+HyZP"}, {"__type__": "cc.Node", "_name": "StreakToggle", "_objFlags": 0, "_parent": {"__id__": 157}, "_children": [{"__id__": 287}, {"__id__": 303}], "_active": false, "_components": [{"__id__": 319}, {"__id__": 321}, {"__id__": 323}], "_prefab": {"__id__": 325}, "_lpos": {"__type__": "cc.Vec3", "x": 375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 286}, "_children": [{"__id__": 288}], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "_prefab": {"__id__": 302}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 287}, "_children": [], "_active": true, "_components": [{"__id__": 289}, {"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 290}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Chuỗi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10Ctlpk7pD5Y1z0xcYczWS"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 292}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd+AsQ87NPOoz4Yd3BoY8C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 288}, "_enabled": true, "__prefab": {"__id__": 294}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "60dc9+1F9Iua66swCEdHzZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02ASxms4tMZafbq11zHnjr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 297}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9T4BSQwBMFrTa/k8kR6hj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 299}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dH9k6PRtJK5WsuSH5utp/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 287}, "_enabled": true, "__prefab": {"__id__": 301}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9NoFOZ7RE3anaxon0foKW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53aKY07vhIH7JA0yj38WEq"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 286}, "_children": [{"__id__": 304}], "_active": false, "_components": [{"__id__": 312}, {"__id__": 314}, {"__id__": 316}], "_prefab": {"__id__": 318}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 303}, "_children": [], "_active": true, "_components": [{"__id__": 305}, {"__id__": 307}, {"__id__": 309}], "_prefab": {"__id__": 311}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 306}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "Chuỗi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 1, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7odCnaIxJoIUPDsDw5LjT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 308}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "03O4H2g2FBh7YhGGymyNC9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 304}, "_enabled": true, "__prefab": {"__id__": 310}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25cb0OGitEorSH9GGL/t11"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ad/1yPNsFLm6RMvPJz0foK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 313}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1SL8+t05HhLFVlQfiBLYf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 315}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "47AqpoRXFMfaIAdSbIAz0o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 317}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6foT7YsV9NTo3GLMdJHnJG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40Ny/I4L1Jqbxu/92sAJc0"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "__prefab": {"__id__": 320}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 287}, "checkEvents": [], "_isChecked": false, "_checkMark": {"__id__": 312}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8HwCP+DpKfq+VCA7/Bguj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "__prefab": {"__id__": 322}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "23zWv/dhVAE6Mg1WKmCEeN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 286}, "_enabled": true, "__prefab": {"__id__": 324}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6KSe125xKDYOytX/N05me"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6uAa8x3NKIp3fCsqfMBHI"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 327}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 100, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 600, "height": 100}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccmzJfPlBBoZBv/fUYoLyu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 329}, "_alignFlags": 41, "_target": null, "_left": 710, "_right": 710, "_top": 153, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9FUMDe8dCAoECQPAeWbWC"}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 331}, "_allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "790dzxQbZALbxRc+W9hxr9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 333}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4b5Cx9LL5JkbbqUEibv8WN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 335}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4N+YG0a1PvIa35BY5wXg9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9bVdRXNEBCcYd1rBRmHOsh"}, {"__type__": "cc.Node", "_name": "InputMoneyForm", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 338}, {"__id__": 360}, {"__id__": 370}, {"__id__": 412}, {"__id__": 424}, {"__id__": 436}, {"__id__": 444}], "_active": true, "_components": [{"__id__": 465}, {"__id__": 467}, {"__id__": 469}, {"__id__": 471}], "_prefab": {"__id__": 473}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -577.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "MaxBtn", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [{"__id__": 339}], "_active": true, "_components": [{"__id__": 349}, {"__id__": 351}, {"__id__": 353}, {"__id__": 355}, {"__id__": 357}], "_prefab": {"__id__": 359}, "_lpos": {"__type__": "cc.Vec3", "x": 821.5, "y": 101.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 338}, "_children": [], "_active": true, "_components": [{"__id__": 340}, {"__id__": 342}, {"__id__": 344}, {"__id__": 346}], "_prefab": {"__id__": 348}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 341}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> đa", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfgs0XCGVKWZaQbtiN1lQT"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "id": "spr095", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 343}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5citgoar1Ioax0yAKHR0TU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 345}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "13ihrGYq1IuYtpu7BwD2pG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 347}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 72.26, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffTINSPJ9KqLdQo7uH9CQ0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c08TghL59L5I5iIeDtk135"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 350}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "97b075fc-007c-4f42-9871-70e885a98c24@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7QT1sVxVOKL9IqCoDsWAD"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 352}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7gSuPdHtIbJBqFdQMuodh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 354}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 50, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eKe7009xBkZzHD7GqhGAf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 356}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "44rwLtQVNB6bvunw+pHeCm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 338}, "_enabled": true, "__prefab": {"__id__": 358}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1/rtDXeVH1oi4Rq25Nz52"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26pf3Elz1JtoBomGpxwYoA"}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [], "_active": true, "_components": [{"__id__": 361}, {"__id__": 363}, {"__id__": 365}, {"__id__": 367}], "_prefab": {"__id__": 369}, "_lpos": {"__type__": "cc.Vec3", "x": 876.995, "y": 40.010000000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 362}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": " 2 VIW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 22, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73RiTJPXtOmYojvo1bkdcz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 364}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 130.63, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abeN+amQ9IM6JkdMUDyIbj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 366}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fZPiCH3hG74Rzo4GZZXlm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 360}, "_enabled": true, "__prefab": {"__id__": 368}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 66.01, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "06m74PQRpMJpF1qjtmbkNI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "508ptHAOlJ5o6lW5sJiy4I"}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [{"__id__": 371}, {"__id__": 381}, {"__id__": 391}], "_active": true, "_components": [{"__id__": 403}, {"__id__": 405}, {"__id__": 407}, {"__id__": 409}], "_prefab": {"__id__": 411}, "_lpos": {"__type__": "cc.Vec3", "x": -668, "y": 100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 370}, "_children": [], "_active": true, "_components": [{"__id__": 372}, {"__id__": 374}, {"__id__": 376}, {"__id__": 378}], "_prefab": {"__id__": 380}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 373}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fw3VJVwJNbJEX8o7ckPZ/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 375}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -13, "_bottom": -13, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1qUHnaPdPnpz6M+rKrbHo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 377}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "63TzoVXmRAfKORQAzm42oD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 371}, "_enabled": true, "__prefab": {"__id__": 379}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "47wBgor1tFyKO7u+/azvoC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87BngslYpBR7hl+7jyZMIC"}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 370}, "_children": [], "_active": false, "_components": [{"__id__": 382}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}], "_prefab": {"__id__": 390}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 383}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5ctMOUeStBmrwo5dwvfhua"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 385}, "_alignFlags": 45, "_target": null, "_left": 10, "_right": -8, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05GCwiK0xHOpy/1a+KIBOj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 387}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "021SDLcpFGCaPVHJ546ygh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 381}, "_enabled": true, "__prefab": {"__id__": 389}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 548, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdnMoeAY1B67ZjBpybCXWm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68fzIgi6FBT4/xxQziDAq7"}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 370}, "_children": [], "_active": true, "_components": [{"__id__": 392}, {"__id__": 394}, {"__id__": 396}, {"__id__": 398}, {"__id__": 400}], "_prefab": {"__id__": 402}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 393}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "556Q59VtNF+4qQTwsfok9o"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 395}, "_alignFlags": 45, "_target": null, "_left": 10, "_right": 90, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0rKWxC/9C9ohAEU7noOWY"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "id": "xs12", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 397}}, {"__type__": "cc.CompPrefabInfo", "fileId": "753iBO0XRKGI7DGjVSQUmT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 399}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6f9k1R9tJAM6mOzi507aNe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 391}, "_enabled": true, "__prefab": {"__id__": 401}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "de1uRHE1ZFO447QILZ7QUY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cekn2HiilBubROHDfKv1BW"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 404}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 382}, "_placeholderLabel": {"__id__": 392}, "_returnType": 0, "_useOriginalSize": true, "_string": "", "_tabIndex": 0, "_backgroundImage": null, "_inputFlag": 5, "_inputMode": 2, "_maxLength": 20, "returnType": 0, "maxLength": 12, "_background": {"__id__": 372}, "_stayOnTop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9NSPiY+9IXJfiG8Rysn0m"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 406}, "_alignFlags": 9, "_target": null, "_left": 67, "_right": 0, "_top": 64.5, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bMDICyOtPGJbRNvZxenXu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 408}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c1PK94UlGw6hiuHg+gz8e"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 370}, "_enabled": true, "__prefab": {"__id__": 410}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "70iNZspglFlo5L8hkFjaop"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c26UrsrX9FCLMSspKh+B/s"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [], "_active": true, "_components": [{"__id__": 413}, {"__id__": 415}, {"__id__": 417}, {"__id__": 419}, {"__id__": 421}], "_prefab": {"__id__": 423}, "_lpos": {"__type__": "cc.Vec3", "x": -885.563, "y": 40.010000000000005, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 412}, "_enabled": true, "__prefab": {"__id__": 414}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> thể thắng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65e8qYo/ZG8qLzcrrvY4w8"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 412}, "_enabled": true, "id": "spr094", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 416}}, {"__type__": "cc.CompPrefabInfo", "fileId": "64pPpqkNlGSKie9gEwr0UF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 412}, "_enabled": true, "__prefab": {"__id__": 418}, "_alignFlags": 9, "_target": null, "_left": 72.89700000000003, "_right": 1017.194, "_top": 130.63, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67RFVJbN5PkKwnVYoh+FVO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 412}, "_enabled": true, "__prefab": {"__id__": 420}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbqqw14XxG1ZOBeFiE40xr"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 412}, "_enabled": true, "__prefab": {"__id__": 422}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 103.08, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "aedza18W5OZ46S5Nt+eHuE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4NxPMh0NHIoqWhLKvmQw3"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [], "_active": true, "_components": [{"__id__": 425}, {"__id__": 427}, {"__id__": 429}, {"__id__": 431}, {"__id__": 433}], "_prefab": {"__id__": 435}, "_lpos": {"__type__": "cc.Vec3", "x": -616, "y": -14.071999999999989, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 426}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Tổng tiền cược nằm ngoài phạm vi cho phép là từ 1,000 - 20,000,000,000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "976ptSxJFKY7vfXcaihuWv"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 424}, "_enabled": true, "id": "spr142", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 428}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ccB7lzBXBHu4R7QMshaqX9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 430}, "_alignFlags": 9, "_target": null, "_left": 98.86000000000001, "_right": 1017.194, "_top": 184.712, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aniX8QsxIBrYQk84TF0f/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 432}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dyk2H5+lJIogwsFSVpcMM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 424}, "_enabled": true, "__prefab": {"__id__": 434}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 590.28, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2alENFPhMcKE0D5nHfFkL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45JkkY2aJOoajaYREsbPnu"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [], "_active": true, "_components": [{"__id__": 437}, {"__id__": 439}, {"__id__": 441}], "_prefab": {"__id__": 443}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": -13, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 438}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "05b73689-54a6-4868-af55-ac49f1a018c1@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30rmkYiFZJK4eWKmvLK1v+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 440}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2InXoHG9FpLejlNKiRRGH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 436}, "_enabled": true, "__prefab": {"__id__": 442}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fQMQQXbxE5YL6iPpLnpe+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1iO88CRhGE7NOxPEu/boy"}, {"__type__": "cc.Node", "_name": "BetBtn", "_objFlags": 0, "_parent": {"__id__": 337}, "_children": [{"__id__": 445}], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}, {"__id__": 460}, {"__id__": 462}], "_prefab": {"__id__": 464}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 444}, "_children": [], "_active": true, "_components": [{"__id__": 446}, {"__id__": 448}, {"__id__": 450}, {"__id__": 452}], "_prefab": {"__id__": 454}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 447}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "ĐẶT CƯỢC", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1q22VNK5I/498mINOIcnG"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 445}, "_enabled": true, "id": "spr096", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 449}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbvQ7MAiZCt7T67xGBdbgR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 451}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfDvoiG4lNJ6ewV/arUNrL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 445}, "_enabled": true, "__prefab": {"__id__": 453}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 217.34, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0uNWckAtEcZuaq7KSJg4a"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17YgVuziVKHZr04SU2V76k"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 456}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "069b8bde-4518-474e-9504-701bcc8a8638@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86e7UW1n5EOI/tN+ibOZI6"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 458}, "clickEvents": [{"__id__": 459}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a51USCHLdCxLpW4eVcPxQA"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "doBet", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 461}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dIufzm4JBRb0vrrvpJT3D"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 444}, "_enabled": true, "__prefab": {"__id__": 463}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7599SgBI1NIJzLHDwACPFO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02ZMW10PVCc5K6u9RFYGA0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 466}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 49, "g": 51, "b": 58, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20iuBWhDFLYImQMPXf1oY9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 468}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 35, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ba96WupxxIzpxdIfMzLmc5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 470}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0480/Bm2hMTqh/qLMT3IMO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 337}, "_enabled": true, "__prefab": {"__id__": 472}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 369}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0KR9LDSZCGaZ70X8zqb5y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76WQJSt1hOArmLCd4VpEaM"}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 475}], "_active": true, "_components": [{"__id__": 495}, {"__id__": 497}, {"__id__": 499}, {"__id__": 501}], "_prefab": {"__id__": 503}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 474}, "_children": [{"__id__": 476}], "_active": true, "_components": [{"__id__": 486}, {"__id__": 488}, {"__id__": 490}, {"__id__": 492}], "_prefab": {"__id__": 494}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 475}, "_children": [], "_active": true, "_components": [{"__id__": 477}, {"__id__": 479}, {"__id__": 481}, {"__id__": 483}], "_prefab": {"__id__": 485}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 397, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 476}, "_enabled": true, "__prefab": {"__id__": 478}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1920, "height": 138}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07pnmMsDxFqY2ArV/wXxxT"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 476}, "_enabled": true, "__prefab": {"__id__": 480}, "_alignFlags": 41, "_target": null, "_left": 50, "_right": 50, "_top": 75, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4cHeHIsFNJIPEXQogIBOd"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 476}, "_enabled": true, "__prefab": {"__id__": 482}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "21/Az43x1Gi7WdpVDVwG6L"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 476}, "_enabled": true, "__prefab": {"__id__": 484}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 138}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9VveJZo1MObyF6I5qQNly"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94ai2kOXhD57KzJPCHJvwd"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 487}, "_materials": [], "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6EVq3ZexBq7S3QrBPr39c"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 489}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "999qsb/i9OMZEtBV6CufQU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 491}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dGW8u5UxOtYB86DuLDAm4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 475}, "_enabled": true, "__prefab": {"__id__": 493}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 944}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a2OdA7K9Pk5cSLfWNp49F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "79+sE0tn5BeI9iYBfj/5BH"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 496}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 476}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeglVaQhVNSoHH3tLFtC6X"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 498}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 250, "_bottom": 400, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21jwznOrNFp7HVPpeauNKc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 500}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "67TRV4WyRDBa9SxIKiOqx6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 502}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 944}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3f/c6zKZ9Il50yZJppVJkB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "45qQvBQ6BAMoi7gboAnbn9"}, {"__type__": "cc.Node", "_name": "InputMoneyFormMany", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 505}, {"__id__": 527}, {"__id__": 537}, {"__id__": 579}, {"__id__": 591}, {"__id__": 599}, {"__id__": 617}, {"__id__": 629}, {"__id__": 637}, {"__id__": 655}], "_active": false, "_components": [{"__id__": 667}, {"__id__": 669}, {"__id__": 671}, {"__id__": 673}], "_prefab": {"__id__": 675}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -592, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "MaxBtn", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [{"__id__": 506}], "_active": true, "_components": [{"__id__": 516}, {"__id__": 518}, {"__id__": 520}, {"__id__": 522}, {"__id__": 524}], "_prefab": {"__id__": 526}, "_lpos": {"__type__": "cc.Vec3", "x": 391.5, "y": 170, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 505}, "_children": [], "_active": true, "_components": [{"__id__": 507}, {"__id__": 509}, {"__id__": 511}, {"__id__": 513}], "_prefab": {"__id__": 515}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 506}, "_enabled": true, "__prefab": {"__id__": 508}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> đa", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3UnT9zAxLyYgvleio8Nvl"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 506}, "_enabled": true, "id": "spr095", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 510}}, {"__type__": "cc.CompPrefabInfo", "fileId": "851yCfGnlHc5qetnIxcRe0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 506}, "_enabled": true, "__prefab": {"__id__": 512}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0QPIbpEVDVZ+w9Ez/SDnn"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 506}, "_enabled": true, "__prefab": {"__id__": 514}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 72.26, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3f3t1a9fpNV7yECr3vFJqJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27sdpXGkdPa4C016ZMZSON"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 517}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "97b075fc-007c-4f42-9871-70e885a98c24@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94UYluqeVM/Yh6Tw5Uhpzy"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 519}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c9T8IdrVLKJoeEcTp9Tck"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 521}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 50, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bwVPgpsVFD79eqe9+AX6u"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 523}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6btkxrIgZBGqX1K62JiNTs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 525}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dhiZMh6tFZZS1VYR3o2Pa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66ulcXcoZHho0AZ36L6F8d"}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 528}, {"__id__": 530}, {"__id__": 532}, {"__id__": 534}], "_prefab": {"__id__": 536}, "_lpos": {"__type__": "cc.Vec3", "x": 446.995, "y": 229.20300000000003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 527}, "_enabled": true, "__prefab": {"__id__": 529}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "22:42", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38DMWVmcVO1LB54BwK/pi6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 527}, "_enabled": true, "__prefab": {"__id__": 531}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 110.485, "_top": 9.93699999999997, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54SoJfpjBK5JRYPJ7Hvzjx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 527}, "_enabled": true, "__prefab": {"__id__": 533}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "95JYlKc/lE56OzTRpbcESV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 527}, "_enabled": true, "__prefab": {"__id__": 535}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 45.04, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a51P1xlEVGA7X4PGksZ6Zj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09GoleIBNLioYjY9MCiGiJ"}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [{"__id__": 538}, {"__id__": 548}, {"__id__": 558}], "_active": true, "_components": [{"__id__": 570}, {"__id__": 572}, {"__id__": 574}, {"__id__": 576}], "_prefab": {"__id__": 578}, "_lpos": {"__type__": "cc.Vec3", "x": -238, "y": 168.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 537}, "_children": [], "_active": true, "_components": [{"__id__": 539}, {"__id__": 541}, {"__id__": 543}, {"__id__": 545}], "_prefab": {"__id__": 547}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 540}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fI7Smr45Hg5wbsgOhq0ta"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 542}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -13, "_bottom": -13, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6be+eAsANOSYCLznrDgsQL"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 544}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "01ley6sjNJDIK3np9ZP4N8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 538}, "_enabled": true, "__prefab": {"__id__": 546}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "78FnqIqWBHe6/0HoE56nc/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6d6Np5jJRLuozC7gyJW+2G"}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 537}, "_children": [], "_active": false, "_components": [{"__id__": 549}, {"__id__": 551}, {"__id__": 553}, {"__id__": 555}], "_prefab": {"__id__": 557}, "_lpos": {"__type__": "cc.Vec3", "x": -78, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 550}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ff1pVzvShJ2qfPY54HiKH/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 552}, "_alignFlags": 45, "_target": null, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3emsYZwuRI1rDGoQGSFaOJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 554}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "723h9jH35Nko/aovFe+SuG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 548}, "_enabled": true, "__prefab": {"__id__": 556}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "37q0hlBKFON5lPjmKy7KMw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32ww5MI0NEM4nJr3f8pQfz"}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 537}, "_children": [], "_active": true, "_components": [{"__id__": 559}, {"__id__": 561}, {"__id__": 563}, {"__id__": 565}, {"__id__": 567}], "_prefab": {"__id__": 569}, "_lpos": {"__type__": "cc.Vec3", "x": -273, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 560}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_useOriginalSize": true, "_string": "NHẬP TIỀN CƯỢC", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68UalSjxxESI8Qcp4J+DcL"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 558}, "_enabled": true, "id": "xs12", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 562}}, {"__type__": "cc.CompPrefabInfo", "fileId": "07oTy2mJ1HfLd+6ayiPtlQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 564}, "_alignFlags": 45, "_target": null, "_left": 2, "_right": 98, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8DkUkMjtGObW4MKjjHlk+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 566}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3etVCswpBKnqcQD7S8yb3j"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 568}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "71JETWHdlOxoRyOlCbsOCo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37ry8ewjFHhJhmpzxBsa3g"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 571}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 549}, "_placeholderLabel": {"__id__": 559}, "_returnType": 0, "_useOriginalSize": true, "_string": "", "_tabIndex": 0, "_backgroundImage": null, "_inputFlag": 5, "_inputMode": 3, "_maxLength": 20, "returnType": 0, "maxLength": 12, "_background": {"__id__": 539}, "_stayOnTop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "170AtoAiVOcr93b/4VTZ4l"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 573}, "_alignFlags": 9, "_target": null, "_left": 67, "_right": 0, "_top": 64.5, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aNWopdEBJ/pn/mj8CF7AN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 575}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f87gvRdjxNSasMdRUrAKQ6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 577}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "30LMOMxsBGL6XZwBdkYAp6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e8FHudqRZDcbx6dpRTd1lu"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 580}, {"__id__": 582}, {"__id__": 584}, {"__id__": 586}, {"__id__": 588}], "_prefab": {"__id__": 590}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 108.09400000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 581}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Một số sự kiện trong phiếu cư<PERSON><PERSON> đã thay đổi giá", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56a1fIOKRCjIIhrHDxuvOG"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "id": "spr101", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 583}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6domDsa59ADoa2Tgi0TF4G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 585}, "_alignFlags": 9, "_target": null, "_left": 123.94999999999999, "_right": 1017.194, "_top": 131.046, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30M6y2WIdAoYTlizIBvG8u"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 587}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eh5w3UGRHvKlACtSZlQ51"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 579}, "_enabled": true, "__prefab": {"__id__": 589}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 382.1, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9RGWoRwxDu5GIuHNgMvYX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25OTpDJ/lGTYA3TqG+NX/B"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 592}, {"__id__": 594}, {"__id__": 596}], "_prefab": {"__id__": 598}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": 105.666, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 593}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "05b73689-54a6-4868-af55-ac49f1a018c1@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66zjJ7FFJI3oktQC0wvjIj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 595}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "db60MREwFAYrhonpCtTDTt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 597}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "affpuJNVZOT465Isz0r3L6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2ahr9TLnhAyLI3mCGtqimL"}, {"__type__": "cc.Node", "_name": "ChangeBtn", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [{"__id__": 600}], "_active": true, "_components": [{"__id__": 610}, {"__id__": 612}, {"__id__": 614}], "_prefab": {"__id__": 616}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 599}, "_children": [], "_active": true, "_components": [{"__id__": 601}, {"__id__": 603}, {"__id__": 605}, {"__id__": 607}], "_prefab": {"__id__": 609}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 600}, "_enabled": true, "__prefab": {"__id__": 602}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "CHẤP NHẬN THAY ĐỔI", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05cq/ulstNsJsgyGnNGj1x"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 600}, "_enabled": true, "id": "spr102", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 604}}, {"__type__": "cc.CompPrefabInfo", "fileId": "83GzyNdCdFnKe41e6jiVti"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 600}, "_enabled": true, "__prefab": {"__id__": 606}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20cos+wnBN1Zz8x17/FfFM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 600}, "_enabled": true, "__prefab": {"__id__": 608}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 432.64, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2dA//EX1OtJhOL1v5kKHR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93jb+Gq7xA4ojMHvdxwIJM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 611}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "069b8bde-4518-474e-9504-701bcc8a8638@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1au/0t7xJKaLLWzoB8NMFG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 613}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "08F3BKFl9GHaEvNI+8Scol"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 599}, "_enabled": true, "__prefab": {"__id__": 615}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "72slfjWL9Ct4+29QJOZM7F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dlVTT4uFBQrBuBFpi8oZ2"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 618}, {"__id__": 620}, {"__id__": 622}, {"__id__": 624}, {"__id__": 626}], "_prefab": {"__id__": 628}, "_lpos": {"__type__": "cc.Vec3", "x": -388, "y": 61.27900000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 619}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Đặt cọc để cược", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56VSXpSVVOh5S6cEfBVf83"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "id": "spr096", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 621}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3OVVwWsVGebmjlfPIZMpU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 623}, "_alignFlags": 9, "_target": null, "_left": 125.55000000000001, "_right": 1017.194, "_top": 177.861, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24HZ8eWlJOE7xv6DN/7Mn1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 625}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5axzplrmJCtqXwrhvu7Xje"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 627}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 132.9, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2ybBJceVD6I5UaAxvXmqJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3e0/pE3pJtpoY1YUPGIQs"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 630}, {"__id__": 632}, {"__id__": 634}], "_prefab": {"__id__": 636}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": 58.851, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 631}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0e4ad8d1-80d0-463c-a320-2aabe06dd0fa@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77s+7WdH9HmJ2WEaGAtI7c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 633}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "80I2xTZc5Ar5N45de6uXN0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 629}, "_enabled": true, "__prefab": {"__id__": 635}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e225fjBoVBq4d5c+Feb648"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55GId5nhNIqpGgpM5QTz2o"}, {"__type__": "cc.Node", "_name": "BetBtn", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [{"__id__": 638}], "_active": true, "_components": [{"__id__": 648}, {"__id__": 650}, {"__id__": 652}], "_prefab": {"__id__": 654}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150.693, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 637}, "_children": [], "_active": true, "_components": [{"__id__": 639}, {"__id__": 641}, {"__id__": 643}, {"__id__": 645}], "_prefab": {"__id__": 647}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 638}, "_enabled": true, "__prefab": {"__id__": 640}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "ĐẶT CƯỢC", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64FL0N47VP/rgyJmKecPcP"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 638}, "_enabled": true, "id": "spr096", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 642}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cLBkk3xRHv7QTxHpt3kqR"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 638}, "_enabled": true, "__prefab": {"__id__": 644}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "74iQgytNlJzKo9Hw6NfTzX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 638}, "_enabled": true, "__prefab": {"__id__": 646}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 217.34, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6nilW2vdGPKEMtYROTPOZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6adg2mGvVIIYsYtmMrYyX7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 649}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d2cc74a-58c8-4ab4-b2d3-38e479ea2995@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dKucZHSFKaqFfPNoVn9gB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 651}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3iQwuveNMOZWwwOMCRWGJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 637}, "_enabled": true, "__prefab": {"__id__": 653}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "70Pz+Ud/VExrueY3sL1wIb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaUsBRewpNZpARpz57Rr4C"}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 504}, "_children": [], "_active": true, "_components": [{"__id__": 656}, {"__id__": 658}, {"__id__": 660}, {"__id__": 662}, {"__id__": 664}], "_prefab": {"__id__": 666}, "_lpos": {"__type__": "cc.Vec3", "x": 374.209, "y": 229.20300000000003, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 657}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Tỉ lệ cược", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfMAzrtYpEEJcdWqYS065S"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "id": "spr121", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 659}}, {"__type__": "cc.CompPrefabInfo", "fileId": "94QTf2QLtK87H3rHc0NPcr"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 661}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 165.361, "_top": 9.93699999999997, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94tdvXKRRNwYWtMlF+Y2NE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 663}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "66Lki0GkhJh4KiFRcfQSOG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 655}, "_enabled": true, "__prefab": {"__id__": 665}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 80.86, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eoI+YDrZN6YcfSUBi3tcV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05ek+4MT1MU6qMwlaY7U3m"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 504}, "_enabled": true, "__prefab": {"__id__": 668}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 49, "g": 51, "b": 58, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bGJeVA5xEQK8ll8VUs5ri"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 504}, "_enabled": true, "__prefab": {"__id__": 670}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 35, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37hFQTmvFJNpuTXv26Qm4C"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 504}, "_enabled": true, "__prefab": {"__id__": 672}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1Tk1OfwJB+4TDr0HGNJ+x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 504}, "_enabled": true, "__prefab": {"__id__": 674}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 506}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1vXGjtlFKMad5WozjjM7E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2jO8mFD9JbILlnm7AqA/a"}, {"__type__": "cc.Node", "_name": "InputFreeTicket", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 677}, {"__id__": 687}, {"__id__": 699}, {"__id__": 728}, {"__id__": 740}, {"__id__": 750}, {"__id__": 762}], "_active": false, "_components": [{"__id__": 770}, {"__id__": 772}, {"__id__": 774}, {"__id__": 776}], "_prefab": {"__id__": 778}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -690, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 678}, {"__id__": 680}, {"__id__": 682}, {"__id__": 684}], "_prefab": {"__id__": 686}, "_lpos": {"__type__": "cc.Vec3", "x": 450.995, "y": 49.551, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 679}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": " 2 VIW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7elBgsSV1FEJjcUib8MXEt"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 681}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 86.589, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c62qLwm0FC8rQY7Bh9K4qc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 683}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e6EMGTBJEVpeuSHj/El1p"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 677}, "_enabled": true, "__prefab": {"__id__": 685}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 78.01, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1d2MZdRaVIt6kNt7tZ8dKN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "be4WYDjThKcqiaEiGu8TmZ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 688}, {"__id__": 690}, {"__id__": 692}, {"__id__": 694}, {"__id__": 696}], "_prefab": {"__id__": 698}, "_lpos": {"__type__": "cc.Vec3", "x": -445.555, "y": 49.551, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 689}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> thể thắng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29KIpSzwlPS5AZtJ9rxt5i"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 687}, "_enabled": true, "id": "spr094", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 691}}, {"__type__": "cc.CompPrefabInfo", "fileId": "45zm8/y7xCK6uOa6b54IyL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 693}, "_alignFlags": 9, "_target": null, "_left": 70, "_right": 1017.194, "_top": 86.589, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45pqgNZ6dCy4uoEyN9ES/c"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 695}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1epmuketBwLtqrvmgV/US"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 697}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 148.89, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "015tG83R5C55qWqhKYAtEr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "35Q/lNhApAPYCOtlkCHzg/"}, {"__type__": "cc.Node", "_name": "BetBtn", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [{"__id__": 700}, {"__id__": 708}], "_active": true, "_components": [{"__id__": 718}, {"__id__": 720}, {"__id__": 723}, {"__id__": 725}], "_prefab": {"__id__": 727}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -85.172, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Grayscale", "_objFlags": 0, "_parent": {"__id__": 699}, "_children": [], "_active": false, "_components": [{"__id__": 701}, {"__id__": 703}, {"__id__": 705}], "_prefab": {"__id__": 707}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 700}, "_enabled": true, "__prefab": {"__id__": 702}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d2cc74a-58c8-4ab4-b2d3-38e479ea2995@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8aFAfIdhC5bxiJU+7mAjf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 700}, "_enabled": true, "__prefab": {"__id__": 704}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "93yHNg7qFAfqZjlGkbp+G5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 700}, "_enabled": true, "__prefab": {"__id__": 706}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "17QIz9T+9MAZnYuDguU/X4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96v7EanVhGCqQxQyJftEVj"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 699}, "_children": [], "_active": true, "_components": [{"__id__": 709}, {"__id__": 711}, {"__id__": 713}, {"__id__": 715}], "_prefab": {"__id__": 717}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 708}, "_enabled": true, "__prefab": {"__id__": 710}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "DÙNG VÉ", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aiegf42lBSqbF7JSNdAA3"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 708}, "_enabled": true, "id": "td15", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 712}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4Ot+7QahHBJpmnEx6BI1w"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 708}, "_enabled": true, "__prefab": {"__id__": 714}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "40AexIs3lId6s3ZwOL5oUY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 708}, "_enabled": true, "__prefab": {"__id__": 716}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 182.25, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4f28kyFm5NgZWg2YFUWV7E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "acVzAe06hKvJ9HA1McgS0x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 719}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "069b8bde-4518-474e-9504-701bcc8a8638@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9N9AcmdVEJonC4G2QNM9J"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 721}, "clickEvents": [{"__id__": 722}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7GRHjILNCNI3P9AHrUbf9"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "doBet", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 724}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ch5fweKVHfKIY/DkkIdMF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 699}, "_enabled": true, "__prefab": {"__id__": 726}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c3uDOMwhLS6f7mHWQs0EJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5bJoxnjzlG+Jqcg8tN1DBt"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 729}, {"__id__": 731}, {"__id__": 733}, {"__id__": 735}, {"__id__": 737}], "_prefab": {"__id__": 739}, "_lpos": {"__type__": "cc.Vec3", "x": -520, "y": 97.038, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 730}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON>nh giá phi<PERSON>u c<PERSON><PERSON><PERSON> miễn phí", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5knoL+HpP0povqnkvMU0a"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 728}, "_enabled": true, "id": "spr074", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 732}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0amEUrqzFKsIC/CJaGImGx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 734}, "_alignFlags": 9, "_target": null, "_left": 70, "_right": 1017.194, "_top": 39.102000000000004, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27cNSlt/FPlrfvWAk/9PVM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 736}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bf4OAAj+pIxJrocV49gxYR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 728}, "_enabled": true, "__prefab": {"__id__": 738}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 352.42, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fnkKv7lpKf5QzJ5PYCNkF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bdWRJe6cBCTrS0QhxVcYt1"}, {"__type__": "cc.Node", "_name": "PriceValueLabel", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 741}, {"__id__": 743}, {"__id__": 745}, {"__id__": 747}], "_prefab": {"__id__": 749}, "_lpos": {"__type__": "cc.Vec3", "x": 490, "y": 97.038, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 742}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 46, "g": 161, "b": 103, "a": 255}, "_useOriginalSize": true, "_string": "1000 VIW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdIE5H7qxO1Lo+tiwQMUff"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 744}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 39.102000000000004, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57+D1RisxPQ5zL9rrW43ob"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 746}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20TONlZV9HlLqI1DA3nxvS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 740}, "_enabled": true, "__prefab": {"__id__": 748}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 114.17, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "39/MGQz11GyKA8wPfEzD17"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbZ4flDCdBULQaEap9zwZJ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 751}, {"__id__": 753}, {"__id__": 755}, {"__id__": 757}, {"__id__": 759}], "_prefab": {"__id__": 761}, "_lpos": {"__type__": "cc.Vec3", "x": -310, "y": -0.16899999999999693, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 752}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Bạn không có vé miễn phí nào", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8buom4VNBB1ZAvXAmp1JAF"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 750}, "_enabled": true, "id": "NO_DATA", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 754}}, {"__type__": "cc.CompPrefabInfo", "fileId": "26Hm8/W+RIPK6bQriGWKTO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 756}, "_alignFlags": 9, "_target": null, "_left": 104.38000000000002, "_right": 1017.194, "_top": 136.309, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5OO1Atc5Np7zgkuxgsv66"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 758}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8QujweJVFqbaKufaL5/81"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 750}, "_enabled": true, "__prefab": {"__id__": 760}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 351.24, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "27VyXU4NpOm6oXlf/sLPPE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ablZNA6nlDBKR0KanS2Ilo"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 676}, "_children": [], "_active": true, "_components": [{"__id__": 763}, {"__id__": 765}, {"__id__": 767}], "_prefab": {"__id__": 769}, "_lpos": {"__type__": "cc.Vec3", "x": -510, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 764}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "05b73689-54a6-4868-af55-ac49f1a018c1@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbwrPteoRClrJKqQotgoeo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 766}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "34pV4CwhdO8J4T5O0QFRbL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 762}, "_enabled": true, "__prefab": {"__id__": 768}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "046V54I1JIrJU/SUohbo/d"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2diEK543dBMpkubncJWhfl"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 771}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 49, "g": 51, "b": 58, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21s7PeFzRPGrS9DbqC7r1y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 773}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 40, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8axpTt8GFNtYhrz5NeaN2H"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 775}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddFWTOuydJebpR9Giam4jj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 676}, "_enabled": true, "__prefab": {"__id__": 777}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1180, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "783V59AtxHw4PJlxzYX0Ca"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40dFJrv2xM97vZ9JXsA0p7"}, {"__type__": "cc.Node", "_name": "InpuFreeTicketMany", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 780}, {"__id__": 790}, {"__id__": 802}, {"__id__": 810}, {"__id__": 828}, {"__id__": 846}, {"__id__": 858}, {"__id__": 868}, {"__id__": 880}, {"__id__": 892}], "_active": false, "_components": [{"__id__": 902}, {"__id__": 904}, {"__id__": 906}, {"__id__": 908}], "_prefab": {"__id__": 910}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -592, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 781}, {"__id__": 783}, {"__id__": 785}, {"__id__": 787}], "_prefab": {"__id__": 789}, "_lpos": {"__type__": "cc.Vec3", "x": 459.995, "y": 206, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 780}, "_enabled": true, "__prefab": {"__id__": 782}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "22:42", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9dwXWXPBNwoWONu8BhaUO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 780}, "_enabled": true, "__prefab": {"__id__": 784}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 97.485, "_top": 33.139999999999986, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0aOEf23tNd7uKon7R2ZKs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 780}, "_enabled": true, "__prefab": {"__id__": 786}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ab9L4jCmlFlKPRkP60Lo8Q"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 780}, "_enabled": true, "__prefab": {"__id__": 788}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 45.04, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8veJSaY9JuYGHxciFprpA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbZbDIFLlMbrP3fPJOl9QL"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 791}, {"__id__": 793}, {"__id__": 795}, {"__id__": 797}, {"__id__": 799}], "_prefab": {"__id__": 801}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 61.96900000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 790}, "_enabled": true, "__prefab": {"__id__": 792}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Một số sự kiện trong phiếu cư<PERSON><PERSON> đã thay đổi giá", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "43Q7/W8ftFZJloa9MWZcjy"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 790}, "_enabled": true, "id": "spr101", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 794}}, {"__type__": "cc.CompPrefabInfo", "fileId": "79Owgq0O1MyIQGBTHzjATe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 790}, "_enabled": true, "__prefab": {"__id__": 796}, "_alignFlags": 9, "_target": null, "_left": 123.94999999999999, "_right": 1017.194, "_top": 177.171, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f00toxLTxLQKz7U2qitMVF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 790}, "_enabled": true, "__prefab": {"__id__": 798}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "10W1CHx8FPY46OwjdZSHUe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 790}, "_enabled": true, "__prefab": {"__id__": 800}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 382.1, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "82aQG/6ApDFYpHovA3shWz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fSb914BZKzL6jgW+KsG65"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 803}, {"__id__": 805}, {"__id__": 807}], "_prefab": {"__id__": 809}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": 59.541, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 804}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "05b73689-54a6-4868-af55-ac49f1a018c1@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3dLg9hOO5NT75VwSUHy2rV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 806}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "dddx1WItxIoKOYx+PWCeIs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 802}, "_enabled": true, "__prefab": {"__id__": 808}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 26, "height": 27}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "93QXWR4MdOFL0Tu0iA2oi3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7EAH+AzxMkbFmj+PszjPz"}, {"__type__": "cc.Node", "_name": "ChangeBtn", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [{"__id__": 811}], "_active": true, "_components": [{"__id__": 821}, {"__id__": 823}, {"__id__": 825}], "_prefab": {"__id__": 827}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 810}, "_children": [], "_active": true, "_components": [{"__id__": 812}, {"__id__": 814}, {"__id__": 816}, {"__id__": 818}], "_prefab": {"__id__": 820}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 813}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "CHẤP NHẬN THAY ĐỔI", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beilByA9BNJY/DxmfKuWCp"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 811}, "_enabled": true, "id": "spr102", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 815}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcX2OuMxBC3KFd3t3WW1Fy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 817}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "38C48wGGhN44SR6MO/BOD4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 811}, "_enabled": true, "__prefab": {"__id__": 819}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 432.64, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceVVrh6xZG8b7wJSvp397h"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2gQKy8QJGiJZwUtZyqwoQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 810}, "_enabled": true, "__prefab": {"__id__": 822}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "069b8bde-4518-474e-9504-701bcc8a8638@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8A2NaCJtImZMajiB2AXQ4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 810}, "_enabled": true, "__prefab": {"__id__": 824}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "78mNbMu5tMX6CTlMWbnzH9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 810}, "_enabled": true, "__prefab": {"__id__": 826}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7ZgSC7UFPOqSZhaHe8p8v"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d030sU5HJFRIpIwoaujDX+"}, {"__type__": "cc.Node", "_name": "BetBtn", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [{"__id__": 829}], "_active": true, "_components": [{"__id__": 839}, {"__id__": 841}, {"__id__": 843}], "_prefab": {"__id__": 845}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -150.693, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 828}, "_children": [], "_active": true, "_components": [{"__id__": 830}, {"__id__": 832}, {"__id__": 834}, {"__id__": 836}], "_prefab": {"__id__": 838}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 831}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "DÙNG VÉ", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9R/Zpe71KVZXjcozeppJM"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 829}, "_enabled": true, "id": "td15", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 833}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7DJvEyZRIEKhktMRc5SvJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 835}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "02VGDz3ElAA6Pnl1tpQuIG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 829}, "_enabled": true, "__prefab": {"__id__": 837}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 182.25, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bfrh4RGBINL1Rv7AdTZgn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9/iuHZdxDMoIzAoOpCJj1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 840}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3d2cc74a-58c8-4ab4-b2d3-38e479ea2995@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baNkLi4GdC47HQmdH1CnmZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 842}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "79nig1+IFAxqN5uDFKNxO5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 828}, "_enabled": true, "__prefab": {"__id__": 844}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "45W0mRGexNW7nSa2uR/82f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3v9HVTmBJ/4Jykx5wk7xt"}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 847}, {"__id__": 849}, {"__id__": 851}, {"__id__": 853}, {"__id__": 855}], "_prefab": {"__id__": 857}, "_lpos": {"__type__": "cc.Vec3", "x": 387.209, "y": 206, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 846}, "_enabled": true, "__prefab": {"__id__": 848}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Tỉ lệ cược", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10LYo6dcBAkba0XBreXE6a"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 846}, "_enabled": true, "id": "spr121", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 850}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4rPRga29OvKMInZVX3Pah"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 846}, "_enabled": true, "__prefab": {"__id__": 852}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 152.361, "_top": 33.139999999999986, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eVO7PR/1PcqbUAq/wRjzb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 846}, "_enabled": true, "__prefab": {"__id__": 854}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "712cc61J9EdJPrwgM50qHD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 846}, "_enabled": true, "__prefab": {"__id__": 856}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 80.86, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dtaWZOYJAKbRm1RXifYd5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6bZzx2TlGyaMQomv65zCU"}, {"__type__": "cc.Node", "_name": "PriceValueLabel", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 859}, {"__id__": 861}, {"__id__": 863}, {"__id__": 865}], "_prefab": {"__id__": 867}, "_lpos": {"__type__": "cc.Vec3", "x": 440.995, "y": 162.053, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 860}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 46, "g": 161, "b": 103, "a": 255}, "_useOriginalSize": true, "_string": " 2 VIW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e14QwOlIFDh5rKHPewfA5i"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 862}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 77.08700000000002, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eeTng4FlAfIepR58oPi96"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 864}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3KccUoiFLkKXKsHVpATjF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 858}, "_enabled": true, "__prefab": {"__id__": 866}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 78.01, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5p2lDroFBh6tORLSohdlT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cfWUkD1UdH34C5IkVAyjzX"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 869}, {"__id__": 871}, {"__id__": 873}, {"__id__": 875}, {"__id__": 877}], "_prefab": {"__id__": 879}, "_lpos": {"__type__": "cc.Vec3", "x": -510, "y": 162.053, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 868}, "_enabled": true, "__prefab": {"__id__": 870}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON>nh giá phi<PERSON>u c<PERSON><PERSON><PERSON> miễn phí", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ek5Rny9REBJXBawJZRmMu"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 868}, "_enabled": true, "id": "spr074", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 872}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5LKce/TFO8ZK+qko6UE+p"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 868}, "_enabled": true, "__prefab": {"__id__": 874}, "_alignFlags": 9, "_target": null, "_left": 70, "_right": 1017.194, "_top": 77.08700000000002, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33Urprbs9JK6eK7eqaAgJE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 868}, "_enabled": true, "__prefab": {"__id__": 876}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bZ57v8fpJnJf4RHE+PVUR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 868}, "_enabled": true, "__prefab": {"__id__": 878}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 352.42, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25SM6C3vtLs7joTQOdFa2R"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6ariaVEM5Gtqk9shSaYxYp"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 881}, {"__id__": 883}, {"__id__": 885}, {"__id__": 887}, {"__id__": 889}], "_prefab": {"__id__": 891}, "_lpos": {"__type__": "cc.Vec3", "x": -435.555, "y": 114.56599999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 882}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON> thể thắng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46sakwSoJPprFH/p48wOvi"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 880}, "_enabled": true, "id": "spr094", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 884}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eYKCeVolE0LIhynGxdlEv"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 886}, "_alignFlags": 9, "_target": null, "_left": 70, "_right": 1017.194, "_top": 124.574, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcGuhb2zJO2bpt6wEHFpdU"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 888}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0UIYVirtJUJ7XIEeTFNnT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 880}, "_enabled": true, "__prefab": {"__id__": 890}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 148.89, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1puBnDVRNh7qJCRD8wT5u"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaj3d53dhA3Lb5/8u3YD0U"}, {"__type__": "cc.Node", "_name": "RateLabel", "_objFlags": 0, "_parent": {"__id__": 779}, "_children": [], "_active": true, "_components": [{"__id__": 893}, {"__id__": 895}, {"__id__": 897}, {"__id__": 899}], "_prefab": {"__id__": 901}, "_lpos": {"__type__": "cc.Vec3", "x": 440.995, "y": 114.56599999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 892}, "_enabled": true, "__prefab": {"__id__": 894}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": " 2 VIW", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bR4i+Bn9Eo4pN+pcp8WMg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 892}, "_enabled": true, "__prefab": {"__id__": 896}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 124.574, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bfNZzBsQNIcoTzYngirT4s"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 892}, "_enabled": true, "__prefab": {"__id__": 898}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ejq/jT1tKEY5h3fwiado3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 892}, "_enabled": true, "__prefab": {"__id__": 900}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 78.01, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0FCtVxmhH46TQrTvRVoX5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feaIC+hf5BlZNm56bWuF6h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 779}, "_enabled": true, "__prefab": {"__id__": 903}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 49, "g": 51, "b": 58, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08fQzvMz1N8a9QcRJLRkqa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 779}, "_enabled": true, "__prefab": {"__id__": 905}, "_alignFlags": 44, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 35, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69Xn3q1b5IqIj+A4SntFM1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 779}, "_enabled": true, "__prefab": {"__id__": 907}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "40R6HBNjVEtIGxVfLcW/KC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 779}, "_enabled": true, "__prefab": {"__id__": 909}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1160, "height": 506}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3AQUCYeRDoYsURQ9bprgH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5Vuhi9DNFd5MrwD1gHJ52"}, {"__type__": "cc.Node", "_name": "Success", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 912}, {"__id__": 920}], "_active": false, "_components": [{"__id__": 930}, {"__id__": 932}, {"__id__": 934}], "_prefab": {"__id__": 936}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 147, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "checked", "_objFlags": 0, "_parent": {"__id__": 911}, "_children": [], "_active": true, "_components": [{"__id__": 913}, {"__id__": 915}, {"__id__": 917}], "_prefab": {"__id__": 919}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 150, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 912}, "_enabled": true, "__prefab": {"__id__": 914}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "39123ff2-d670-4377-b67a-82ab17777128@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dOzNfo6JAT7p7KZz7Z3x/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 912}, "_enabled": true, "__prefab": {"__id__": 916}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0Pd0ZjU1NALBpr7U80q+m"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 912}, "_enabled": true, "__prefab": {"__id__": 918}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 160, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "53AcaZk8JAxIrZUSvSgWxY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49jRqFnIZIr6n9/wBzJnAx"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 911}, "_children": [], "_active": true, "_components": [{"__id__": 921}, {"__id__": 923}, {"__id__": 925}, {"__id__": 927}], "_prefab": {"__id__": 929}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 920}, "_enabled": true, "__prefab": {"__id__": 922}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> nhận đặt cược", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 58, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 58, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9f9i/M079DAYI0EPE7R/a3"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 920}, "_enabled": true, "id": "spr064", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 924}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ecPhigNZEKZQwnm2B98ej"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 920}, "_enabled": true, "__prefab": {"__id__": 926}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cgejcn9pHQJ23Edkovw5C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 920}, "_enabled": true, "__prefab": {"__id__": 928}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 531.52, "height": 73.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fCMVLBf9KpLbuEaO/xU/4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d45WGcq45NtZvRFn7qy9jq"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 931}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 400, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eacR+LT2RJGITSjuexk83F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 933}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9nNNORQxGybGNHume+YZw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 911}, "_enabled": true, "__prefab": {"__id__": 935}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3GIDoj+pG1YBH+PvGxqbi"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98yPqxUrxEh5KBIzSm/ef1"}, {"__type__": "cc.Node", "_name": "DeleteBtn", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 938}], "_active": true, "_components": [{"__id__": 948}, {"__id__": 950}, {"__id__": 952}, {"__id__": 955}, {"__id__": 957}], "_prefab": {"__id__": 959}, "_lpos": {"__type__": "cc.Vec3", "x": -29.95699999999988, "y": 511.374, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 937}, "_children": [], "_active": true, "_components": [{"__id__": 939}, {"__id__": 941}, {"__id__": 943}, {"__id__": 945}], "_prefab": {"__id__": 947}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 938}, "_enabled": true, "__prefab": {"__id__": 940}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> tất cả", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 4, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57Tf2D3j1DuYPtX9vEfU0Z"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 938}, "_enabled": true, "id": "spr093", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 942}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aq1bmS/9OhZtb+PUDqfyr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 938}, "_enabled": true, "__prefab": {"__id__": 944}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "98oBUfGcpJuIN5BB63owvi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 938}, "_enabled": true, "__prefab": {"__id__": 946}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 126.08, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "33KLV09d1C1YJjkOmYs7R6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9cbk3QxZFBb7UV6Y65xAMP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 937}, "_enabled": true, "__prefab": {"__id__": 949}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0JSC7j1xPPIJcJsqHHz/m"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 937}, "_enabled": true, "__prefab": {"__id__": 951}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 964.9569999999999, "_top": 251.12599999999998, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52iDewvxhGT4Dc9gEE7uS5"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 937}, "_enabled": true, "__prefab": {"__id__": 953}, "clickEvents": [{"__id__": 954}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fm2X9GEdMr4K48YT1OpZB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "removeAll", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 937}, "_enabled": true, "__prefab": {"__id__": 956}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1nvT64xZGh4yqIgkCm4++"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 937}, "_enabled": true, "__prefab": {"__id__": 958}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebvyu7e1BM1IoQmmMFfbWo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfie1qQxFC2rsubc4eSKUc"}, {"__type__": "cc.Node", "_name": "OddSelect", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 961}, {"__id__": 971}, {"__id__": 983}], "_active": false, "_components": [{"__id__": 1100}, {"__id__": 1102}, {"__id__": 1104}, {"__id__": 1106}], "_prefab": {"__id__": 1108}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -75, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "_parent": {"__id__": 960}, "_children": [], "_active": true, "_components": [{"__id__": 962}, {"__id__": 964}, {"__id__": 966}, {"__id__": 968}], "_prefab": {"__id__": 970}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 961}, "_enabled": true, "__prefab": {"__id__": 963}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 38, "g": 39, "b": 43, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02tnQLsIVNSZuz5748YANc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 961}, "_enabled": true, "__prefab": {"__id__": 965}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 128, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1oc3uoJFKpKqDYvIzfKvP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 961}, "_enabled": true, "__prefab": {"__id__": 967}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "3ePHeQwMhAObLioAneUlKS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 961}, "_enabled": true, "__prefab": {"__id__": 969}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1180, "height": 1610}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dIuNiXJJFcKcM+rxmbcit"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcxY+sfqZAaLjTCbTCn36H"}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 960}, "_children": [], "_active": true, "_components": [{"__id__": 972}, {"__id__": 974}, {"__id__": 976}, {"__id__": 978}, {"__id__": 980}], "_prefab": {"__id__": 982}, "_lpos": {"__type__": "cc.Vec3", "x": -500, "y": 749.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 971}, "_enabled": true, "__prefab": {"__id__": 973}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON>hi thay đổi giá:", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76fV3u3RNIZrI9ylwazkSq"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 971}, "_enabled": true, "id": "spr100", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 975}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9qmmCeP1MlZ0/uz4n38UI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 971}, "_enabled": true, "__prefab": {"__id__": 977}, "_alignFlags": 9, "_target": null, "_left": 90, "_right": 0, "_top": 30, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62RJDjxXlGwLi1NugPSyL2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 971}, "_enabled": true, "__prefab": {"__id__": 979}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "02ea10emNG5LoJ/F1ybUVl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 971}, "_enabled": true, "__prefab": {"__id__": 981}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 284.63, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfteiqjQhNNqeNcHg5RFqB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3ZtBleHdJdI7MXFqCTv3A"}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "_parent": {"__id__": 960}, "_children": [{"__id__": 984}, {"__id__": 1019}, {"__id__": 1054}], "_active": true, "_components": [{"__id__": 1089}, {"__id__": 1091}, {"__id__": 1093}, {"__id__": 1095}, {"__id__": 1097}], "_prefab": {"__id__": 1099}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 705, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Odd1", "_objFlags": 0, "_parent": {"__id__": 983}, "_children": [{"__id__": 985}, {"__id__": 993}, {"__id__": 1001}], "_active": true, "_components": [{"__id__": 1011}, {"__id__": 1014}, {"__id__": 1016}], "_prefab": {"__id__": 1018}, "_lpos": {"__type__": "cc.Vec3", "x": -475, "y": -25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 984}, "_children": [], "_active": true, "_components": [{"__id__": 986}, {"__id__": 988}, {"__id__": 990}], "_prefab": {"__id__": 992}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 987}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "471f34b5-134b-400a-9494-cf6046e22fda@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27kw0sW5RNyoSr4RcVbv5Q"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 989}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdpyRXSuNCo5PCsWHTtAYe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 985}, "_enabled": true, "__prefab": {"__id__": 991}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3f8OxxqpMibwH8BsF1kDr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3aYCvkTetAAKC9e+43a4PY"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 984}, "_children": [], "_active": true, "_components": [{"__id__": 994}, {"__id__": 996}, {"__id__": 998}], "_prefab": {"__id__": 1000}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 993}, "_enabled": true, "__prefab": {"__id__": 995}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c8e93454-492a-48c1-8fce-580d35cdf678@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20HUs9MaZPWZx2EEP+SNc+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 993}, "_enabled": true, "__prefab": {"__id__": 997}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cSppjcR1E2ZW5VdJFs9u4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 993}, "_enabled": true, "__prefab": {"__id__": 999}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "98ytnql2RMvJFM8pPkhP6F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cPbVX4SFF65auSQtIHFtn"}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 984}, "_children": [], "_active": true, "_components": [{"__id__": 1002}, {"__id__": 1004}, {"__id__": 1006}, {"__id__": 1008}], "_prefab": {"__id__": 1010}, "_lpos": {"__type__": "cc.Vec3", "x": 45, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1001}, "_enabled": true, "__prefab": {"__id__": 1003}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> nhận tỷ lệ cược cao hơn", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "670l9vlahLI4tZk2mIXIaZ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1001}, "_enabled": true, "id": "spr097", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1005}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dA6sz77hPcLqWemBvDT1k"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1001}, "_enabled": true, "__prefab": {"__id__": 1007}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5GuuYaw1P8YtpQnxip3vA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1001}, "_enabled": true, "__prefab": {"__id__": 1009}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 474.04, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "39MfRbRNtB7pozQqT6p+Xw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbzeG3QuhOaKZxSZLA+ZBC"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 984}, "_enabled": true, "__prefab": {"__id__": 1012}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 985}, "checkEvents": [{"__id__": 1013}], "_isChecked": true, "_checkMark": {"__id__": 994}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cBx8ogURNvZPPQa1tcFa2"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "onClickOdd0", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 984}, "_enabled": true, "__prefab": {"__id__": 1015}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aq6Jfsg1HD6C8+ghdmZkw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 984}, "_enabled": true, "__prefab": {"__id__": 1017}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "46+PIvqW1CO4U8lkW7ZqGG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "67CnrGb89KtYWs8zjkDCpK"}, {"__type__": "cc.Node", "_name": "Odd2", "_objFlags": 0, "_parent": {"__id__": 983}, "_children": [{"__id__": 1020}, {"__id__": 1028}, {"__id__": 1036}], "_active": true, "_components": [{"__id__": 1046}, {"__id__": 1049}, {"__id__": 1051}], "_prefab": {"__id__": 1053}, "_lpos": {"__type__": "cc.Vec3", "x": -475, "y": -100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 1019}, "_children": [], "_active": true, "_components": [{"__id__": 1021}, {"__id__": 1023}, {"__id__": 1025}], "_prefab": {"__id__": 1027}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1020}, "_enabled": true, "__prefab": {"__id__": 1022}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "471f34b5-134b-400a-9494-cf6046e22fda@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01N9Q6Li1Hl7foSt9F4FJ2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1020}, "_enabled": true, "__prefab": {"__id__": 1024}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef851wLudCYYJ3cfDJUKDq"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1020}, "_enabled": true, "__prefab": {"__id__": 1026}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9sWYiVBFHQ5tom7E4w57j"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dBJ1yX9lGoLQjdoOEQCO9"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 1019}, "_children": [], "_active": false, "_components": [{"__id__": 1029}, {"__id__": 1031}, {"__id__": 1033}], "_prefab": {"__id__": 1035}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1028}, "_enabled": true, "__prefab": {"__id__": 1030}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c8e93454-492a-48c1-8fce-580d35cdf678@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bftem07MNP4bYmuJwDNmkB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1028}, "_enabled": true, "__prefab": {"__id__": 1032}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "adFqo/OVZIf7ZuiJS29O8r"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1028}, "_enabled": true, "__prefab": {"__id__": 1034}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "beNq6zvkZMjZSztWR/JJNp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37jW+Lm+pBXZ89IotuKMeq"}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 1019}, "_children": [], "_active": true, "_components": [{"__id__": 1037}, {"__id__": 1039}, {"__id__": 1041}, {"__id__": 1043}], "_prefab": {"__id__": 1045}, "_lpos": {"__type__": "cc.Vec3", "x": 45, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1036}, "_enabled": true, "__prefab": {"__id__": 1038}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Luôn hỏi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "532FGOZzBP9apbx5xK0Gh0"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1036}, "_enabled": true, "id": "spr098", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1040}}, {"__type__": "cc.CompPrefabInfo", "fileId": "98sz7No+xKjqUKLYQ9TM7Y"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1036}, "_enabled": true, "__prefab": {"__id__": 1042}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "25Qp6inm1N2qvT6r7mIq5o"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1036}, "_enabled": true, "__prefab": {"__id__": 1044}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 134.29, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "329CN0WLBOXYl8N6Tp7KzW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bBmCCyuJAs4FfcI/IjtbE"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 1019}, "_enabled": true, "__prefab": {"__id__": 1047}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 1020}, "checkEvents": [{"__id__": 1048}], "_isChecked": false, "_checkMark": {"__id__": 1029}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daILbfQOZIDayIF4hgiUzN"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "onClickOdd1", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1019}, "_enabled": true, "__prefab": {"__id__": 1050}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6V2izOpNP2rNsqUlIVm8k"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1019}, "_enabled": true, "__prefab": {"__id__": 1052}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cHxRt0LJIZpr8Dm2N5/00"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "17osKMyGxP6KgluNMTv5JK"}, {"__type__": "cc.Node", "_name": "Odd3", "_objFlags": 0, "_parent": {"__id__": 983}, "_children": [{"__id__": 1055}, {"__id__": 1063}, {"__id__": 1071}], "_active": true, "_components": [{"__id__": 1081}, {"__id__": 1084}, {"__id__": 1086}], "_prefab": {"__id__": 1088}, "_lpos": {"__type__": "cc.Vec3", "x": -475, "y": -175, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 1054}, "_children": [], "_active": true, "_components": [{"__id__": 1056}, {"__id__": 1058}, {"__id__": 1060}], "_prefab": {"__id__": 1062}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1057}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "471f34b5-134b-400a-9494-cf6046e22fda@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76O2eM/x9KPIG1nzBNnt2M"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1059}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8vbCyU2FDZ5LqpZxOy1p3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1055}, "_enabled": true, "__prefab": {"__id__": 1061}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aJ3XjdMpGoK1X7hnHp5Dg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56AvS81oBEFpVUaL0hxQq0"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 1054}, "_children": [], "_active": false, "_components": [{"__id__": 1064}, {"__id__": 1066}, {"__id__": 1068}], "_prefab": {"__id__": 1070}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1063}, "_enabled": true, "__prefab": {"__id__": 1065}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "c8e93454-492a-48c1-8fce-580d35cdf678@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66liWzgyNE3YBf7MxLjjOX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1063}, "_enabled": true, "__prefab": {"__id__": 1067}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f79tqD+3VJVa2iNCDk0bsb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1063}, "_enabled": true, "__prefab": {"__id__": 1069}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 55, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "28Mnr2mIdO1LNdfJFKVIvg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5ssAtDLVGII8gaxHP8hOw"}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 1054}, "_children": [], "_active": true, "_components": [{"__id__": 1072}, {"__id__": 1074}, {"__id__": 1076}, {"__id__": 1078}], "_prefab": {"__id__": 1080}, "_lpos": {"__type__": "cc.Vec3", "x": 45, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1071}, "_enabled": true, "__prefab": {"__id__": 1073}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> nhận bất kỳ tỉ lệ cược nào", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eePovNB5Cb4Xdhb0b3wOZ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1071}, "_enabled": true, "id": "spr099", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1075}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8qVUHnChFU6gbhLvZ68/B"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1071}, "_enabled": true, "__prefab": {"__id__": 1077}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "681/jIlItFiYIpaghhIidp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1071}, "_enabled": true, "__prefab": {"__id__": 1079}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 497.78, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6+ZWORK9BErMJ6rjSXcKv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02BMZudLhPD7jyK1L2pzee"}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 1054}, "_enabled": true, "__prefab": {"__id__": 1082}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 1055}, "checkEvents": [{"__id__": 1083}], "_isChecked": false, "_checkMark": {"__id__": 1064}, "_N$enableAutoGrayEffect": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "275ubk3G5LTKGh9mekLD8g"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "onClickOdd2", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1054}, "_enabled": true, "__prefab": {"__id__": 1085}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbxGItc1lKDoshJoW92lYc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1054}, "_enabled": true, "__prefab": {"__id__": 1087}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dbOHAeyBFhqN5qV9W2TTA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9hFNoN8hDjLrFxLOaGRC8"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 983}, "_enabled": true, "__prefab": {"__id__": 1090}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 25, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1080, "height": 200}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "525v6JjONP+6wPJ8H4cikQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 983}, "_enabled": true, "__prefab": {"__id__": 1092}, "_alignFlags": 41, "_target": null, "_left": 50, "_right": 50, "_top": 100, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9nRsuqmRAy73esEuUG/J3"}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "node": {"__id__": 983}, "_enabled": true, "__prefab": {"__id__": 1094}, "_allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c30hb0gmVHwbEnQZjmyXXB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 983}, "_enabled": true, "__prefab": {"__id__": 1096}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8ajf0LuoNERZWpsTeCHHq5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 983}, "_enabled": true, "__prefab": {"__id__": 1098}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "17QrBqR/lO8a8z16mG8hHj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62u+Kb5tlIa7gvr19BEg/Y"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 960}, "_enabled": true, "__prefab": {"__id__": 1101}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 150, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "72kh9o9PlKHLIfO6E2XF6w"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 960}, "_enabled": true, "__prefab": {"__id__": 1103}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7awnaDV29LLIZ7RMrelIBO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 960}, "_enabled": true, "__prefab": {"__id__": 1105}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "90DREk5jtOmLViAn5eiPen"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 960}, "_enabled": true, "__prefab": {"__id__": 1107}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1180, "height": 1610}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4a8fuhPttAt4kaz81acNlU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dc9t0lBItD86QQ41GBs+gn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 1110}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "140b298d-a035-49a0-8226-a163e622cd48@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6pKZraDRL54TndtEkA/cp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 1112}, "_alignFlags": 44, "_target": null, "_left": -50, "_right": -50, "_top": 365.75, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 900, "_originalHeight": 1594, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8ujY2NRdHE7vEtLE/WRlb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 1114}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "034lZyLTBBQJhY9BEcWqe7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 1116}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 1594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96OpHy6dpCm7Jj1kdZ3SCa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a9R3NI5sFA/6W51+jJIYP9"}, {"__type__": "cc.Node", "_name": "QuickBet", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 1119}, {"__id__": 1129}, {"__id__": 1164}, {"__id__": 1176}, {"__id__": 1196}, {"__id__": 1219}], "_active": true, "_components": [{"__id__": 1356}, {"__id__": 1358}, {"__id__": 1360}, {"__id__": 1362}], "_prefab": {"__id__": 1364}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1354, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Line", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [], "_active": true, "_components": [{"__id__": 1120}, {"__id__": 1122}, {"__id__": 1124}, {"__id__": 1126}], "_prefab": {"__id__": 1128}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 285.06899999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1119}, "_enabled": true, "__prefab": {"__id__": 1121}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35Osoz9D5LDYoIqBbaj5IQ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1119}, "_enabled": true, "__prefab": {"__id__": 1123}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 154.43100000000004, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35V0th1JZMnYxPPwgx5s2H"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1119}, "_enabled": true, "__prefab": {"__id__": 1125}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9mvAcLP5ELLhPFB4S/cNA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1119}, "_enabled": true, "__prefab": {"__id__": 1127}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfpI4KXpJPEqzp7E0C23Yb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3ejvZ+oGFIcoTVETdTzEcz"}, {"__type__": "cc.Node", "_name": "Toggle", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [{"__id__": 1130}, {"__id__": 1146}], "_active": true, "_components": [{"__id__": 1154}, {"__id__": 1156}, {"__id__": 1159}, {"__id__": 1161}], "_prefab": {"__id__": 1163}, "_lpos": {"__type__": "cc.Vec3", "x": -886, "y": 345, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 1129}, "_children": [{"__id__": 1131}], "_active": true, "_components": [{"__id__": 1139}, {"__id__": 1141}, {"__id__": 1143}], "_prefab": {"__id__": 1145}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Ellipse 3", "_objFlags": 0, "_parent": {"__id__": 1130}, "_children": [], "_active": true, "_components": [{"__id__": 1132}, {"__id__": 1134}, {"__id__": 1136}], "_prefab": {"__id__": 1138}, "_lpos": {"__type__": "cc.Vec3", "x": -22, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1133}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "06023c31-d698-41a1-abdb-ebec9fd4cb5f@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18xqGL6jZGgbbzGDv3nfA0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1135}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8ZkRLoWJMEL6twyCJqn4z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1131}, "_enabled": true, "__prefab": {"__id__": 1137}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 50, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "37ySBJP/dMUJ6taHPAPlp3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcEHtK8eVMcp/Wf4CkRxzp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1130}, "_enabled": true, "__prefab": {"__id__": 1140}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b3076a55-499c-4a71-a4c0-60e735bc5fc9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eLex3KUBCBIT7eljkxHdw"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1130}, "_enabled": true, "__prefab": {"__id__": 1142}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "22xQLAqC5IxJrRnwCB4eXp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1130}, "_enabled": true, "__prefab": {"__id__": 1144}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "86lHOhynhCoblYPyuNJKHS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "25lUhBwEVG06UR3NyiDOM4"}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 1129}, "_children": [], "_active": true, "_components": [{"__id__": 1147}, {"__id__": 1149}, {"__id__": 1151}], "_prefab": {"__id__": 1153}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1146}, "_enabled": true, "__prefab": {"__id__": 1148}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2d952bd0-7d56-4a9f-b3a0-8ff4903747cf@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5yzncUThHaKXP933BrA2/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1146}, "_enabled": true, "__prefab": {"__id__": 1150}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "30QNvDdUtDMKb0VeMGR0aZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1146}, "_enabled": true, "__prefab": {"__id__": 1152}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fL3sJlMFJO6KWOT+8mHYp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e9RdeQ4sJE3IPd/Z8r54o2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1129}, "_enabled": true, "__prefab": {"__id__": 1155}, "_alignFlags": 9, "_target": null, "_left": 76, "_right": 0, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdmnB0AxtMD7auOWgNQhLP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1129}, "_enabled": true, "__prefab": {"__id__": 1157}, "clickEvents": [{"__id__": 1158}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fm8/9wpdK3Z0PaVjl7fk8"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "setNormal", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1129}, "_enabled": true, "__prefab": {"__id__": 1160}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8bffsE+NIzb941sKQArtX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1129}, "_enabled": true, "__prefab": {"__id__": 1162}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3e1hknlNLJLdzTO9fFW/2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beA/39IIhHqZhcCjnWzoHx"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [], "_active": true, "_components": [{"__id__": 1165}, {"__id__": 1167}, {"__id__": 1169}, {"__id__": 1171}, {"__id__": 1173}], "_prefab": {"__id__": 1175}, "_lpos": {"__type__": "cc.Vec3", "x": -737.125, "y": 347.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1164}, "_enabled": true, "__prefab": {"__id__": 1166}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5U+Ev5a5BeJnsIbkArg9T"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1164}, "_enabled": true, "id": "spr139", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1168}}, {"__type__": "cc.CompPrefabInfo", "fileId": "18nu1wVwpPtYelTcAY9vsg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1164}, "_enabled": true, "__prefab": {"__id__": 1170}, "_alignFlags": 9, "_target": null, "_left": 200, "_right": 0, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8d/lvb6FZDCJJns0rd39js"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1164}, "_enabled": true, "__prefab": {"__id__": 1172}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "49wktnQWdIcZ9Qcmi4wxqd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1164}, "_enabled": true, "__prefab": {"__id__": 1174}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 145.75, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "12fIKxpShJtK28m2JBd4m6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "09Us2S+LRGoKl8fkz+AIm/"}, {"__type__": "cc.Node", "_name": "SettingBtn", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [{"__id__": 1177}], "_active": true, "_components": [{"__id__": 1185}, {"__id__": 1187}, {"__id__": 1189}, {"__id__": 1191}, {"__id__": 1193}], "_prefab": {"__id__": 1195}, "_lpos": {"__type__": "cc.Vec3", "x": 795.5, "y": 338.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 1176}, "_children": [], "_active": true, "_components": [{"__id__": 1178}, {"__id__": 1180}, {"__id__": 1182}], "_prefab": {"__id__": 1184}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1177}, "_enabled": true, "__prefab": {"__id__": 1179}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "303c7b44-9b46-41a6-854c-ffa2e8ae5798@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcV/GRV4tG+a/Fn9f6o2OG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1177}, "_enabled": true, "__prefab": {"__id__": 1181}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7WA3danBCW7dj1XDEvkhm"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1177}, "_enabled": true, "__prefab": {"__id__": 1183}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 42, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aCNC/GWtKsr6UxmSDcTVM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d7gXvW9ORDWI3wvmPM6fkT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1176}, "_enabled": true, "__prefab": {"__id__": 1186}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b03e6462-965c-4ba2-8f2a-9d84f2915da9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78N8fZ2rhMM5yJXCluNbea"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1176}, "_enabled": true, "__prefab": {"__id__": 1188}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 180, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c67RoWlCdDXqZ5EBAFBR6w"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1176}, "_enabled": true, "__prefab": {"__id__": 1190}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efctmcZSdINIvNa+NAp5yY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1176}, "_enabled": true, "__prefab": {"__id__": 1192}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "37tuTMl4dLAIT8Ou0qfejv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1176}, "_enabled": true, "__prefab": {"__id__": 1194}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7qMVxcVlEnpPaqXuEjmzw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4a+m9a6dCMa+mnIXq6aWR"}, {"__type__": "cc.Node", "_name": "CloseBtn", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [{"__id__": 1197}], "_active": true, "_components": [{"__id__": 1207}, {"__id__": 1210}, {"__id__": 1212}, {"__id__": 1214}, {"__id__": 1216}], "_prefab": {"__id__": 1218}, "_lpos": {"__type__": "cc.Vec3", "x": 895.5, "y": 338.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 1196}, "_children": [], "_active": true, "_components": [{"__id__": 1198}, {"__id__": 1200}, {"__id__": 1202}, {"__id__": 1204}], "_prefab": {"__id__": 1206}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1197}, "_enabled": true, "__prefab": {"__id__": 1199}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "efe93bdd-59ae-4d01-be41-cabbf88ac4b2@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c94tkA6hJIcIEIEIuXb9Sl"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1197}, "_enabled": true, "__prefab": {"__id__": 1201}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaKJCgt3JE8qblo/Nx1LZ/"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1197}, "_enabled": true, "__prefab": {"__id__": 1203}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebQWUiZu9FEbtmjkUZT+8D"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1197}, "_enabled": true, "__prefab": {"__id__": 1205}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 36, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "afU55sWhtPIr/7qbndR4e+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1eEnQzQOlJRJZNPHH7Ykbt"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1196}, "_enabled": true, "__prefab": {"__id__": 1208}, "clickEvents": [{"__id__": 1209}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c28hPp/qVCp5HzoDtc3/Hn"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "deactivePage", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1196}, "_enabled": true, "__prefab": {"__id__": 1211}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b03e6462-965c-4ba2-8f2a-9d84f2915da9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76sIPDuaBHJqGHeLqy4yTc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1196}, "_enabled": true, "__prefab": {"__id__": 1213}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 80, "_top": 67, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98njYxhQlCjpHlCNPVbaqY"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1196}, "_enabled": true, "__prefab": {"__id__": 1215}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7q39ulc9Pr79mlo2GIRB+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1196}, "_enabled": true, "__prefab": {"__id__": 1217}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dJRYshAFCCaDpXKaVXV5s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfyzY/JRtBDpzemv6QqSeq"}, {"__type__": "cc.Node", "_name": "InputMoneyForm", "_objFlags": 0, "_parent": {"__id__": 1118}, "_children": [{"__id__": 1220}, {"__id__": 1242}, {"__id__": 1284}, {"__id__": 1296}, {"__id__": 1304}, {"__id__": 1325}], "_active": true, "_components": [{"__id__": 1349}, {"__id__": 1351}, {"__id__": 1353}], "_prefab": {"__id__": 1355}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 195.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "MaxBtn", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [{"__id__": 1221}], "_active": true, "_components": [{"__id__": 1231}, {"__id__": 1233}, {"__id__": 1235}, {"__id__": 1237}, {"__id__": 1239}], "_prefab": {"__id__": 1241}, "_lpos": {"__type__": "cc.Vec3", "x": 821.5, "y": 40.193, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 1220}, "_children": [], "_active": true, "_components": [{"__id__": 1222}, {"__id__": 1224}, {"__id__": 1226}, {"__id__": 1228}], "_prefab": {"__id__": 1230}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1221}, "_enabled": true, "__prefab": {"__id__": 1223}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 24, "g": 203, "b": 174, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bQu5P7hlHRoHt4/GeOE9G"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1221}, "_enabled": true, "id": "se25", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1225}}, {"__type__": "cc.CompPrefabInfo", "fileId": "baky73zvhEq7UE1T+EXn0e"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1221}, "_enabled": true, "__prefab": {"__id__": 1227}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "43Ib4XM0FGA4ujrvi1yReG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1221}, "_enabled": true, "__prefab": {"__id__": 1229}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 46.33, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "67eJdmfd9GebekOG59oQkC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5biHHS76NLU4J/AdDKg2xd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1220}, "_enabled": true, "__prefab": {"__id__": 1232}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "97b075fc-007c-4f42-9871-70e885a98c24@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ed+qZFK9lLRJjORLmNRLoF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1220}, "_enabled": true, "__prefab": {"__id__": 1234}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24K53OllxNHqjvGmRZ3g9U"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1220}, "_enabled": true, "__prefab": {"__id__": 1236}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 100, "_top": 111.307, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94pS3sy2FIMqfs47vvIbKx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1220}, "_enabled": true, "__prefab": {"__id__": 1238}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "f3fSvatsZLsIpMPV+k0AKD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1220}, "_enabled": true, "__prefab": {"__id__": 1240}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 177, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "537aGEilxHXoPs30/SR+ja"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfTa2xKLNDap+mkDt7umYO"}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [{"__id__": 1243}, {"__id__": 1253}, {"__id__": 1263}], "_active": true, "_components": [{"__id__": 1275}, {"__id__": 1277}, {"__id__": 1279}, {"__id__": 1281}], "_prefab": {"__id__": 1283}, "_lpos": {"__type__": "cc.Vec3", "x": -668, "y": 38.693, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 1242}, "_children": [], "_active": true, "_components": [{"__id__": 1244}, {"__id__": 1246}, {"__id__": 1248}, {"__id__": 1250}], "_prefab": {"__id__": 1252}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1243}, "_enabled": true, "__prefab": {"__id__": 1245}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd2d9381-50e7-4fc4-a085-14bc5fc773a9@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b11g8bq8dLLJdM+F69yxlI"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1243}, "_enabled": true, "__prefab": {"__id__": 1247}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -13, "_bottom": -13, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39dcC5IWJLV7oYfJDfMH3R"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1243}, "_enabled": true, "__prefab": {"__id__": 1249}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "03cu1ksdVLBZtPX3mzFW9x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1243}, "_enabled": true, "__prefab": {"__id__": 1251}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee0O4qtf5AZJdhml9olZwB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76lajrnu1HPrmrrYQwLUvx"}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 1242}, "_children": [], "_active": false, "_components": [{"__id__": 1254}, {"__id__": 1256}, {"__id__": 1258}, {"__id__": 1260}], "_prefab": {"__id__": 1262}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1253}, "_enabled": true, "__prefab": {"__id__": 1255}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8n53j2S5GtJiVwCHuDzrK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1253}, "_enabled": true, "__prefab": {"__id__": 1257}, "_alignFlags": 45, "_target": null, "_left": 10, "_right": -8, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eaIQvzIxM25AJyQuNryld"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1253}, "_enabled": true, "__prefab": {"__id__": 1259}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "07VGGnGw5C4LNYSPjvp3ff"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1253}, "_enabled": true, "__prefab": {"__id__": 1261}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 548, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "381EX0nLZLKq0Xi2moByZ1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86Q4FSaXlKcoooQMKs31Rj"}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 1242}, "_children": [], "_active": true, "_components": [{"__id__": 1264}, {"__id__": 1266}, {"__id__": 1268}, {"__id__": 1270}, {"__id__": 1272}], "_prefab": {"__id__": 1274}, "_lpos": {"__type__": "cc.Vec3", "x": -265, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1263}, "_enabled": true, "__prefab": {"__id__": 1265}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 25, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceSda9rUhEvq9gwP9p4bSq"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1263}, "_enabled": true, "id": "xs12", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1267}}, {"__type__": "cc.CompPrefabInfo", "fileId": "565ai2AP1P7rDehCHxppUK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1263}, "_enabled": true, "__prefab": {"__id__": 1269}, "_alignFlags": 45, "_target": null, "_left": 10, "_right": 90, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "29GI6EW1xIMqKNyq8cWKwH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1263}, "_enabled": true, "__prefab": {"__id__": 1271}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "00CtwgDZdBFo9ssUEk7Ay+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1263}, "_enabled": true, "__prefab": {"__id__": 1273}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f1pJxpGNE9KaQGWrYc9Wk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "39om4U0RhP8I1jRoHU1+rL"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 1242}, "_enabled": true, "__prefab": {"__id__": 1276}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 1254}, "_placeholderLabel": {"__id__": 1264}, "_returnType": 0, "_useOriginalSize": true, "_string": "", "_tabIndex": 0, "_backgroundImage": null, "_inputFlag": 5, "_inputMode": 2, "_maxLength": 20, "returnType": 0, "maxLength": 12, "_background": {"__id__": 1244}, "_stayOnTop": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10qj7RZ/9NFY6Toy43Qovf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1242}, "_enabled": true, "__prefab": {"__id__": 1278}, "_alignFlags": 9, "_target": null, "_left": 67, "_right": 0, "_top": 125.807, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69AXc6RYdBPbOGNZyhPioH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1242}, "_enabled": true, "__prefab": {"__id__": 1280}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "8aCK7HskdAN5xR3ZK1Rz4Z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1242}, "_enabled": true, "__prefab": {"__id__": 1282}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 550, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0C1zdqApGKaqJ2l2xsYGR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dDhq7oG5JhbAVwCCHLEqD"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [], "_active": true, "_components": [{"__id__": 1285}, {"__id__": 1287}, {"__id__": 1289}, {"__id__": 1291}, {"__id__": 1293}], "_prefab": {"__id__": 1295}, "_lpos": {"__type__": "cc.Vec3", "x": -885, "y": -29.07199999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1284}, "_enabled": true, "__prefab": {"__id__": 1286}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 131, "g": 140, "b": 165, "a": 255}, "_useOriginalSize": true, "_string": "Tổng tiền cược nằm ngoài phạm vi cho phép là từ 1,000 - 20,000,000,000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a42TbUhs5JHqnuqCQbVH/S"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1284}, "_enabled": true, "id": "spr142", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1288}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cuvM4u99DlqTvXhjc4vNK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1284}, "_enabled": true, "__prefab": {"__id__": 1290}, "_alignFlags": 9, "_target": null, "_left": 125, "_right": 1017.194, "_top": 199.712, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 66.01, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75mFov0GJC/pbHstipWSu4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1284}, "_enabled": true, "__prefab": {"__id__": 1292}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dfN6MSHlCUamdR589PTWR"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1284}, "_enabled": true, "__prefab": {"__id__": 1294}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 590.28, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bMEb8sKJDXoMn+gANXEJx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaPWVdXCBJba+gd04Sctmo"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [], "_active": true, "_components": [{"__id__": 1297}, {"__id__": 1299}, {"__id__": 1301}], "_prefab": {"__id__": 1303}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": -28, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1296}, "_enabled": true, "__prefab": {"__id__": 1298}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "20a90d18-4122-4a86-8389-5f6d9e4b69cb@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cMfm3tNVINKh+5+THeH7F"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1296}, "_enabled": true, "__prefab": {"__id__": 1300}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bkJmB4WdIhquA6SjW1E9v"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1296}, "_enabled": true, "__prefab": {"__id__": 1302}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "172x9SF39P5omjQWxhzMQZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56uFMEuo9MIJkoV68HLZnD"}, {"__type__": "cc.Node", "_name": "BetBtn", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [{"__id__": 1305}], "_active": true, "_components": [{"__id__": 1315}, {"__id__": 1317}, {"__id__": 1320}, {"__id__": 1322}], "_prefab": {"__id__": 1324}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -145, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 1304}, "_children": [], "_active": true, "_components": [{"__id__": 1306}, {"__id__": 1308}, {"__id__": 1310}, {"__id__": 1312}], "_prefab": {"__id__": 1314}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1305}, "_enabled": true, "__prefab": {"__id__": 1307}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "ĐẶT CƯỢC", "_horizontalAlign": 0, "_verticalAlign": 0, "_actualFontSize": 0, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa6XGX3F1Le5rbu8XVlzAx"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1305}, "_enabled": true, "id": "spr096", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1309}}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6mIXCfwJHv5CvP3BnN2YB"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1305}, "_enabled": true, "__prefab": {"__id__": 1311}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "94VExiNspOOLMpXuasD4MZ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1305}, "_enabled": true, "__prefab": {"__id__": 1313}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 217.34, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8jzQJDRxF17KqHpUxs4GZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4nk0fxIxGBb3CRs3RClrr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1304}, "_enabled": true, "__prefab": {"__id__": 1316}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "069b8bde-4518-474e-9504-701bcc8a8638@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "000+nJWsVHuYKNg9MTtu9O"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1304}, "_enabled": true, "__prefab": {"__id__": 1318}, "clickEvents": [{"__id__": 1319}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "991A3d/QxNGqTgtkeipO+i"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "dcf91+yywZGtbxy7zKD7T78", "handler": "doBet", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1304}, "_enabled": true, "__prefab": {"__id__": 1321}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "50Q4V71A1PHZqDeSeewJar"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1304}, "_enabled": true, "__prefab": {"__id__": 1323}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1025, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dpx8JYRJFj5XJ2VuTS+KM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "530Sfr0B1Dvb64CpcsqcXI"}, {"__type__": "cc.Node", "_name": "SuccessQuickBet", "_objFlags": 0, "_parent": {"__id__": 1219}, "_children": [{"__id__": 1326}, {"__id__": 1336}], "_active": true, "_components": [{"__id__": 1344}, {"__id__": 1346}], "_prefab": {"__id__": 1348}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -72, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "QuickLabel", "_objFlags": 0, "_parent": {"__id__": 1325}, "_children": [], "_active": true, "_components": [{"__id__": 1327}, {"__id__": 1329}, {"__id__": 1331}, {"__id__": 1333}], "_prefab": {"__id__": 1335}, "_lpos": {"__type__": "cc.Vec3", "x": -466, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 1326}, "_enabled": true, "__prefab": {"__id__": 1328}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 247, "g": 247, "b": 247, "a": 255}, "_useOriginalSize": true, "_string": "Tổng tiền cược nằm ngoài phạm vi cho phép là từ 1,000 - 20,000,000,000", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 18, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 22, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3QQ99jQpBtYgERu4RmxkW"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 1326}, "_enabled": true, "id": "spr064", "isUpperCase": false, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": "", "__prefab": {"__id__": 1330}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d8127tA65CvL5qFsj5QU80"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1326}, "_enabled": true, "__prefab": {"__id__": 1332}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "39giYauyxKEYq/yo6urj76"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1326}, "_enabled": true, "__prefab": {"__id__": 1334}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 590.28, "height": 27.72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "51H3p9cyNN2rPqpWHS0Dkt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5A49H9v1NEY/O5GxniqMg"}, {"__type__": "cc.Node", "_name": "", "_objFlags": 0, "_parent": {"__id__": 1325}, "_children": [], "_active": true, "_components": [{"__id__": 1337}, {"__id__": 1339}, {"__id__": 1341}], "_prefab": {"__id__": 1343}, "_lpos": {"__type__": "cc.Vec3", "x": -495, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1336}, "_enabled": true, "__prefab": {"__id__": 1338}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b6c2c73f-1b24-42bb-8b6f-39ff77b18032@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3b5SY6a9ZETp/iznc+E9Tc"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1336}, "_enabled": true, "__prefab": {"__id__": 1340}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "caz0mihNlMh4ES0Tk+ouaA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1336}, "_enabled": true, "__prefab": {"__id__": 1342}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c8Dlsg61D9JjlSjqk5K6Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eyLCjxDVCDoAppzJm5gzE"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1325}, "_enabled": true, "__prefab": {"__id__": 1345}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7p/I6P/pIppOiUEoVDwKX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1325}, "_enabled": true, "__prefab": {"__id__": 1347}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 920, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "denYTVmOhGK5qAgTD7njaM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edeKW3DBBH+4xwy0UijkWh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1219}, "_enabled": true, "__prefab": {"__id__": 1350}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 60, "_bottom": 35, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 128, "_originalHeight": 369, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbOmI/qqxOybtX6wEyXyGJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1219}, "_enabled": true, "__prefab": {"__id__": 1352}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9sS7XYTlPUK6jFWIyIP7h"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1219}, "_enabled": true, "__prefab": {"__id__": 1354}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 369}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "90374flHpIsrpGqd6ffN95"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38SIdFT01L8qGV+A5IctXS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1118}, "_enabled": true, "__prefab": {"__id__": 1357}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "140b298d-a035-49a0-8226-a163e622cd48@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bahGSBQDFNuZjQtb2TOLF7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1118}, "_enabled": true, "__prefab": {"__id__": 1359}, "_alignFlags": 45, "_target": null, "_left": -50, "_right": -50, "_top": 1454, "_bottom": -1254, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 900, "_originalHeight": 1429, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8AT8H2+FMALZhw5j8xtu5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1118}, "_enabled": true, "__prefab": {"__id__": 1361}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "a5GbJpO+FNZ543Sjnkn5P5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1118}, "_enabled": true, "__prefab": {"__id__": 1363}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 2020, "height": 880}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1ZiILV8ZKW5/Fynx0X/pV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dUAkEIohJp5jWBeA7Jyrp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1366}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efXBIWRiZGSolgaujjy2Va"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1368}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8409aeYORHva4AA4p7uEkl"}, {"__type__": "dcf91+yywZGtbxy7zKD7T78", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "priceInput": {"__id__": 403}, "quickBetInput": {"__id__": 1275}, "quickBetLabel": {"__id__": 1327}, "winLabel": {"__id__": 361}, "freeWinLabel": {"__id__": 678}, "freeBetLabel": {"__id__": 36}, "Main": {"__id__": 12}, "QuickBet": {"__id__": 1118}, "QuickBetButton": {"__id__": 58}, "FreeBetButton": {"__id__": 50}, "LayoutContent": {"__id__": 476}, "TicketItem": {"__uuid__": "5e9f4f9d-91fc-43fb-8222-b63b95b3d87c"}, "SuccessNode": {"__id__": 911}, "SuccessQuickNode": {"__id__": 1325}, "InputSingleNode": {"__id__": 337}, "InputSingleFreeNode": {"__id__": 676}, "SelectOddNode": {"__id__": 960}, "_id": "", "__prefab": {"__id__": 1370}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5acCpWGrxC94LFaY+Fvrir"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1372}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "25dvIjM2FE26h6MUK/hbzP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 1374}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "75bUc5bElEU5IarDqW+myG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22bfm57INNaZ6wn058nKqe"}]