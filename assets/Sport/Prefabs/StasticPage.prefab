[{"__type__": "cc.Prefab", "_name": "StasticPage", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "StasticPage", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 15}], "_active": true, "_components": [{"__id__": 47}, {"__id__": 49}, {"__id__": 51}, {"__id__": 53}], "_prefab": {"__id__": 55}, "_lpos": {"__type__": "cc.Vec3", "x": 960, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BG", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}, {"__id__": 12}], "_prefab": {"__id__": 14}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "clickEvents": [{"__id__": 5}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81zcKq63lNrb1HNktD8Wft"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "b232668i2BEeZsh5CAsn20p", "handler": "close", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 7}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "f02b99b3-392f-4b14-8e0c-30a54a83ffa5@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02aSb/dpRElpDxmihYkufX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5YWZH3HNGWqOzuuTbP83W"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 11}, "_opacity": 150}, {"__type__": "cc.CompPrefabInfo", "fileId": "f51Mysn0tGdaQcVCERDlFD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 13}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3PfDGJedH+J4KWWOEYhOt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7lxY9tOpPjZ2iXAySwu+0"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 16}], "_active": true, "_components": [{"__id__": 36}, {"__id__": 38}, {"__id__": 40}, {"__id__": 42}, {"__id__": 44}], "_prefab": {"__id__": 46}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "<PERSON>ame", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [{"__id__": 17}], "_active": true, "_components": [{"__id__": 27}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 35}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "WebView", "_objFlags": 0, "_parent": {"__id__": 16}, "_children": [], "_active": true, "_components": [{"__id__": 18}, {"__id__": 20}, {"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.WebView", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 19}, "_url": "", "webviewEvents": [], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0F/9rwOFFP4RktZWAUmI+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 21}, "_alignFlags": 45, "_target": null, "_left": 5, "_right": 5, "_top": 5, "_bottom": 5, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edND50RFZCJqxCaoU6dIMq"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 23}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "19RoR0twdMcZY66a8Eb5Dd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "__prefab": {"__id__": 25}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1680, "height": 540}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1dkU+UXzJDEocmgg8yeu4E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3eGxjWyDhOW7W69YP2Qghu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ae9de179-9c9f-4b2c-9081-bb4aa9f62ece@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8etO4G3vdOBKwiU7kZZEtE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 30}, "_alignFlags": 45, "_target": null, "_left": 15, "_right": 15, "_top": 15, "_bottom": 15, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 725, "_originalHeight": 339, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2387TanlhN0IItMo4br4qM"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 32}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6el17V7C9DbJtHnq5SB/he"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": {"__id__": 34}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1690, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dWoYUu9lF04d8+IQj7yMZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c0bVCCF71MMpEhZIAwyl4X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a3c63cf7-53a8-48fb-88d1-567fcffb5a46@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8Pi+eru5BxrFRaDRLkvuX"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 39}, "_alignFlags": 45, "_target": null, "_left": 100, "_right": 100, "_top": 250, "_bottom": 250, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 725, "_originalHeight": 600, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbSCrCVkNE+o+DZzhfc94J"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 41}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45UxvOPKtGuY9t3ze+MEU9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 43}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4cWi7GqlBaanNIuXyjsYs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": {"__id__": 45}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1720, "height": 580}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdf7hRd3NHHLZ2KFXnUFcs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3NPxpk3ZLJIZUXl9w0jz7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 48}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4y6Ua/ZBEjoejJEnn7WA+"}, {"__type__": "b232668i2BEeZsh5CAsn20p", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "WebView": {"__id__": 18}, "_id": "", "__prefab": {"__id__": 50}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fUfQDDnNH6pQW8rIVtW3m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 52}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "312m9tgQ9DfbA9CnK6nahD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 54}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "08cnna2H5J8Jx27YijltBz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "93CHE3ZPhBXbbSqr5xNGuP"}]