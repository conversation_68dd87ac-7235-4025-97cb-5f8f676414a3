import { _decorator, Component, Label } from "cc";
const { ccclass, property } = _decorator;

import { Competition } from "./Sport.const";
import SportUIController from "./SportUIController";
import { convertObjectToArray } from "./Ultils";

@ccclass("SearchItemController")
export default class SearchItemController extends Component {
  @property(Label)
  label: Label | null = null;
  @property(Label)
  alias: Label | null = null;
  private data: Competition;
  private callback: () => void;
  setData(data: Competition, cb: () => void) {
    if (data) {
      this.data = data;
      if (this.label) this.label.string = data.name;
      if (this.alias) this.alias.string = data.info ?? "Không có";
    }
    this.callback = cb;
  }
  onClick() {
    if (this.data) {
      const list = convertObjectToArray(this.data.game);
      if (list && list.length) {
        SportUIController.instance.initAndShowLivePagePrefab(list[0]);
      }
    }
    this.callback && this.callback();
  }
}

/**
 * Note: The original script has been commented out, due to the large number of changes in the script, there may be missing in the conversion, you need to convert it manually
 */
// // Learn TypeScript:
// //  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// // Learn Attribute:
// //  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// // Learn life-cycle callbacks:
// //  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
//
// import { Competition } from "./Sport.const";
// import SportUIController from "./SportUIController";
// import { convertObjectToArray } from "./Ultils";
//
// const { ccclass, property } = cc._decorator;
//
// @ccclass
// export default class SearchItemController extends cc.Component {
//
//     @property(cc.Label)
//     label: cc.Label = null;
//
//     @property(cc.Label)
//     alias: cc.Label = null;
//
//     private data: Competition;
//     private callback: () => void;
//
//     setData(data: Competition, cb: () => void) {
//         if (data) {
//             this.data = data;
//             if (this.label) this.label.string = data.name;
//             if (this.alias) this.alias.string = data.info ?? 'Không có';
//         }
//         this.callback = cb;
//     }
//
//     onClick() {
//         if (this.data) {
//             const list = convertObjectToArray(this.data.game);
//             if (list && list.length) {
//                 SportUIController.instance.initAndShowLivePagePrefab(list[0]);
//             }
//         }
//         this.callback && this.callback();
//     }
//
// }
