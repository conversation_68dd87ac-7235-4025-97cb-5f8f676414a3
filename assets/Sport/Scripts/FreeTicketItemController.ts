import { _decorator, Component, Label } from "cc";
const { ccclass, property } = _decorator;

import { BonusData, FreeBetHistory } from "./Sport.const";

@ccclass("FreeTicketItemController")
export default class FreeTicketItemController extends Component {
  @property(Label)
  IDlabel: Label | null = null;
  @property(Label)
  dateGet: Label | null = null;
  @property(Label)
  priceLabel: Label | null = null;
  @property(Label)
  amountLabel: Label | null = null;
  private data: FreeBetHistory;
  setData(data: FreeBetHistory) {
    if (data) {
      this.data = data;
      if (this.IDlabel) this.IDlabel.string = `${data.betID}`;
      if (this.priceLabel) this.priceLabel.string = `${data.amount}`;
      if (this.amountLabel) this.amountLabel.string = `${data.amountPrize}`;
      let time = new Date(data.createdTime).getTime();
      if (this.dateGet) this.dateGet.string = this.formatTimestamp(time);
    }
  }

  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    const pad = (n: number) => (n < 10 ? "0" + n : "" + n);

    return `${pad(date.getDate())}/${pad(date.getMonth() + 1)}/${date.getFullYear()}\n${pad(date.getHours())}:${pad(date.getMinutes())}`;
  }
}
