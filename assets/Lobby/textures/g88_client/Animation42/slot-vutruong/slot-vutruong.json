{"skeleton": {"hash": "J9+p8QaQSsE", "spine": "4.2.43", "x": -5, "y": -457, "width": 220.58, "height": 458, "images": "./slot-vutruong/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 81.85, "rotation": 94.02, "x": 67, "y": -447.15}, {"name": "bone2", "parent": "bone", "length": 101.45, "rotation": 174.9, "x": 306.82, "y": -74.65}, {"name": "bone3", "parent": "bone2", "length": 82.1, "rotation": -31.87, "x": 108.52, "y": -3.06}, {"name": "bone4", "parent": "bone3", "length": 59.31, "rotation": 14.12, "x": 86.7, "y": -0.06}, {"name": "bone5", "parent": "bone", "x": 268.8, "y": -15.06}, {"name": "bone6", "parent": "bone", "x": 270.2, "y": 41.11}, {"name": "bone7", "parent": "bone", "length": 72.15, "rotation": -6.56, "x": 349.08, "y": -28.38}, {"name": "bone8", "parent": "root", "x": 139.08, "y": -433.75}], "slots": [{"name": "Group 529", "bone": "root", "attachment": "Group 529"}, {"name": "<PERSON>ame", "bone": "root", "attachment": "<PERSON>ame"}, {"name": "Boy", "bone": "bone8", "attachment": "Boy"}, {"name": "Body", "bone": "root", "attachment": "Body"}, {"name": "Hand", "bone": "root", "attachment": "Hand"}, {"name": "Light", "bone": "root", "attachment": "Light", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"Body": {"Body": {"type": "mesh", "uvs": [0.23516, 0, 0.80874, 0, 0.87826, 0.1579, 1, 0.25071, 1, 0.37563, 0.76239, 0.52871, 1, 0.64203, 1, 1, 0, 1, 0, 0.65603, 0.10191, 0.53378, 0, 0.43919, 0, 0.39112, 0, 0.30088, 0.23227, 0.16614, 0.18012, 0.47294, 0.43504, 0.47205, 0.78556, 0.4542, 0.23227, 0.28912, 0.44953, 0.28823, 0.79425, 0.28823, 0.09322, 0.39174, 0.46981, 0.39263], "triangles": [21, 12, 13, 22, 15, 21, 18, 22, 21, 11, 12, 21, 19, 14, 2, 1, 14, 0, 14, 1, 2, 7, 9, 6, 5, 6, 9, 9, 10, 5, 5, 10, 16, 10, 15, 16, 9, 7, 8, 10, 11, 15, 5, 17, 4, 5, 16, 17, 22, 16, 15, 15, 11, 21, 16, 22, 17, 22, 18, 19, 17, 22, 4, 22, 20, 4, 22, 19, 20, 18, 21, 13, 20, 3, 4, 18, 13, 14, 18, 14, 19, 3, 20, 2, 20, 19, 2], "vertices": [1, 7, 91.93, 47.48, 1, 1, 7, 95.43, -31.6, 1, 2, 7, 25.18, -44.31, 0.95127, 5, 100.23, -60.21, 0.04873, 1, 1, 326.39, -89.12, 1, 1, 1, 270.56, -85.19, 1, 3, 1, 204.45, -47.67, 0.99345, 7, -141.49, -35.68, 0.00083, 6, -65.75, -88.78, 0.00571, 1, 1, 151.5, -76.82, 1, 1, 1, -8.47, -65.57, 1, 1, 1, 1.21, 72.09, 1, 1, 1, 154.93, 61.28, 1, 1, 1, 208.57, 43.41, 1, 2, 1, 251.83, 54.47, 0.32541, 5, -16.97, 69.53, 0.67459, 1, 6, 3.12, 11.84, 1, 1, 1, 313.64, 50.12, 1, 2, 7, 17.55, 44.59, 0.99892, 5, 102.81, 28.98, 0.00108, 1, 1, 235.01, 30.73, 1, 1, 1, 232.94, -4.39, 1, 2, 1, 237.52, -53.2, 0.92192, 7, -108, -37.4, 0.07808, 1, 1, 316.65, 17.78, 1, 1, 1, 314.95, -12.16, 1, 2, 1, 311.61, -59.61, 0.99714, 7, -33.66, -35.31, 0.00286, 3, 7, -84.27, 59.29, 0.00243, 5, 3.33, 55.21, 0.00196, 6, 1.94, -0.97, 0.99562, 3, 1, 268.09, -11.67, 0.00141, 5, -0.71, 3.39, 0.96723, 6, -2.1, -52.78, 0.03136], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 26, 28, 0, 28, 22, 24, 24, 26], "width": 138, "height": 448}}, "Boy": {"Boy": {"x": 3.92, "y": 207.75, "width": 136, "height": 454}}, "Frame": {"Frame": {"x": 107.5, "y": -228, "width": 215, "height": 458}}, "Group 529": {"Group 529": {"x": 107.5, "y": -228, "width": 215, "height": 458}}, "Hand": {"Hand": {"type": "mesh", "uvs": [0.62866, 0, 1, 0, 1, 0.17555, 0.95608, 0.38412, 0.94847, 0.47893, 0.50302, 0.80759, 0.4421, 0.87395, 0.3355, 1, 0, 1, 1e-05, 0.83445, 0.08802, 0.7665, 0.16798, 0.7428, 0.56393, 0.47103, 0.58297, 0.40308], "triangles": [6, 11, 5, 10, 11, 6, 9, 10, 6, 7, 9, 6, 8, 9, 7, 5, 11, 12, 5, 12, 4, 0, 1, 2, 2, 13, 0, 3, 13, 2, 4, 13, 3, 12, 13, 4], "vertices": [1, 2, -13.49, -19.19, 1, 1, 2, -14.23, 19.79, 1, 1, 2, 30.18, 20.63, 1, 1, 2, 83.03, 17.02, 1, 2, 2, 107.02, 16.67, 0.71537, 3, -11.69, 15.96, 0.28463, 2, 3, 83.53, 21.94, 0.83714, 4, 2.29, 22.11, 0.16286, 1, 4, 20.25, 21.47, 1, 1, 4, 54.05, 21.17, 1, 1, 4, 65.41, -12.18, 1, 1, 4, 25.77, -25.69, 1, 2, 3, 98.51, -20.28, 0.00434, 4, 6.52, -22.49, 0.99566, 2, 3, 88.91, -16.5, 0.20286, 4, -1.87, -16.48, 0.79714, 2, 2, 105.79, -23.74, 0.20193, 3, 8.6, -19.01, 0.79807, 2, 2, 88.56, -22.06, 0.77555, 3, -6.92, -26.68, 0.22445], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 105, "height": 253}}, "Light": {"Light": {"x": 108.08, "y": -189.5, "width": 215, "height": 237}}}}], "animations": {"animation": {"slots": {"Light": {"alpha": [{"value": 1, "curve": [0.111, 1, 0.222, 0.38]}, {"time": 0.3333, "value": 0.3825, "curve": [0.444, 0.38, 0.556, 1]}, {"time": 0.6667, "value": 1, "curve": [0.778, 1, 0.889, 0.38]}, {"time": 1, "value": 0.3825, "curve": [1.111, 0.38, 1.222, 1]}, {"time": 1.3333, "value": 1, "curve": [1.444, 1, 1.556, 0.38]}, {"time": 1.6667, "value": 0.3825, "curve": [1.778, 0.38, 1.889, 1]}, {"time": 2, "value": 1}]}}, "bones": {"bone5": {"translate": [{"curve": [0.113, 0, 0.227, 5.28, 0.113, 0, 0.227, 0.03]}, {"time": 0.3333, "x": 5.28, "y": 0.03, "curve": [0.389, 5.28, 0.445, -5.22, 0.389, 0.03, 0.445, 1.58]}, {"time": 0.5, "x": -5.31, "y": 1.58, "curve": [0.557, -5.41, 0.614, 1.46, 0.557, 1.58, 0.614, 1.08]}, {"time": 0.6667, "x": 1.52, "y": 1.04, "curve": [0.724, 1.59, 0.781, -3.65, 0.724, 1, 0.781, 1.33]}, {"time": 0.8333, "x": -3.66, "y": 1.28, "curve": [1.002, -3.68, 1.169, 3.35, 1.002, 1.14, 1.169, 1.67]}, {"time": 1.3333, "x": 4.12, "y": 1.42, "curve": [1.558, 5.15, 1.779, 0, 1.558, 1.09, 1.779, 0]}, {"time": 2}]}, "bone6": {"translate": [{"curve": [0.111, 0.15, 0.222, 2.37, 0.111, 0, 0.222, 0]}, {"time": 0.3333, "x": 2.37, "curve": [0.389, 2.37, 0.444, -3.35, 0.389, 0, 0.444, 0]}, {"time": 0.5, "x": -3.35, "curve": [0.556, -3.35, 0.611, 0.27, 0.556, 0, 0.611, 0]}, {"time": 0.6667, "x": 0.27, "curve": [0.722, 0.27, 0.778, -5.31, 0.722, 0, 0.778, 0]}, {"time": 0.8333, "x": -5.31, "curve": [1, -5.31, 1.167, 1.9, 1, 0, 1.167, 0.13]}, {"time": 1.3333, "x": 2.12, "y": 0.13, "curve": [1.556, 2.42, 1.778, -0.3, 1.556, 0.13, 1.778, 0]}, {"time": 2}]}, "bone7": {"rotate": [{"curve": [0.333, 0, 0.667, -3.47]}, {"time": 1, "value": -3.47, "curve": [1.333, -3.47, 1.667, 0]}, {"time": 2}]}, "bone": {"rotate": [{"curve": [0.333, 0, 0.667, 0.78]}, {"time": 1, "value": 0.78, "curve": [1.333, 0.78, 1.667, 0]}, {"time": 2}]}, "bone2": {"rotate": [{"curve": [0.222, 0, 0.444, 8.42]}, {"time": 0.6667, "value": 8.42, "curve": [0.889, 8.42, 1.111, 7.75]}, {"time": 1.3333, "value": 5.65, "curve": [1.556, 3.54, 1.778, 0]}, {"time": 2}]}, "bone3": {"rotate": [{"curve": [0.278, 0, 0.556, 4.52]}, {"time": 0.8333, "value": 4.52, "curve": [1.222, 4.52, 1.611, 0]}, {"time": 2}]}, "bone4": {"rotate": [{"curve": [0.211, 0, 0.422, -32.81]}, {"time": 0.6333, "value": -32.81, "curve": [0.824, -32.81, 1.014, -23.45]}, {"time": 1.2, "value": -20.16, "curve": [1.47, -15.58, 1.735, 0]}, {"time": 2}]}, "bone8": {"scale": [{"curve": [0.333, 1, 0.667, 1, 0.333, 1, 0.667, 0.993]}, {"time": 1, "y": 0.993, "curve": [1.333, 1, 1.667, 1, 1.333, 0.993, 1.667, 1]}, {"time": 2}]}}}}}