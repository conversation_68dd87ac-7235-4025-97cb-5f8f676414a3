{"skeleton": {"hash": "jvDRbUVvWvs", "spine": "4.2.43", "x": 0.5, "y": -458, "width": 215, "height": 458, "images": "./sport-virtualsport/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone2", "parent": "root", "length": 162.39, "rotation": 105.51, "x": 171.48, "y": -209.37}, {"name": "bone3", "parent": "root", "length": 157.54, "rotation": 109.28, "x": 123.15, "y": -295.47}, {"name": "bone", "parent": "root", "length": 69.73, "rotation": 78.82, "x": 51.05, "y": -144.31}, {"name": "bone4", "parent": "bone", "length": 38.73, "rotation": 21.54, "x": 89.88, "y": -6.88}], "slots": [{"name": "Rectangle 1033", "bone": "root", "attachment": "Rectangle 1033"}, {"name": "Group 515", "bone": "root", "attachment": "Group 515"}, {"name": "Layer 6", "bone": "bone2", "attachment": "Layer 6"}, {"name": "Layer 7", "bone": "bone3", "attachment": "Layer 7"}], "skins": [{"name": "default", "attachments": {"Group 515": {"Group 515": {"type": "mesh", "uvs": [0.48216, 0, 0.99067, 0, 1, 0.40317, 1, 1, 0, 1, 0, 0.49659, 0.49158, 0.23309, 0.75996, 0.3337], "triangles": [6, 0, 1, 5, 3, 4, 7, 5, 6, 5, 7, 2, 2, 3, 5, 7, 6, 1, 7, 1, 2], "vertices": [2, 3, 139.91, 36.42, 0.00076, 4, 62.43, 21.9, 0.99924, 1, 4, 54.47, -21.62, 1, 2, 3, 81.01, -21.15, 0.8833, 4, -13.49, -10.02, 0.1167, 1, 3, -19.11, -40.93, 1, 1, 3, -35.98, 44.42, 1, 2, 3, 48.47, 61.11, 0.99896, 4, -13.56, 78.44, 0.00104, 2, 3, 100.97, 27.88, 0.89783, 4, 23.07, 28.26, 0.10217, 2, 3, 88.61, 1.64, 0.79279, 4, 1.95, 8.39, 0.20721], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12], "width": 87, "height": 171}}, "Layer 6": {"Layer 6": {"x": 76.45, "y": -8.26, "rotation": -105.51, "width": 113, "height": 195}}, "Layer 7": {"Layer 7": {"x": 71.87, "y": -6.45, "rotation": -109.28, "width": 186, "height": 195}}, "Rectangle 1033": {"Rectangle 1033": {"x": 108, "y": -229, "width": 215, "height": 458}}}}], "animations": {"animation": {"bones": {"bone": {"rotate": [{"curve": [0.333, 0, 0.667, -2.85]}, {"time": 1, "value": -2.85, "curve": [1.333, -2.85, 1.667, 0]}, {"time": 2}], "translate": [{"curve": [0.333, 0, 0.667, 7.83, 0.333, 0, 0.667, 1.92]}, {"time": 1, "x": 7.83, "y": 1.92, "curve": [1.333, 7.83, 1.667, 0, 1.333, 1.92, 1.667, 0]}, {"time": 2}]}, "bone4": {"rotate": [{"value": 3.09, "curve": [0.058, 1.34, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, 39.24]}, {"time": 1.1667, "value": 39.24, "curve": [1.446, 39.24, 1.724, 12.06]}, {"time": 2, "value": 3.09}], "translate": [{"x": -0.56, "y": -0.82, "curve": [0.058, -0.24, 0.112, 0, 0.058, -0.35, 0.112, 0]}, {"time": 0.1667, "curve": [0.5, 0, 0.833, -7.08, 0.5, 0, 0.833, -10.37]}, {"time": 1.1667, "x": -7.08, "y": -10.37, "curve": [1.446, -7.08, 1.724, -2.18, 1.446, -10.37, 1.724, -3.19]}, {"time": 2, "x": -0.56, "y": -0.82}], "scale": [{"x": 0.976, "curve": [0.058, 0.989, 0.112, 1, 0.058, 1, 0.112, 1]}, {"time": 0.1667, "curve": [0.5, 1, 0.833, 0.691, 0.5, 1, 0.833, 1]}, {"time": 1.1667, "x": 0.691, "curve": [1.446, 0.691, 1.724, 0.905, 1.446, 1, 1.724, 1]}, {"time": 2, "x": 0.976}]}, "bone2": {"rotate": [{"time": 0.3333, "curve": [1, 0, 1.444, 0]}, {"time": 2}], "scale": [{"x": 0.988, "curve": [0.113, 0.995, 0.223, 1, 0.113, 1, 0.223, 1]}, {"time": 0.3333, "curve": [0.667, 1, 1, 0.956, 0.667, 1, 1, 1]}, {"time": 1.3333, "x": 0.956, "curve": [1.557, 0.956, 1.78, 0.975, 1.557, 1, 1.78, 1]}, {"time": 2, "x": 0.988}]}, "bone3": {"translate": [{"x": 3.08, "y": -1.54, "curve": [0.224, 1.85, 0.446, 0, 0.224, -0.92, 0.446, 0]}, {"time": 0.6667, "curve": [1, 0, 1.333, 4.17, 1, 0, 1.333, -2.09]}, {"time": 1.6667, "x": 4.17, "y": -2.09, "curve": [1.779, 4.17, 1.891, 3.71, 1.779, -2.09, 1.891, -1.85]}, {"time": 2, "x": 3.08, "y": -1.54}], "scale": [{"x": 0.971, "y": 0.981, "curve": [0.224, 0.982, 0.446, 1, 0.224, 0.988, 0.446, 1]}, {"time": 0.6667, "curve": [1, 1, 1.333, 0.96, 1, 1, 1.333, 0.974]}, {"time": 1.6667, "x": 0.96, "y": 0.974, "curve": [1.779, 0.96, 1.891, 0.965, 1.779, 0.974, 1.891, 0.977]}, {"time": 2, "x": 0.971, "y": 0.981}]}}}}}