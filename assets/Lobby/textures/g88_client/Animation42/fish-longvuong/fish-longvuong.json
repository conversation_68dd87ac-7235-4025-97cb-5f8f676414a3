{"skeleton": {"hash": "uNyAD5XXdKA", "spine": "4.2.43", "x": -26, "y": -457, "width": 270, "height": 472, "images": "./fish-longvuong/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 169.92, "rotation": 70.03, "x": 84.99, "y": -219.34}, {"name": "bone2", "parent": "bone", "length": 85.24, "rotation": 137.49, "x": 140.37, "y": 47.03}, {"name": "bone3", "parent": "bone2", "length": 30.47, "rotation": 125.46, "x": 96.74, "y": 18.62}, {"name": "bone4", "parent": "bone", "length": 46.44, "rotation": -134.94, "x": 159.54, "y": -27.56}, {"name": "bone5", "parent": "bone4", "length": 37.57, "rotation": -17.77, "x": 58.84, "y": -9.92}], "slots": [{"name": "Rectangle 1", "bone": "root", "attachment": "Rectangle 1"}, {"name": "Layer 87", "bone": "root", "attachment": "Layer 87"}, {"name": "wave", "bone": "root", "attachment": "wave"}, {"name": "Group 4", "bone": "bone3", "attachment": "Group 4"}, {"name": "ca", "bone": "root", "attachment": "ca"}], "skins": [{"name": "default", "attachments": {"ca": {"ca": {"x": 101, "y": -324, "width": 194, "height": 172}}, "Group 4": {"Group 4": {"x": 84.54, "y": 48.78, "rotation": 27.01, "width": 270, "height": 137}}, "Layer 87": {"Layer 87": {"type": "mesh", "uvs": [0.54156, 0, 0.81063, 0, 0.82196, 0.14951, 1, 0.22198, 1, 0.32983, 1, 0.5624, 0.8078, 0.50173, 0.67751, 0.50679, 0.70867, 0.58094, 1, 0.69216, 1, 1, 0, 1, 0, 0.6197, 0.14503, 0.53375, 0, 0.43769, 0, 0.27928, 0.20451, 0.15288, 0.48774, 0.07199, 0.27617, 0.44546, 0.63886, 0.45861, 0.25815, 0.44218, 0.30076, 0.34409, 0.74279, 0.36167, 0.16896, 0.54945], "triangles": [11, 12, 23, 11, 8, 10, 8, 9, 10, 6, 22, 4, 19, 22, 6, 6, 4, 5, 22, 2, 3, 4, 22, 3, 14, 15, 21, 20, 14, 21, 13, 14, 20, 21, 16, 17, 15, 16, 21, 0, 2, 17, 2, 0, 1, 22, 17, 2, 21, 17, 22, 18, 20, 21, 19, 21, 22, 18, 21, 19, 12, 13, 23, 18, 19, 7, 7, 23, 18, 23, 20, 18, 7, 8, 23, 11, 23, 8, 23, 13, 20, 7, 19, 6], "vertices": [2, 1, 230.8, 50.98, 0.81042, 2, -64, -64.02, 0.18958, 3, 1, 252.67, -9.21, 0.92461, 2, -120.79, -34.42, 0.03056, 4, -78.77, 52.97, 0.04483, 2, 1, 197.38, -32.16, 0.45066, 4, -23.47, 30.04, 0.54934, 2, 4, 20.75, 56.12, 0.99984, 5, -56.42, 51.27, 0.00016, 2, 4, 59.82, 37.82, 0.78432, 5, -13.63, 45.77, 0.21568, 1, 5, 78.63, 33.9, 1, 2, 1, 63.81, -77.11, 0.06159, 5, 48.73, -8.37, 0.93841, 1, 1, 51.32, -48.65, 1, 2, 1, 25.98, -65.75, 0.92066, 5, 77.15, -35.81, 0.07934, 1, 0, 225, -261.86, 1, 1, 0, 225, -385, 1, 1, 0, -13, -385, 1, 1, 0, -13, -232.88, 1, 2, 1, -2.09, 66.78, 0.48286, 3, 38.84, -54.2, 0.51714, 3, 1, 22.23, 112.34, 0.01024, 2, 131.21, 31.68, 0.00129, 3, -9.36, -35.65, 0.98848, 2, 2, 101.93, -24.52, 0.96983, 3, -38.14, 20.8, 0.03017, 1, 2, 35.4, -46.86, 1, 2, 1, 199.36, 53.19, 0.6051, 2, -39.33, -44.4, 0.3949, 1, 1, 41.76, 49.5, 1, 2, 1, 66.29, -33.43, 0.66409, 5, 26.5, -46.05, 0.33591, 3, 1, 41.53, 53.98, 0.26417, 2, 77.55, 61.66, 0.0228, 3, 46.19, -9.34, 0.71303, 3, 1, 81.87, 57.85, 0.29574, 2, 50.43, 31.55, 0.20702, 3, 37.41, 30.22, 0.49724, 3, 1, 111.18, -43.43, 0.5108, 4, 45.39, -23.02, 0.26372, 5, -8.81, -16.57, 0.22548, 1, 1, -6.05, 59.28, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 238, "height": 400}}, "Rectangle 1": {"Rectangle 1": {"x": 107.5, "y": -228, "width": 215, "height": 458}}, "wave": {"wave": {"x": 107, "y": -231.5, "width": 220, "height": 243}}}}], "animations": {"animation": {"bones": {"bone": {"rotate": [{"curve": [0.333, 0, 0.667, -0.85]}, {"time": 1, "value": -0.85, "curve": [1.333, -0.85, 1.667, 0]}, {"time": 2}], "translate": [{"curve": [0.333, 0, 0.667, 0, 0.333, 0, 0.667, -11.76]}, {"time": 1, "y": -11.76, "curve": [1.333, 0, 1.667, 0, 1.333, -11.76, 1.667, 0]}, {"time": 2}]}, "bone5": {"rotate": [{"curve": [0.333, 0, 0.667, -11.79]}, {"time": 1, "value": -11.79, "curve": [1.333, -11.79, 1.667, 0]}, {"time": 2}]}, "bone3": {"rotate": [{"curve": [0.333, 0, 0.667, -4.52]}, {"time": 1, "value": -4.52, "curve": [1.333, -4.52, 1.667, 0]}, {"time": 2}]}}}}}