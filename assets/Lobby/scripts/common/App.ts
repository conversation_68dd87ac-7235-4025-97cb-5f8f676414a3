import {
    _decorator,
    Component,
    Node,
    Size,
    Label,
    SpriteFrame,
    instantiate,
    easing,
    Tween,
    tween,
    Prefab,
    log,
    sys,
    native,
    Sprite,
    Slider,
    UITransform,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    director,
    UIOpacity,
    BlockInputEvents
} from "cc";
import AlertDialog from "db://assets/Lobby/scripts/common/AlertDialog";
import {LanguageManager} from "db://assets/Lobby/scripts/common/language/Language.LanguageManager";
import ConfirmDialog from "db://assets/Lobby/scripts/common/ConfirmDialog";
import Config from "db://assets/Lobby/scripts/common/Config";
import MiniGameTXSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXSignalRClient";
import MiniGameSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameSignalRClient";
import BundleControl from "db://assets/Loading/scripts/BundleControl";
import {MiniGame} from "db://assets/Lobby/scripts/common/MiniGame";
import AudioManager from "./AudioManager";
import {BroadcastReceiver} from "./BroadcastListener";
import MiniGameTXMD5SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXMD5SignalRClient";
import BlackjackSignalRClient from "db://assets/Lobby/scripts/common/networks/BlackjackSignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import BaccaratSignalRClient from "db://assets/Lobby/scripts/common/networks/BaccaratSignalRClient";
import DragonTigerSignalRClient from "db://assets/Lobby/scripts/common/networks/DragonTigerSignalRClient";
import RouletteSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteSignalRClient";
import SicboSignalRClient from "db://assets/Lobby/scripts/common/networks/SicboSignalRClient";
import SedieSignalRClient from "db://assets/Lobby/scripts/common/networks/SedieSignalRClient";
import KingdomSignalRClient from "./networks/KingdomSignalRClient";
import OceanSignalRClient from "./networks/OceanSignalRClient";
import DQSignalRClient from "./networks/DQSignalRClient";
import ForestSignalRClient from "./networks/ForestSignalRClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import CardGameSignalRClient from "db://assets/Lobby/scripts/common/networks/CardGameSignalRClient";
import Http from "db://assets/Lobby/scripts/common/Http";
import XocDiaLiveSignalRClient from "db://assets/Lobby/scripts/common/networks/XocDiaLiveSignalRClient";
import {Global} from "db://assets/Lobby/scripts/common/Global";
import {SamTruyenNetWorkSignalR} from "./networks/SamTruyenSignalRClient";
import eventBus from "db://assets/Lobby/scripts/common/EventBus";
import {PopupTournament} from "db://assets/Lobby/scripts/controller/lobby/PopupTournament";
import {SoDoSignalRClient} from "./networks/SoDoSignalRClient";
import ThanTaiSignalRClient from "./networks/ThanTaiSignalRClient";
import RouletteLiveSignalRClient from "db://assets/Lobby/scripts/common/networks/RouletteLiveSignalRClient";

const {ccclass, property} = _decorator;

@ccclass("App")
export default class App extends Component {
    @property(Node)
    public canvas: Node | null = null;
    static instance: App = null;
    inactivityTimer = 0;
    DataPass = [];

    @property
    designResolution: Size = new Size(1920, 1080);
    @property([SpriteFrame])
    sprFrameAvatars: Array<SpriteFrame> = new Array<SpriteFrame>();
    @property([SpriteFrame])
    listEmotionSpr: SpriteFrame[] = [];
    @property(AlertDialog)
    alertDialog: AlertDialog = null;
    @property(AlertDialog)
    notificationDialog: AlertDialog = null;
    @property(ConfirmDialog)
    confirmDialog: ConfirmDialog = null;
    @property(Node)
    loadingNode: Node = null;
    @property(Label)
    loadingLabel: Label = null;
    @property(Node)
    bigGameNode: Node = null;
    @property(Node)
    skillsGameNode: Node = null;
    @property(Node)
    slotGameNode: Node = null;
    @property(Node)
    miniGameNode: Node = null;
    @property(Node)
    popupNode: Node = null;
    @property(Node)
    nodeRoom: Node = null;
    @property(Node)
    tipzoJackpotEventX2X6Node: Node = null;
    @property(Node)
    tipzoMiniLiveNode: Node = null;
    @property(Node)
    alertToast: Node = null;
    @property([Node])
    allNodeGame: Node[] = [];

    @property(Label)
    lblStatus: Label = null;
    @property(Sprite)
    spriteProgress: Sprite = null;
    @property(Slider)
    nodeSlider: Slider = null;
    @property(Node)
    loadingProcess: Node = null;

    // Vuong quoc
    @property({type: Node, group: "Loading"})
    vuongQuocLoadingProgress: Node = null;
    @property({type: Sprite, group: "Loading"})
    vuongQuocSpriteProgress: Sprite = null;

    // Thuy cung
    @property({type: Node, group: "Loading"})
    thuyCungLoadingProgress: Node = null;
    @property({type: Sprite, group: "Loading"})
    thuyCungSpriteProgress: Sprite = null;

    // Rung vang
    @property({type: Node, group: "Loading"})
    rungVangLoadingProgress: Node = null;
    @property({type: Sprite, group: "Loading"})
    rungVangSpriteProgress: Sprite = null;

    @property({type: Node, group: "Loading"})
    gaiNhayLoadingProgress: Node = null;
    @property({type: Sprite, group: "Loading"})
    gaiNhaySpriteProgress: Sprite = null;

    public isShowNotifyJackpot = true;
    private timeOutLoading: any = null;
    public taiXiuJackpotMiniGame: MiniGame = null;
    public taiXiuMD5MiniGame: MiniGame = null;
    public hiLoMiniGame: MiniGame = null;
    public miniPoker: MiniGame = null;
    public phucSinhMiniGame: MiniGame = null;
    public bauCuaMiniGame: MiniGame = null;
    public luckyWildMiniGame: MiniGame = null;
    public gameNodeMap: Map<number, Node[]> = new Map();
    public slotGame: { [key: number]: Node } = {};
    private autoRunningGameIds: number[] = [];
    public isMegaMillions = false;

    getAutoRunningGameIds() {
        return this.autoRunningGameIds;
    }

    addAutoRunningGameIds(idGame) {
        if (!this.autoRunningGameIds.includes(idGame)) {
            this.autoRunningGameIds.push(idGame);
        }
    }

    removeAutoRunningGameIds(idGame: number) {
        this.autoRunningGameIds = this.autoRunningGameIds.filter((id) => id !== idGame);
    }

    clearAutoRunningGameIds() {
        this.autoRunningGameIds = [];
    }

    protected onLoad() {
        if (App.instance != null) {
            this.node.destroy();
            return;
        }
        App.instance = this;
        director.addPersistRootNode(this.node.parent);

        const gameIds = [
            Config.GameAvailableIds.SamLocSolo,
            Config.GameAvailableIds.BaCay,
            Config.GameAvailableIds.Catte,
            Config.GameAvailableIds.TLMN,
            Config.GameAvailableIds.MauBinh,
            Config.GameAvailableIds.Poker,
            Config.GameAvailableIds.TLMNSolo,
            Config.GameAvailableIds.SamLoc,
            Config.GameAvailableIds.Sedie,
            Config.GameAvailableIds.NewSicbo,
            Config.GameAvailableIds.NewRoulette,
            Config.GameAvailableIds.DragonTiger,
            Config.GameAvailableIds.Baccarat,
            Config.GameAvailableIds.Blackjack,
            Config.GameAvailableIds.Kingdom,
            Config.GameAvailableIds.Olympia,
            Config.GameAvailableIds.Ocean,
            Config.GameAvailableIds.Forest,
            Config.GameAvailableIds.GodOfFortune,
            Config.GameAvailableIds.Dancing,
            Config.GameAvailableIds.Disco,
            Config.GameAvailableIds.SoDo,
            Config.GameAvailableIds.SpaceWar,
            Config.GameAvailableIds.Sortie,
            Config.GameAvailableIds.Shark,
            Config.GameAvailableIds.TieuLongNgu,
            Config.GameAvailableIds.BanCa,
            Config.GameAvailableIds.PokerTournament,
            Config.GameAvailableIds.ABC, //chua ro game nay
            Config.GameAvailableIds.TournamentGoOn,
            Config.GameAvailableIds.TournamentOTT,
            Config.GameAvailableIds.MultiLuckyDiceLive,
            Config.GameAvailableIds.LuckyDiceMd5Live,
            Config.GameAvailableIds.SedieLive,
            Config.GameAvailableIds.MegaMillions,
            Config.GameAvailableIds.PowerBall,
            Config.GameAvailableIds.Keno,
            Config.GameAvailableIds.OTT, //xoso
            Config.GameAvailableIds.FantasySport,
            Config.GameAvailableIds.SportVirtual,
            Config.GameAvailableIds.VQMM, //MiniGame
            Config.GameAvailableIds.MiniPoker,
            Config.GameAvailableIds.TaiXiu,
            Config.GameAvailableIds.LuckyDiceMd5,
            Config.GameAvailableIds.HiLo,
            Config.GameAvailableIds.BauCua,
            Config.GameAvailableIds.PhucSinh,
            Config.GameAvailableIds.LuckyWild,
            Config.GameAvailableIds.OTT,
        ];

        this.allNodeGame.forEach((node, index) => {
            const gameId = gameIds[index];
            if (gameId != null) {
                if (!this.gameNodeMap.has(gameId)) {
                    this.gameNodeMap.set(gameId, []);
                }
                this.gameNodeMap.get(gameId).push(node);
                if (node !== null) {
                    (node as any).gameId = gameId;
                }
            }
        });

        this.setActiveGameById(Config.GameAvailableIds.ABC, false);
    }

    showLoadingProcess(isShow: boolean) {
        const children = this.node.children;
        if (children.length > 0) {
            const topZIndex = children[children.length - 1].getComponent(UITransform)?.priority ?? 0;
            const transform = this.loadingProcess.getComponent(UITransform);
            if (transform) {
                transform.priority = topZIndex + 1;
            }
        }
        this.loadingProcess.active = isShow;
    }

    gotoLobby() {
        AudioManager.getInstance().turnOnMusic();
        this.bigGameNode.removeAllChildren();
        this.skillsGameNode.removeAllChildren();
        this.slotGameNode.removeAllChildren();
        this.alertDialog.dismiss();
        this.alertToast.active = false;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        Tween.stopAllByTarget(this.alertToast);
        Utils.setStorageValue("last_open_game_id", "");
        Utils.setStorageValue("last_open_casino_room_value", "");
        Utils.setStorageValue("last_open_casino_room_currency", "");
    }

    static sendEventAF(objectAF) {
        // exeample  Utils.sendEventAF({"eventName":"af_registration","af_System_User_ID":"aaa"})

        try {
            var text = JSON.stringify(objectAF);
            log("Send tracking ==== " + text);
            if (sys.os == sys.OS.ANDROID) {
                // jsb.reflection.callStaticMethod("com/cocos/game/AppActivity", "JavaCopy", "(Ljava/lang/String;)V", text);
                native.reflection.callStaticMethod("com/cocos/game/AppActivity", "sendEventAF", "(Ljava/lang/String;)V", text);
            } else if (sys.os == sys.OS.IOS) {
                native.reflection.callStaticMethod("AppDelegate", "sendEventAF:", text);
            } else {
            }
        } catch (message) {
            //  log("Error Copy:", message);
        }
    }

    getAvatarSpriteFrame(avatar: string): SpriteFrame {
        let avatarInt = parseInt(avatar);

        if (isNaN(avatarInt) || avatarInt < 0 || avatarInt >= this.sprFrameAvatars.length) {
            return this.sprFrameAvatars[0];
        }

        return this.sprFrameAvatars[avatarInt];
    }

    public getTextLang(key: string) {
        return LanguageManager.instance.getString(key);
    }

    showLoading(isShow: boolean, timeOut: number = 15) {
        if (!this.loadingNode || !this.loadingLabel) return;
        const lastChild = this.node.children[this.node.children.length - 1];
        this.loadingNode.setSiblingIndex(lastChild?.getSiblingIndex() + 1 || 0);

        this.loadingLabel.string = App.instance.getTextLang("IS_LOADING");

        if (this.timeOutLoading != null) {
            clearTimeout(this.timeOutLoading);
        }

        if (isShow) {
            if (timeOut > 0) {
                this.timeOutLoading = setTimeout(() => {
                    this.showLoading(false);
                }, timeOut * 1000);
            }
            this.loadingNode.active = true;
        } else {
            this.loadingNode.active = false;
        }
    }

    public ShowAlertDialog(mess: string,callback = null) {
        this.alertDialog.showMsg(mess,callback);
    }

    showErrLoading(msg?: string) {
        this.showLoading(true, 1);
        this.loadingLabel.string = msg ? msg : "Mất kết nối, đang thử lại...";
    }

    openGameFromWheel(gameId: number) {
        // this.openGame(gameId);
    }

    openGame(gameId: number) {
        if (Configs.Login.IsLogin === false) {
            Global.LobbyController.actShowLoginForm();
            return;
        }

        if (Configs.App.GAME_BLOCKED_IDS.includes(gameId)) {
            App.instance.notificationDialog.showMsg(App.instance.getTextLang("me10014"));
            return;
        }

        switch (gameId) {
            case Config.InGameIds.TaiXiuMini:
            case Config.InGameIds.TaiXiuMD5:
            case Config.InGameIds.XoSo:
            case Config.InGameIds.LuckyWild:
            case Config.InGameIds.MiniPhucSinh:
            case Config.InGameIds.BauCua:
            case Config.InGameIds.HiLo:
            case Config.InGameIds.MiniPoker:
            case Config.InGameIds.TaiXiuLive:
            case Config.InGameIds.TaiXiuMD5Live:
            case Config.InGameIds.XocDiaLive:
            case Config.InGameIds.XocdiaMd5Live:
            case Config.InGameIds.CrabFishLive:
            case Config.InGameIds.DragonTigerLive:
            case Config.InGameIds.RouletteLive:
            case Config.InGameIds.BaccaratLive:
            case Config.InGameIds.SicboLive:
                break;
            default:
                Utils.setStorageValue("last_open_game_id", gameId.toString());
                break;
        }

        switch (gameId) {
            case Config.InGameIds.TaiXiuMini:
                this.actGameTaiXiuJackpot();
                break;
            case Config.InGameIds.TaiXiuLive:
                Utils.setStorageValue("opened_tx_jp_live", "true");
                this.actGameTaiXiuJackpot(true);
                break;
            case Config.InGameIds.TaiXiuMD5:
                this.actGameTaiXiuMD5();
                break;
            case Config.InGameIds.TaiXiuMD5Live:
                this.actGameTaiXiuMD5(true);
                break;
            case Config.InGameIds.XoSo:
                this.actXoSoMiniGame();
                break;
            case Config.InGameIds.XocDiaLive:
                this.actXocDiaLive();
                break;
            case Config.InGameIds.XocdiaMd5Live:
                this.actXocDiaLive(true);
                break;
            case Config.InGameIds.DragonTigerLive:
                this.actRongHoLive();
                break;
            case Config.InGameIds.RouletteLive:
                this.actRouletteLive();
                break;
            case Config.InGameIds.BlackJack:
            case Config.InGameIds.Baccarat:
            case Config.InGameIds.Sicbo:
                this.actGoToCasino(gameId);
                break;
            case Config.InGameIds.Catte:
            case Config.InGameIds.BaCay:
            case Config.InGameIds.MauBinh:
                this.actCardGame(gameId);
                break;
            case Config.InGameIds.SamLoc:
                this.actGoToSam(Config.InGameIds.SamLoc);
                break;
            case Config.InGameIds.SamLocSolo:
                this.actGoToSam(Config.InGameIds.SamLocSolo);
                break;
            case Config.InGameIds.TLMN:
                this.actGoToTLMN(Config.InGameIds.TLMN);
                break;
            case Config.InGameIds.TLMNSolo:
                this.actGoToTLMN(Config.InGameIds.TLMNSolo);
                break;
            
            case Config.InGameIds.Poker:
                this.actGoToPoker(Configs.InGameIds.Poker);
                 break;
            case Config.InGameIds.MiniPoker:
                this.actGameMiniPoker();
                break;
            case Config.InGameIds.LuckyWild:
                this.actGameLuckyWild();
                break;

            case Config.InGameIds.BauCua:
                this.actGameBauCua();
                break;
            case Config.InGameIds.HiLo:
                this.actGameHiLo();
                break;
            case Config.InGameIds.MiniPhucSinh:
                this.actGamePhucSinh();
                break;
            case Config.InGameIds.XuatKich:
                this.actGameXuatKich();
                break;
            case Config.InGameIds.LongVuong:
                this.actGameLongVuong();
                break;
            case Config.InGameIds.SamTruyen:
                this.actGameSamTruyen();
                break;
            case Config.InGameIds.PhiDoi:
                this.actGamePhiDoi();
                break;
            case Config.InGameIds.CaMap:
                this.actGameCaMap();
                break;
            case Config.InGameIds.CaKiem:
                this.actGameCaKiem();
                break;
            case Config.InGameIds.VirtualSports:
                this.actOpenVirtualSports();
                break;
            case Config.InGameIds.Kingdom:
                this.actGameVuongQuoc();
                break;
            case Config.InGameIds.ThuyCung:
                this.actGameThuyCung();
                break;
            case Config.InGameIds.RungVang:
                this.actGameRungVang();
                break;
            case Config.InGameIds.GaiNhay:
                this.actGameGaiNhay();
                break;
            case Config.InGameIds.ThanTai:
                this.actGameThanTai();
                break;
            case Config.InGameIds.VuTruong:
                this.actGameVuTruong();
                break;
            case Config.InGameIds.MegaMillion:
                this.actGameMegaMillions();
                break;
            case Config.InGameIds.Keno:
                this.actGameKeno();
                break;
            case Config.InGameIds.Sports:
                this.actGoToSport();
                break;
            case Config.InGameIds.PowerBall:
                this.actGameUSPowerBall();
                break;
            case Config.InGameIds.SoDo:
                this.actGameSoDo();
                break;
            case Config.InGameIds.TourPoker:
                this.actGamePokerTour();
                break;
            default:
                break;
        }
    }

    actGoToSam(idGame) {

        App.instance.showLoading(true);
        App.instance.openPrefabGame("Sam", "Sam", () => {
            CardGameSignalRClient.getInstance().connectHub(idGame, (success) => {
                App.instance.showLoading(false);
                if (success) {
                    this.nodeRoom.removeAllChildren();
                    let cb = (prefab) => {
                        let popupDaily = instantiate(prefab)
                            .getComponent("RoomCards");
                        this.nodeRoom.addChild(popupDaily.node);
                        popupDaily.setDataRoom(idGame);

                    };
                    BundleControl.loadPrefabPopup("prefabs/RoomCards", cb);
                } else {
                    App.instance.showErrLoading(App.instance.getTextLang("me11"));
                }

            });
        });
    }

    getEmotionSpriteFrameByIdx(idx: number) {
        return this.listEmotionSpr[idx] || null;
    }

    actGoToPoker(idGame) {


        App.instance.showLoading(true);
        App.instance.openPrefabGame("Poker", "Poker", (bundle, prefab) => {
            // App.instance.showLoading(true);
            CardGameSignalRClient.getInstance().connectHub(idGame, (success) => {
                App.instance.showLoading(false);
                if (success) {
                    this.nodeRoom.removeAllChildren();
                    let cb = (prefab) => {
                        let popupDaily = instantiate(prefab)
                            .getComponent("RoomCards");
                        this.nodeRoom.addChild(popupDaily.node);
                        popupDaily.setDataRoom(idGame);

                    };
                    BundleControl.loadPrefabPopup("prefabs/RoomCards", cb);
                } else {
                    App.instance.showErrLoading(App.instance.getTextLang("me11"));
                }

            });
        });
    }

    actGoToTLMN(idGame) {


        App.instance.showLoading(true);
        App.instance.openPrefabGame("TienLen", "TienLen", (bundle, prefab) => {
            // App.instance.showLoading(true);
            CardGameSignalRClient.getInstance().connectHub(idGame, (success) => {
                App.instance.showLoading(false);
                if (success) {
                    this.nodeRoom.removeAllChildren();
                    let cb = (prefab) => {
                        let popupDaily = instantiate(prefab)
                            .getComponent("RoomCards");
                        this.nodeRoom.addChild(popupDaily.node);
                        popupDaily.setDataRoom(idGame);

                    };
                    BundleControl.loadPrefabPopup("prefabs/RoomCards", cb);
                } else {
                    App.instance.showErrLoading(App.instance.getTextLang("me11"));
                }

            });
        });
    }

    openPrefabGame(bundleName: string, sceneName: string, callback: (prefab: Prefab, bundle: AssetManager.Bundle) => void) {
        BundleControl.loadPrefabGame(
            bundleName,
            sceneName,
            (finish, total) => {
                this.showLoadingProcess(true);
                const percent = Math.floor((finish / total) * 100);
                this.lblStatus.string = `${percent}%`;
                const progress = finish / total;
                this.spriteProgress.fillRange = progress;
                this.nodeSlider.progress = progress;
            },
            (prefab, bundle) => {
                this.showLoadingProcess(false);
                callback(prefab, bundle);
            }
        );
    }

    openSceneGame(bundleName: string, sceneName: string, callback: () => void) {
        SignalRClient.closeHubMiniGameAll();

        BundleControl.loadSceneGame(
            bundleName,
            sceneName,
            (finish, total) => {
                this.showLoadingProcess(true);
                const percent = Math.floor((finish / total) * 100);
                this.lblStatus.string = `${percent}%`;
                const progress = finish / total;
                this.spriteProgress.fillRange = progress;
                this.nodeSlider.progress = progress;
            },
            () => {
                const root2d = director.getScene().getChildByName("RenderRoot2D");
                if (root2d) {
                    this.node.parent = root2d;
                }
                this.showLoadingProcess(false);
                callback();
            }
        );
    }

    actVQMM() {
        Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], {type: 0}, (status, res) => {
            if (status === 200 && res.c == 0) {
                // console.log(res.d);
                this.checkTurnVQMM();
                BundleControl.loadPrefabPopup("prefabs/PopupSpinWheel", (prefab: any) => {
                    let popup = instantiate(prefab).getComponent("PopupSpinWheel");
                    App.instance.miniGameNode.addChild(popup.node);
                    popup.show();
                });
            }
        });
    }

    checkTurnVQMM() {
        Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], {type: 0}, (status, res) => {
            if (status === 200 && res.c == 0 && res.d > 0) {
                Global.LobbyController.turnVQMM.active = true;
                Global.LobbyController.turnVQMM.getChildByName("number").getComponent(Label).string = res.d.toString();
                Global.LobbyController.turnVQMMPanelMiniGame.active = true;
                Global.LobbyController.turnVQMMPanelMiniGame.getChildByName("number").getComponent(Label).string = res.d.toString();
            } else {
                Global.LobbyController.turnVQMM.active = false;
                Global.LobbyController.turnVQMMPanelMiniGame.active = false;
            }
        });
    }

    addGameToNode(game: any) {
        let node = instantiate(game);
        this.bigGameNode.addChild(node);
    }

    resetAllGameActive() {
        // MiniGame
        App.instance.taiXiuJackpotMiniGame?.dismiss();
        App.instance.taiXiuJackpotMiniGame = null;
        App.instance.taiXiuMD5MiniGame?.dismiss();
        App.instance.taiXiuMD5MiniGame = null;
        App.instance.hiLoMiniGame?.dismiss();
        App.instance.hiLoMiniGame = null;
        App.instance.miniPoker?.dismiss();
        App.instance.miniPoker = null;
        App.instance.phucSinhMiniGame?.dismiss();
        App.instance.phucSinhMiniGame = null;
        App.instance.bauCuaMiniGame?.dismiss();
        App.instance.bauCuaMiniGame = null;
        App.instance.luckyWildMiniGame?.dismiss();
        App.instance.luckyWildMiniGame = null;

        // BigGame
        App.instance.bigGameNode?.removeAllChildren();
        App.instance.slotGameNode?.removeAllChildren();
        App.instance.miniGameNode?.removeAllChildren();
        App.instance.nodeRoom?.removeAllChildren();
        App.instance.slotGame = {};

        // Lobby
        App.instance.popupNode?.removeAllChildren();
        App.instance.tipzoJackpotEventX2X6Node?.removeAllChildren();
        App.instance.tipzoMiniLiveNode?.removeAllChildren();
    }

    actGameLuckyWild() {
        this.showLoading(true);
        MiniGameSignalRClient.getInstance().connectHub((success) => {
            this.showLoading(false);
            if (success) {
                if (this.luckyWildMiniGame == null || this.luckyWildMiniGame.node == null) {
                    console.log(" crarete ========= 1");
                    this.openPrefabGame("Slot3x3", "Slot3x3", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.luckyWildMiniGame = node.getComponent(MiniGame);
                        this.luckyWildMiniGame.show();
                    });
                } else {
                    console.log(" dung lai ========= 1");
                    if (this.luckyWildMiniGame.node.parent === null) {
                        console.log(" dung lai ========= 2");
                        this.luckyWildMiniGame.node.parent = this.miniGameNode;
                    }
                    this.luckyWildMiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGameTaiXiuJackpot(isLive: boolean = false) {
        const showError = () => {
            App.instance.showLoading(false);
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    success ? resolve() : reject();
                });
            });
        };
        App.instance.showLoading(true);
        connectHub(MiniGameTXSignalRClient.getInstance())
            .then(() => connectHub(MiniGameSignalRClient.getInstance()))
            .then(() => {
                App.instance.showLoading(false);
                eventBus.emit("TX_CONNECTED");
                if (isLive) {
                    this.openPrefabGame("TaiXiuLive", "TaiXiuLive", (prefab: any) => {
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        node.removeComponent("TaiXiuLiveMD5.Controller");
                        this.bigGameNode.addChild(node);
                    });

                    return;
                }

                if (this.taiXiuJackpotMiniGame == null || this.taiXiuJackpotMiniGame.node == null) {
                    this.openPrefabGame("TaiXiuDouble", "TaiXiuDouble", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.taiXiuJackpotMiniGame = node.getComponent(MiniGame);
                        this.taiXiuJackpotMiniGame.show();
                    });
                } else {
                    if (this.taiXiuJackpotMiniGame.node.parent === null) {
                        this.taiXiuJackpotMiniGame.node.parent = this.miniGameNode;
                    }
                    this.taiXiuJackpotMiniGame.show();
                }
            })
            .catch(showError);
    }

    actGameTaiXiuMD5(isLive: boolean = false) {
        App.instance.showLoading(true);
        MiniGameTXMD5SignalRClient.getInstance().connectHub((success: Function) => {
            App.instance.showLoading(false);
            if (success) {
                if (isLive) {
                    this.openPrefabGame("TaiXiuLive", "TaiXiuLive", (prefab: any) => {
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        node.removeComponent("TaiXiuLiveJP.Controller");
                        this.bigGameNode.addChild(node);
                    });
                    return;
                }

                if (this.taiXiuMD5MiniGame == null || this.taiXiuMD5MiniGame.node == null) {
                    this.openPrefabGame("TaiXiuMD5", "TaiXiuMD5", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.taiXiuMD5MiniGame = node.getComponent(MiniGame);
                        this.taiXiuMD5MiniGame.show();
                    });
                } else {
                    if (this.taiXiuMD5MiniGame.node.parent === null) {
                        this.taiXiuMD5MiniGame.node.parent = this.miniGameNode;
                    }
                    this.taiXiuMD5MiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actXoSoMiniGame() {
        BundleControl.loadPrefabPopup("prefabs/PopupXoSo", (prefab: any) => {
            let popup = instantiate(prefab).getComponent("PopupMiniGameXoSo");
            App.instance.miniGameNode.addChild(popup.node);
            popup.show();
        });
    }

    actGameHiLo() {
        this.showLoading(true);
        MiniGameSignalRClient.getInstance().connectHub((success) => {
            this.showLoading(false);
            if (success) {
                if (this.hiLoMiniGame == null || this.hiLoMiniGame.node == null) {
                    this.openPrefabGame("Cao_Thap", "Cao_Thap", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.hiLoMiniGame = node.getComponent(MiniGame);
                        this.hiLoMiniGame.show();
                    });
                } else {
                    if (this.hiLoMiniGame.node.parent === null) {
                        this.hiLoMiniGame.node.parent = this.miniGameNode;
                    }
                    this.hiLoMiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGameMiniPoker() {
        this.showLoading(true);
        MiniGameSignalRClient.getInstance().connectHub((success) => {
            this.showLoading(false);
            if (success) {
                if (this.miniPoker == null || this.miniPoker.node == null) {
                    this.openPrefabGame("MiniPoker", "MiniPoker", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.miniPoker = node.getComponent(MiniGame);
                        this.miniPoker.show();
                    });
                } else {
                    if (this.miniPoker.node.parent === null) {
                        this.miniPoker.node.parent = this.miniGameNode;
                    }
                    this.miniPoker.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGameBauCua() {
        this.showLoading(true);
        MiniGameSignalRClient.getInstance().connectHub((success) => {
            this.showLoading(false);
            if (success) {
                if (this.bauCuaMiniGame == null || this.bauCuaMiniGame.node == null) {
                    this.openPrefabGame("BauCua", "BauCua", (prefab: any, bundle: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.bauCuaMiniGame = node.getComponent(MiniGame);
                        node.getComponent("BauCua.BauCuaController").baucuaBundle = bundle;
                        this.bauCuaMiniGame.show();
                    });
                } else {
                    if (this.bauCuaMiniGame.node.parent === null) {
                        this.bauCuaMiniGame.node.parent = this.miniGameNode;
                    }
                    this.bauCuaMiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGamePhucSinh() {
        this.showLoading(true);
        MiniGameSignalRClient.getInstance().connectHub((success) => {
            this.showLoading(false);
            if (success) {
                if (this.phucSinhMiniGame == null || this.phucSinhMiniGame.node == null) {
                    this.openPrefabGame("PhucSinh", "PhucSinh", (prefab: any) => {
                        let node = instantiate(prefab);
                        node.parent = this.miniGameNode;
                        node.active = false;
                        this.phucSinhMiniGame = node.getComponent(MiniGame);
                        this.phucSinhMiniGame.show();
                    });
                } else {
                    if (this.phucSinhMiniGame.node.parent === null) {
                        this.phucSinhMiniGame.node.parent = this.miniGameNode;
                    }
                    this.phucSinhMiniGame.show();
                }
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actXocDiaLive(isMD5: boolean = false) {
        App.instance.showLoading(true);
        XocDiaLiveSignalRClient.getInstance().connectHub(isMD5, (success: Function) => {
            if (!success) {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
                return;
            }
            App.instance.showLoading(false);
            this.openPrefabGame("CasinoLive", "XocDiaLive", (prefab) => {
                App.instance.bigGameNode.removeAllChildren();
                let node = instantiate(prefab);
                // @ts-ignore
                node.getComponent("XocDiaLiveController").setupController(isMD5);
                node.active = true;
                App.instance.bigGameNode.addChild(node);
            });
        });
    }

    actRongHoLive() {
        App.instance.showLoading(true);
        DragonTigerSignalRClient.getInstance().connectHub((success: Function) => {
            if (!success) {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
                return;
            }
            App.instance.showLoading(false);
            this.openPrefabGame("CasinoLive", "RongHoLive", (prefab) => {
                App.instance.bigGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                App.instance.bigGameNode.addChild(node);
            });
        });
    }

    actRouletteLive() {
        App.instance.showLoading(true);
        RouletteLiveSignalRClient.getInstance().connectHub((success: Function) => {
            if (!success) {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
                return;
            }
            App.instance.showLoading(false);
            this.openPrefabGame("CasinoLive", "RouletteLive", (prefab) => {
                App.instance.bigGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                App.instance.bigGameNode.addChild(node);
            });
        });
    }

    actOpenVirtualSports() {
        this.showLoading(true, -1);
        BundleControl.loadPrefabPopup("prefabs/SportVirtual/SportVirtual", (prefab: any) => {
            this.showLoading(false);
            let popup = instantiate(prefab).getComponent("SportVirtualController");
            App.instance.bigGameNode.addChild(popup.node);
        });
    }

    actCardGame(gameId: number) {
        this.nodeRoom.removeAllChildren();

        App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub(gameId, (success: any) => {
            App.instance.showLoading(false);
            if (success) {
                App.instance.showLoading(true);
                BundleControl.loadPrefabPopup("prefabs/RoomCards", (prefab: any) => {
                    App.instance.showLoading(false);
                    const roomCard = instantiate(prefab).getComponent("RoomCards");
                    this.nodeRoom.addChild(roomCard.node);
                    roomCard.setDataRoom(gameId);
                });
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    actGoToCasino(gameId: number) {
        var hub: SignalRClient;
        switch (gameId) {
            case Configs.InGameIds.Baccarat:
                hub = BaccaratSignalRClient.getInstance();
                break;
            case Configs.InGameIds.BlackJack:
                hub = BlackjackSignalRClient.getInstance();
                break;
            case Configs.InGameIds.RongHo:
                hub = DragonTigerSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Roullete:
                hub = RouletteSignalRClient.getInstance();
                break;
            case Configs.InGameIds.Sicbo:
                hub = SicboSignalRClient.getInstance();
                break;
            case Configs.InGameIds.XocDia:
                hub = SedieSignalRClient.getInstance();
                break;
        }

        App.instance.showLoading(true);
        // @ts-ignore
        hub.connectHub((success: Function) => {
            App.instance.showLoading(false);
            if (success) {
                this.actGoToCasinoGame(gameId);
            } else {
                App.instance.showErrLoading(App.instance.getTextLang("me11"));
            }
        });
    }

    private actGoToCasinoGame(gameID: number) {
        this.nodeRoom.removeAllChildren();
        BundleControl.loadPrefabPopup("prefabs/Casino/Lobby", (prefab: any) => {
            const popupCasino = instantiate(prefab).getComponent("Casino.Lobby");
            this.nodeRoom.addChild(popupCasino.node);
            popupCasino.setDataRoom(gameID);
        });
    }

    private actGoToSport() {
        this.openPrefabGame("Sport", "Sport", (prefab) => {
            this.showLoading(false);
            this.bigGameNode.removeAllChildren();
            let node = instantiate(prefab);
            node.active = true;
            this.bigGameNode.addChild(node);
        });
    }

    private actGameXuatKich() {
        BundleControl.loadPrefabGame(
            "XuatKich",
            "XuatKich",
            (finish, total) => {
                // this.showXuatKichLoadingProcess(true);
                // this.xuatKichSpriteProgress.fillRange = (finish / total);
                // this.xuatKichNodeSlider.progress = this.xuatKichSpriteProgress.fillRange;
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                // this.showXuatKichLoadingProcess(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameLongVuong() {
        BundleControl.loadPrefabGame(
            "LongVuong",
            "LongVuong",
            (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGamePhiDoi() {
        this.openSceneGame("SpaceWar", "scenes/SpaceWar", () => {
        });
    }

    private actGameCaMap() {
        BundleControl.loadPrefabGame(
            "TheShark",
            "prefabs/TheShark",
            (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameCaKiem() {
        BundleControl.loadPrefabGame(
            "FishHunter",
            "prefabs/FishHunter",
            (finish, total) => {
                this.showLoading(true, -1);
            },
            (prefab, _bundle) => {
                this.showLoading(false);
                this.skillsGameNode.removeAllChildren();
                let node = instantiate(prefab);
                node.active = true;
                this.skillsGameNode.addChild(node);
            }
        );
    }

    private actGameVuongQuoc() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.Kingdom]) {
            log("KHOA");
            const hiddenNode = this.slotGame[Configs.InGameIds.Kingdom];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(KingdomSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "Kingdom",
                    "Kingdom",
                    (finish, total) => {
                        this.vuongQuocLoadingProgress.active = true;
                        this.vuongQuocLoadingProgress.setSiblingIndex(this.node.children.length - 1);
                        this.vuongQuocSpriteProgress.fillRange = finish / total;
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.vuongQuocLoadingProgress.active = false;
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.Kingdom] = node;
                    }
                );
            })
            .catch(showError);
    }

    private actGameSamTruyen() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.SamTruyen]) {
            const hiddenNode = this.slotGame[Configs.InGameIds.SamTruyen];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            hiddenNode.getComponent(UIOpacity).opacity! = 255;
            hiddenNode.getComponent(BlockInputEvents).enabled! = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(SamTruyenNetWorkSignalR.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "SamTruyen",
                    "SamTruyen",
                    (finish, total) => {
                        // @TODO with global loading
                        this.showLoading(true, 0);
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.SamTruyen] = node;
                    }
                );
            })
            .catch(showError);
    }

    private actGameSoDo() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(SoDoSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "SoDo",
                    "SoDo",
                    (finish, total) => {
                        // @TODO with global loading
                        this.showLoading(true, 0);
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        this.bigGameNode.addChild(node);
                    }
                );
            })
            .catch(showError);
    }

    private actGameThuyCung() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.ThuyCung]) {
            const hiddenNode = this.slotGame[Configs.InGameIds.ThuyCung];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };
        connectHub(OceanSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "Ocean",
                    "Ocean",
                    (finish, total) => {
                        this.thuyCungLoadingProgress.active = true;
                        this.thuyCungLoadingProgress.setSiblingIndex(this.node.children.length - 1);
                        this.thuyCungSpriteProgress.fillRange = finish / total;
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.thuyCungLoadingProgress.active = false;
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.ThuyCung] = node;
                    }
                );
            })
            .catch(showError);
    }

    private actGameGaiNhay() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.GaiNhay]) {
            const hiddenNode = this.slotGame[Configs.InGameIds.GaiNhay];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(DQSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "DancingQueen",
                    "DancingQueen",
                    (finish, total) => {
                        this.gaiNhayLoadingProgress.active = true;
                        this.gaiNhayLoadingProgress.setSiblingIndex(this.node.children.length - 1);
                        this.gaiNhaySpriteProgress.fillRange = finish / total;
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.gaiNhayLoadingProgress.active = false;
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.GaiNhay] = node;
                    }
                );
            })
            .catch(showError);
    }

    private actGameThanTai() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.ThanTai]) {
            const hiddenNode = this.slotGame[Configs.InGameIds.ThanTai];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(ThanTaiSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "ThanTai",
                    "ThanTai",
                    (finish, total) => {
                        this.showLoading(true, 0);
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.ThanTai] = node;
                    }
                );
            })
            .catch(showError);
    }

    private actGameVuTruong() {
        // TODO
    }

    private actGameRungVang() {
        const showError = () => {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
        };

        if (this.slotGame[Configs.InGameIds.RungVang]) {
            const hiddenNode = this.slotGame[Configs.InGameIds.RungVang];
            this.bigGameNode.addChild(hiddenNode);
            hiddenNode.active = true;
            return;
        }

        const connectHub = (client: { connectHub: (cb: (success: boolean) => void) => void }): Promise<void> => {
            return new Promise((resolve, reject) => {
                client.connectHub((success: boolean) => {
                    App.instance.showLoading(false);
                    success ? resolve() : reject();
                });
            });
        };

        connectHub(ForestSignalRClient.getInstance())
            .then(() => {
                BundleControl.loadPrefabGame(
                    "Forest",
                    "Forest",
                    (finish, total) => {
                        this.rungVangLoadingProgress.active = true;
                        this.rungVangLoadingProgress.setSiblingIndex(this.node.children.length - 1);
                        this.rungVangSpriteProgress.fillRange = finish / total;
                    },
                    (prefab) => {
                        this.showLoading(false);
                        this.rungVangLoadingProgress.active = false;
                        this.bigGameNode.removeAllChildren();
                        let node = instantiate(prefab);
                        node.active = true;
                        this.bigGameNode.addChild(node);
                        this.slotGame[Configs.InGameIds.RungVang] = node;
                    }
                );
            })
            .catch(showError);
    }

    public showToast(msg: string) {
        this.alertToast.active = true;
        this.alertToast.getComponent(Label).string = msg;

        Tween.stopAllByTarget(this.alertToast);
        tween(this.alertToast)
            .set({position: this.alertToast.position.set(this.alertToast.position.x, 0, 0)})
            .to(2.0, {position: this.alertToast.position.set(this.alertToast.position.x, 100, 0)}, {easing: easing.sineOut})
            .call(() => {
                this.alertToast.active = false;
            })
            .start();
    }

    public setActiveGameById(gameId: number, active: boolean) {
        const nodes = this.gameNodeMap.get(gameId);
        if (nodes) {
            nodes.forEach((node) => (node.active = active));
        }
    }

    private actGameMegaMillions() {
        this.isMegaMillions = true;
        this.openPrefabGame("Lottery", "MegaMillions/MegaPrefab", (prefab) => {
            this.bigGameNode.removeAllChildren();
            let node = instantiate(prefab);
            node.active = true;
            this.bigGameNode.addChild(node);
        });
    }

    private actGameKeno() {
        this.openPrefabGame("Lottery", "Keno/KenoPrefab", (prefab) => {
            this.bigGameNode.removeAllChildren();
            let node = instantiate(prefab);
            node.active = true;
            this.bigGameNode.addChild(node);
        });
    }

    private actGameUSPowerBall() {
        this.isMegaMillions = false;
        this.openPrefabGame("Lottery", "USPowerBall/USPrefab", (prefab) => {
            this.bigGameNode.removeAllChildren();
            let node = instantiate(prefab);
            node.active = true;
            this.bigGameNode.addChild(node);
        });
    }

    private actGamePokerTour() {
        this.openPrefabGame("PokerTour", "PokerTour", (prefab) => {
            this.bigGameNode.removeAllChildren();
            let node = instantiate(prefab);
            node.active = true;
            this.bigGameNode.addChild(node);
        });
    }
}
