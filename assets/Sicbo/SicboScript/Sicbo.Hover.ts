import { _decorator, Component, Node, TextAsset, Label, game, sys, Vec3, UITransform } from "cc";
import CasinoLobby from "db://assets/Lobby/scripts/common/casino/Casino.Lobby";
import SicboPlay from "db://assets/Sicbo/SicboScript/Sicbo.Play";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Configs from "db://assets/Lobby/scripts/common/Config";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Sicbo/Hover")
export default class SicboHover extends Component {
    @property(TextAsset)
    json: TextAsset = null;
    @property(Node)
    betPositions: Node = null;
    @property(Node)
    infoNode: Node = null;

    private texts: any[] = [];

    onLoad() {
        this.texts = JSON.parse(this.json.text);
        this.betPositions.children.forEach((node: Node) => {
            const gateId = parseInt(node.name);
            if (isNaN(gateId)) return;

            const item = this.texts.find((item: any) => item.id === gateId);
            const hoverChild = CasinoLobby.instance.isTableVip ? node.getChildByName("hover") : node.getChildByName("hoverNormal");

            node.on(Node.EventType.MOUSE_ENTER, () => {
                game.canvas.style.cursor = "pointer";
                if (hoverChild) hoverChild.active = true;

                const me_bets = SicboPlay.instance?.betAmountLogs.filter(
                    (bet) => bet.gate === gateId && bet.accountId === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`
                );
                const me_bet_amount = me_bets ? me_bets.reduce((sum, bet) => sum + bet.amount, 0) : 0;

                const total_bets = SicboPlay.instance?.betAmountLogs.filter(
                    (bet) => bet.gate === gateId
                );
                const total_bet_amount = total_bets ? total_bets.reduce((sum, bet) => sum + bet.amount, 0) : 0;

                var lang = sys.localStorage.getItem("locale") || "vi";

                this.infoNode.getChildByName("description").getComponent(Label).string = lang === "vi" ? item.description : (item["description_" + lang] || item.description);
                this.infoNode.getChildByName("fund_rate").getComponent(Label).string = `1:${item.fund_rate}`;
                this.infoNode.getChildByName("max_factor").getComponent(Label).string = Utils.formatNumber(item.max_factor * (SicboPlay.instance?.minBet || 0));
                this.infoNode.getChildByName("me_bet").getComponent(Label).string = Utils.formatMoney(me_bet_amount, true);
                this.infoNode.getChildByName("total_bet").getComponent(Label).string = Utils.formatMoney(total_bet_amount, true);
                const worldPos = node.getComponent(UITransform).convertToWorldSpaceAR(Vec3.ZERO);
                const localPos = this.infoNode.parent.parent.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                this.infoNode.setPosition(localPos.x + 180, localPos.y - 120);
                this.infoNode.active = true;
            });

            node.on(Node.EventType.MOUSE_LEAVE, () => {
                if (hoverChild) hoverChild.active = false;
                game.canvas.style.cursor = "default";
                this.infoNode.active = false;

            });
        });
    }
}
