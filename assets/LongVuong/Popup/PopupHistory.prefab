[{"__type__": "cc.Prefab", "_name": "PopupHistory", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false, "persistent": false}, {"__type__": "cc.Node", "_name": "PopupHistory", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}], "_active": true, "_components": [{"__id__": 384}, {"__id__": 386}, {"__id__": 388}, {"__id__": 390}, {"__id__": 392}], "_prefab": {"__id__": 394}, "_lpos": {"__type__": "cc.Vec3", "x": 960, "y": 540, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34tG3+yehGrbmVlW7Mkw63"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44vXneLztK7685kwv9z8GD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 128}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ckCwp+UBEWpzpsDih7Oas"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffdmn8kE5FsqlkjEu5K/zQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dJ0/i/U5JXb+OZcSM6wYL"}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 26}, {"__id__": 38}, {"__id__": 51}, {"__id__": 64}, {"__id__": 87}, {"__id__": 110}, {"__id__": 133}, {"__id__": 155}, {"__id__": 177}, {"__id__": 199}, {"__id__": 339}], "_active": true, "_components": [{"__id__": 377}, {"__id__": 379}, {"__id__": 381}], "_prefab": {"__id__": 383}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "BT Close_", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 18}, {"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 660.41, "y": 351.578, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "aec9808d-7cfe-4ea5-9ffc-34649402d685@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c80zXghZxN+5GbyVuBSNag"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": 31.590000000000032, "_top": 64.422, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edFVbr9hxESaPHwswv/QSZ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "clickEvents": [{"__id__": 20}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "aec9808d-7cfe-4ea5-9ffc-34649402d685@f9941"}, "_hoverSprite": {"__uuid__": "631633b9-1986-42d2-9a2b-71cad71248e3@f9941"}, "_pressedSprite": {"__uuid__": "044287db-1e1d-48b5-838c-bcc7f5d05826@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "c7365ee4-ce90-43f9-9cd4-ca5606269343"}, "hoverSprite": {"__uuid__": "fd1b8284-5ac3-4e3a-8c09-6fc3713608ef"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4mLJkzmhNq4KMKb9zrxBk"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 22}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cveppOaFAdIsB0JCuC/t8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 24}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 133, "height": 137}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7+fFSUcNAE6VdrLQpE80S"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "de//2U9I1MgKRiXsffkMuW"}, {"__type__": "cc.Node", "_name": "TITLE", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 27}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 416.619, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 28}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON> s<PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 92.12, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 92.12, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -5}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "656vXgVRdBLaMelmbsRK2R"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 30}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5SR5SmwdLl6IfhHbVjDZB"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "id": "TITLE_LOG", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 32}}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dpNsnG8NDaZm5biPWVaLZ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 34}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbgME7E21HM7GIddi0chIh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 36}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 332.9, "height": 115.92}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0042kehbdOL5WxxpzQX0M8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dbRM8C4HRDwKna8FJa2XPS"}, {"__type__": "cc.Node", "_name": "BT Back_BT", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 39}, {"__id__": 41}, {"__id__": 43}, {"__id__": 46}, {"__id__": 48}], "_prefab": {"__id__": 50}, "_lpos": {"__type__": "cc.Vec3", "x": -768, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 40}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4bdfa683-557f-4fdf-8102-d9d416a8dff9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00OPuXFWRG6LE70doEGHyi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 42}, "_alignFlags": 8, "_target": null, "_left": -60, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97Zma2kzlHepXFjtIh+RqQ"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 44}, "clickEvents": [{"__id__": 45}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "4bdfa683-557f-4fdf-8102-d9d416a8dff9@f9941"}, "_hoverSprite": {"__uuid__": "c42adc4b-a6c8-4e15-9708-9375a6b553e8@f9941"}, "_pressedSprite": {"__uuid__": "2e275c1c-de06-4cb2-a5f6-accc261895fd@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "e9f5d121-c66c-421d-bd5b-ba48fb4643cd"}, "hoverSprite": {"__uuid__": "5964430d-f3fb-4cb1-b3b2-4fc031fe07b3"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5JFTeRpVBaLRWiHK/WmuV"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "prevPage", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 47}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1QbfBto9HNbOcV8wmKS2/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 49}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "2a1BdR+v9D9ags6+vfRfY3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beoJMsYuVMKJOzuFXpZxSM"}, {"__type__": "cc.Node", "_name": "BT NextBT", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 52}, {"__id__": 54}, {"__id__": 56}, {"__id__": 59}, {"__id__": 61}], "_prefab": {"__id__": 63}, "_lpos": {"__type__": "cc.Vec3", "x": 768, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 53}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "042cfe2b-7cfa-4c23-a2ee-acae6d3a11c0@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dluKVuRBH7I0q+uF+yRep"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 55}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": -60, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c+jwDVbhIa5SpPm6EOgiH"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 57}, "clickEvents": [{"__id__": 58}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "042cfe2b-7cfa-4c23-a2ee-acae6d3a11c0@f9941"}, "_hoverSprite": {"__uuid__": "53d459ec-9855-4d27-928e-b7d8f6744603@f9941"}, "_pressedSprite": {"__uuid__": "64f14453-b82e-455f-a7c5-427b4a4dd513@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "89b63e56-7001-44cb-9352-d43413f47408"}, "hoverSprite": {"__uuid__": "52e83cca-9222-41b7-aa45-b728b5ff20a0"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68rajVxftGAp7TfxrGUCs1"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "nextPage", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 60}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "49A4zKg+ROLI2DCIaREjIA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "__prefab": {"__id__": 62}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 101, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "30VOv9HQ9GzrD0RZzIlQU9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "36UNyW77RCYaX0PeyNH5uj"}, {"__type__": "cc.Node", "_name": "BTPlayInactive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 65}], "_active": true, "_components": [{"__id__": 77}, {"__id__": 79}, {"__id__": 82}, {"__id__": 84}], "_prefab": {"__id__": 86}, "_lpos": {"__type__": "cc.Vec3", "x": -300, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 66}, {"__id__": 68}, {"__id__": 70}, {"__id__": 72}, {"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 67}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Chơi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eLEzZQEhMtopMFQkjOBlU"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 69}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7ahhITKhLSaa6f4evZTtW"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "id": "fish_pu20", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 71}}, {"__type__": "cc.CompPrefabInfo", "fileId": "31kyi9eQBD1ZQ3YRbm5M0S"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 73}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "e37St73x5Pebjlrjk2PAit"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "__prefab": {"__id__": 75}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 73.62, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6nFkMwoxFDIiL1doRBQ28"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6VTm3J3RNn7KKJlw9zk0P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 78}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dKbsdXFNB5KfIjisLvZr+"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 80}, "clickEvents": [{"__id__": 81}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_hoverSprite": {"__uuid__": "92a1beff-0c94-44cc-8a7e-c8a3cd185201@f9941"}, "_pressedSprite": {"__uuid__": "61373be7-3fc9-4fc3-9df8-501b3f0432f3@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "30569d54-2d44-4d0a-9a03-34b66d99915a"}, "hoverSprite": {"__uuid__": "ee52d6b1-6313-405a-83e6-a2e4aabcf421"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66gxzlz1BFBoxWHv1k+Ve4"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "toggleTab", "customEventData": "0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 83}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fOKtMBUlAv7FS7dl+MZYA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 85}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "13i1rJKjNLhIdLvYFPChHD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9hO/cAhZKj4U3txlvx0wN"}, {"__type__": "cc.Node", "_name": "BTCashInInactive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 88}], "_active": true, "_components": [{"__id__": 100}, {"__id__": 102}, {"__id__": 105}, {"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [{"__id__": 89}, {"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 90}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Chơi", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "18Y+7N3NlCCqWjoUDykWE1"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 92}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1zH8UdGJMIpgwu13MmgGq"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "id": "fish_pu18", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 94}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fBAJCZANFGqenjtrI8oWt"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 96}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "002sNiKSlBN7Ez8DD+DXoj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "__prefab": {"__id__": 98}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 73.62, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fwE75qw5E/rfCMSShEDb7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "83KPXb0NJImbmpr9dhig8F"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 101}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0ad8QlXklNJJ8J/JzZ8UG1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 103}, "clickEvents": [{"__id__": 104}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_hoverSprite": {"__uuid__": "92a1beff-0c94-44cc-8a7e-c8a3cd185201@f9941"}, "_pressedSprite": {"__uuid__": "61373be7-3fc9-4fc3-9df8-501b3f0432f3@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "30569d54-2d44-4d0a-9a03-34b66d99915a"}, "hoverSprite": {"__uuid__": "ee52d6b1-6313-405a-83e6-a2e4aabcf421"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "841pYIzOhFHb85LyGmpOpJ"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "toggleTab", "customEventData": "1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 106}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "81+BEjtUNCwqPOXA750kQd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 87}, "_enabled": true, "__prefab": {"__id__": 108}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "67O+Xf9ZlIOY/DDq7hEUdv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "53L2+lh3pCv6+6iLXaUtoz"}, {"__type__": "cc.Node", "_name": "BTCashoutInactive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 111}], "_active": true, "_components": [{"__id__": 123}, {"__id__": 125}, {"__id__": 128}, {"__id__": 130}], "_prefab": {"__id__": 132}, "_lpos": {"__type__": "cc.Vec3", "x": 300, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 114}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 113}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Đổi Gold", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fPgQ4OhtJkLeL+xyeUcTK"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 115}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbHMr67oxGg4Ypd3LFpuMc"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "id": "fish_pu19", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 117}}, {"__type__": "cc.CompPrefabInfo", "fileId": "50F+Z4G6dCxIgAOhdOE+Vs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 119}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "03ptkKyplLeZNZ7MVoynDY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "__prefab": {"__id__": 121}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 132.1, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "45drOyaT5Mc5XLUzfpK9DB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "07CvvMdOtDkqgbRRyeJPBp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 124}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7FRWUlIdOMJmb1yxfUY1M"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 126}, "clickEvents": [{"__id__": 127}], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "79285c35-63be-4217-9605-d259c9a51f3e@f9941"}, "_hoverSprite": {"__uuid__": "92a1beff-0c94-44cc-8a7e-c8a3cd185201@f9941"}, "_pressedSprite": {"__uuid__": "61373be7-3fc9-4fc3-9df8-501b3f0432f3@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "30569d54-2d44-4d0a-9a03-34b66d99915a"}, "hoverSprite": {"__uuid__": "ee52d6b1-6313-405a-83e6-a2e4aabcf421"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5acjwQvvZLa6nxEdfMl+wY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "03f10sDB/NLhJEWtE5Egnku", "handler": "toggleTab", "customEventData": "2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 129}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "02ZlaYCntCI5y+nYqfi2Hs"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 131}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "592WxrGfJG/KmG54Rft3zO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e9ayNhFhKu5EtIWTdVuRt"}, {"__type__": "cc.Node", "_name": "BTCashinActive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 134}], "_active": true, "_components": [{"__id__": 146}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}], "_prefab": {"__id__": 154}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 133}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}, {"__id__": 139}, {"__id__": 141}, {"__id__": 143}], "_prefab": {"__id__": 145}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Nạp Gold", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "97110ltppG5rjDcBHT/gcB"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84m/peUrBGHZ4MkkYPgHop"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "id": "fish_pu18", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 140}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cfQKCdbdLiK7vdQjou6OC"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 142}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fVPypSqBJ5qFGfrSRTXkQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 144}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 140.93, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "55FiR8ed5HL73u25sAI9st"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4dwuCBTDBHH7C6SarvTYLC"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 147}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14BpU+dOJGA4SzlUNRR7ys"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 149}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_hoverSprite": {"__uuid__": "8ee207b1-86fe-4e23-ab72-b332e46abc9c@f9941"}, "_pressedSprite": {"__uuid__": "861646a1-727d-4803-9c93-1e78f0c73b30@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "b9e3ed3b-2e40-4421-a713-6f95e7cacb53"}, "hoverSprite": {"__uuid__": "f6386546-69bd-4f88-9e94-43f742d5e69e"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deQPTzg/xAtLrjvm/AHTUj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 151}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "58UiVehF9GQ7AqBDkWuei6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": true, "__prefab": {"__id__": 153}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ee7V0RLM9D37tjzq1QAauA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "894ZM4D2FPr5C1iM4F4ePm"}, {"__type__": "cc.Node", "_name": "BTCashoutActive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 156}], "_active": false, "_components": [{"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 300, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 155}, "_children": [], "_active": true, "_components": [{"__id__": 157}, {"__id__": 159}, {"__id__": 161}, {"__id__": 163}, {"__id__": 165}], "_prefab": {"__id__": 167}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 158}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Đổi Gold", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "31PAZkaBdL8KdPkPjEIMS8"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 160}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62/vSwbEdMC4CH+mhp3bRV"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "id": "fish_pu19", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 162}}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaqa0o2wpODqbJhSMe9g8N"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 164}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "73MmabpN9JGbcpW9Uw35JI"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 156}, "_enabled": true, "__prefab": {"__id__": 166}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 132.1, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eJkCydsFGs47u161WvG5X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47h3IsZ2BGtIB9tLdZUwwY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "__prefab": {"__id__": 169}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09Bga15O5EpZP3YRbhMRBi"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "__prefab": {"__id__": 171}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_hoverSprite": {"__uuid__": "8ee207b1-86fe-4e23-ab72-b332e46abc9c@f9941"}, "_pressedSprite": {"__uuid__": "861646a1-727d-4803-9c93-1e78f0c73b30@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "b9e3ed3b-2e40-4421-a713-6f95e7cacb53"}, "hoverSprite": {"__uuid__": "f6386546-69bd-4f88-9e94-43f742d5e69e"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1RIO7YuxJrYpkITKfToXQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "__prefab": {"__id__": 173}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "13Ch03QrxJXIaM6OSSrBz6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 155}, "_enabled": true, "__prefab": {"__id__": 175}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "33IVuqRxhFIJEp/rXIkQkv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69IG1Jx5dCu5Otrwtca/sL"}, {"__type__": "cc.Node", "_name": "BTPlayActive", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 178}], "_active": false, "_components": [{"__id__": 190}, {"__id__": 192}, {"__id__": 194}, {"__id__": 196}], "_prefab": {"__id__": 198}, "_lpos": {"__type__": "cc.Vec3", "x": -300, "y": 260, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "_parent": {"__id__": 177}, "_children": [], "_active": true, "_components": [{"__id__": 179}, {"__id__": 181}, {"__id__": 183}, {"__id__": 185}, {"__id__": 187}], "_prefab": {"__id__": 189}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 3.446, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 180}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Play", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 31.71, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 31.71, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "80qNFVESJFdIUR+rUxQ+kH"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 182}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0lgwJPUBBhYKYcgfFXlIr"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "id": "fish_pu20", "isUpperCase": false, "useCustomFont": false, "_id": "", "__prefab": {"__id__": 184}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bGCedkTNMda5yES/IAhqk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 186}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bAKGaGOBMa4Hua5pklW7+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "__prefab": {"__id__": 188}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 65.23, "height": 39.06}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "49R5Clmj9FcLGBYGh/ac6F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afFSzNFJVF8YtxVFMyMPds"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 191}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95BkavRkxEuI0DTlPKpAuO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 193}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "59aef517-7444-4af4-abd9-fe93b81aaef9@f9941"}, "_hoverSprite": {"__uuid__": "8ee207b1-86fe-4e23-ab72-b332e46abc9c@f9941"}, "_pressedSprite": {"__uuid__": "861646a1-727d-4803-9c93-1e78f0c73b30@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "b9e3ed3b-2e40-4421-a713-6f95e7cacb53"}, "hoverSprite": {"__uuid__": "f6386546-69bd-4f88-9e94-43f742d5e69e"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6KSqDRuZJVoNPyYIS9lLs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 195}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "77DN99v39B7ZgtXX2XD/Y7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 197}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 226, "height": 113}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "32YZqtt25NSZCkxJJWdm1H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cbYRoM/RFEqN489F2tmsT"}, {"__type__": "cc.Node", "_name": "Content", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 200}, {"__id__": 270}], "_active": true, "_components": [{"__id__": 332}, {"__id__": 334}, {"__id__": 336}], "_prefab": {"__id__": 338}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -77.34699999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "header", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [{"__id__": 201}, {"__id__": 213}, {"__id__": 225}, {"__id__": 237}, {"__id__": 249}], "_active": true, "_components": [{"__id__": 261}, {"__id__": 263}, {"__id__": 265}, {"__id__": 267}], "_prefab": {"__id__": 269}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 254.49999999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 202}, {"__id__": 204}, {"__id__": 206}, {"__id__": 208}, {"__id__": 210}], "_prefab": {"__id__": 212}, "_lpos": {"__type__": "cc.Vec3", "x": -492.375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 203}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "415CY/db9EUqK8TAbsiiHr"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 205}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55xqo+zU9F9ZAErzpMfrel"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "id": "TITLE_TIME", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 207}}, {"__type__": "cc.CompPrefabInfo", "fileId": "adIpWutV5Ekp7JDJaW+L1i"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 209}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20gVqeV/xL3ZbkXhoo2yzp"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 211}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 211.97, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdz/0VBW1NMZ3K2cl6rpIg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d9L2Bb2RxEBbg9Ppj82p9e"}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 214}, {"__id__": 216}, {"__id__": 218}, {"__id__": 220}, {"__id__": 222}], "_prefab": {"__id__": 224}, "_lpos": {"__type__": "cc.Vec3", "x": -164.125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ecpMsg8BLdLn0XO0iNmP4"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 217}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "668WAIaqdLNbilaPVeVk3T"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "id": "TITLE_AMOUNT", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 219}}, {"__type__": "cc.CompPrefabInfo", "fileId": "20da76wExHCqaQGI47+6bP"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 221}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "167DLPBtNEuI/GxWf96CY6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 213}, "_enabled": true, "__prefab": {"__id__": 223}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 209.95, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cxXsAx0FCN4KMtJR2M1yw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f76Eo6XQpHMof/z319uxBa"}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 226}, {"__id__": 228}, {"__id__": 230}, {"__id__": 232}, {"__id__": 234}], "_prefab": {"__id__": 236}, "_lpos": {"__type__": "cc.Vec3", "x": 164.125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 227}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Phòng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a81sDcibVEZZHixktnQ8pg"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 229}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aa0FfNfiVOV5ZzdhtBAaGx"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "id": "TITLE_ROOM", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 231}}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6grRKKV5JjaxVWEoPb1WF"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 233}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "bb2tR8zXZLNKkM9H6uszAS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 225}, "_enabled": true, "__prefab": {"__id__": 235}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 145.1, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "13KU57Sy1Lp4fDhljBc8T9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58r8eeFr5B3bVTyqg6LwGf"}, {"__type__": "cc.Node", "_name": "3_1", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": false, "_components": [{"__id__": 238}, {"__id__": 240}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}], "_prefab": {"__id__": 248}, "_lpos": {"__type__": "cc.Vec3", "x": 262.6, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 239}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "Số dư", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "93QERNdGdHmaFSABllQcrM"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 241}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "087XzrwqJCxKhNrp3Vzslu"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "id": "fish_pu25", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 243}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6ejXl7119EZIj9tGE7NS+J"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 245}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "35zniIzxVKlr6P3no/IlaO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 237}, "_enabled": true, "__prefab": {"__id__": 247}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 145.1, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "36TNBvu8lEUpi9d7l7GRKQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1cKa2b6N1C8L3dM9ewmbMy"}, {"__type__": "cc.Node", "_name": "4", "_objFlags": 0, "_parent": {"__id__": 200}, "_children": [], "_active": true, "_components": [{"__id__": 250}, {"__id__": 252}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}], "_prefab": {"__id__": 260}, "_lpos": {"__type__": "cc.Vec3", "x": 492.375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 251}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "28r2FWPx1MA7tFvcHAjcd9"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 253}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93LeIraEhDgZszHmf3LANV"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "id": "TITLE_DESCRIPTION", "isUpperCase": false, "useCustomFont": true, "_id": "", "__prefab": {"__id__": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcMdslouVIjpkHE/B0lV98"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 257}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dIrf6S0JO5pnt+ObriT5Y"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 249}, "_enabled": true, "__prefab": {"__id__": 259}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 121.8, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "44bykvcMFC7LTiBupyMn1b"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3xhuviptNRbjIFQC3hmAg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 262}, "_alignFlags": 41, "_target": null, "_left": 2, "_right": 2, "_top": 30, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81+9l5osVDHZAa7LmrabeY"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 264}, "_resizeMode": 2, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1313, "height": 0}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78myxKzbZDC7jqa2M5QTr6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 266}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "67rKNHfKxLFIXEqzuojO6/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 200}, "_enabled": true, "__prefab": {"__id__": 268}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1313, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2FhV/DxJF5Jflp+MKvZFw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a2sUEcOmJIu6JDPMU/7Ew8"}, {"__type__": "cc.Node", "_name": "list", "_objFlags": 0, "_parent": {"__id__": 199}, "_children": [{"__id__": 271}], "_active": true, "_components": [{"__id__": 323}, {"__id__": 325}, {"__id__": 327}, {"__id__": 329}], "_prefab": {"__id__": 331}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "item", "_objFlags": 0, "_parent": {"__id__": 270}, "_children": [{"__id__": 272}, {"__id__": 282}, {"__id__": 292}, {"__id__": 302}], "_active": true, "_components": [{"__id__": 312}, {"__id__": 314}, {"__id__": 316}, {"__id__": 318}, {"__id__": 320}], "_prefab": {"__id__": 322}, "_lpos": {"__type__": "cc.Vec3", "x": -2, "y": 226.49999999999994, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 271}, "_children": [], "_active": true, "_components": [{"__id__": 273}, {"__id__": 275}, {"__id__": 277}, {"__id__": 279}], "_prefab": {"__id__": 281}, "_lpos": {"__type__": "cc.Vec3", "x": -492.375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "__prefab": {"__id__": 274}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 29.16, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29.16, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4b40928a-6c2c-4a07-aa98-c8017421e69f"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fA7dJd9VJeao1c+KaJv+1"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "__prefab": {"__id__": 276}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "belRo/aUFEhrcy+BDgNDwh"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "__prefab": {"__id__": 278}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7A7OYLJdNR65gI29wH9zC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 272}, "_enabled": true, "__prefab": {"__id__": 280}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 121.59, "height": 36.54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a70WhQKElHIK0IkZD/tN0q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "81ZALkpyJODrAP4L4JTPVq"}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "_parent": {"__id__": 271}, "_children": [], "_active": true, "_components": [{"__id__": 283}, {"__id__": 285}, {"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": -164.125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 284}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 29.16, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29.16, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4b40928a-6c2c-4a07-aa98-c8017421e69f"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcEkN4HRdJUqp623KQCPhw"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 286}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61cf17kCVMZLcscWRRc6bu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 288}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "355sYfEYFFmLlSHPlfDox9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 290}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 121.59, "height": 36.54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "53oBaQ5J9AN4YinlrMHMm+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e75H0C2HpI9Z6cn2lc4e+R"}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "_parent": {"__id__": 271}, "_children": [], "_active": true, "_components": [{"__id__": 293}, {"__id__": 295}, {"__id__": 297}, {"__id__": 299}], "_prefab": {"__id__": 301}, "_lpos": {"__type__": "cc.Vec3", "x": 164.125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 294}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 29.16, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29.16, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4b40928a-6c2c-4a07-aa98-c8017421e69f"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "ebSNQO7HtD57x+u5HACCFf"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 296}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8askpQaDJOoJ4ZoRF7c+Rp"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 298}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "09ERI3nG5B650jiBCJ4QFb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 292}, "_enabled": true, "__prefab": {"__id__": 300}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 121.59, "height": 36.54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "37XGRxqe1OYLSS3kxnz5Vm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9c1quzkedJfpae9qSmRjuX"}, {"__type__": "cc.Node", "_name": "4", "_objFlags": 0, "_parent": {"__id__": 271}, "_children": [], "_active": true, "_components": [{"__id__": 303}, {"__id__": 305}, {"__id__": 307}, {"__id__": 309}], "_prefab": {"__id__": 311}, "_lpos": {"__type__": "cc.Vec3", "x": 492.375, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 304}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 29.16, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 29.16, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "4b40928a-6c2c-4a07-aa98-c8017421e69f"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 39, "g": 39, "b": 39, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "53RP4vwuVIlr8YzyGWx35C"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 306}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cbcvxt7/BKYr18XNpwHC+n"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 308}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "54kGyLtFZNT5kyEsHjXtMA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 302}, "_enabled": true, "__prefab": {"__id__": 310}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 121.59, "height": 36.54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "86xbpg4LZOcryHfIRu2LZr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23AplTvzpNIpvR9G9mRsv5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 313}, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 4, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4yPSOb4FJ/4i7U7Zv+hLW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 315}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "235a6a0f-085c-4833-b86e-8c8a7552bac7@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dt4yWRRVAb6u22A1mqF5L"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 317}, "_resizeMode": 2, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1313, "height": 46}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adesXjEOdHLLse1itdAw9W"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 319}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "63LkJjaKlDqYtUk9qTHgEH"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 321}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1313, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25lIyC5HJNZrEpi0I2SpZI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08DjPWLM1K+YpsqLs7uAop"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 324}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 70, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "be0SIDjmVDz7OOhsb1cAEg"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 326}, "_resizeMode": 0, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 15, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 1317, "height": 498.9999999999999}, "_layoutType": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afOTl+cwtA0ohAE+L5w7Yr"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 328}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "60NV/HUipOV5aNKacu7NU6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 270}, "_enabled": true, "__prefab": {"__id__": 330}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1317, "height": 498.9999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "353bF49tdG8bIrsvaS+JVt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2rNkxYvdFuoEICpAIHZR/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 333}, "_alignFlags": 45, "_target": null, "_left": 100, "_right": 100, "_top": 277.34700000000004, "_bottom": 122.65300000000006, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2927WCSAFDKaPVH1oKSHKj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 335}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "18qsrGwVdFbZT2PgEqZWZF"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 199}, "_enabled": true, "__prefab": {"__id__": 337}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1317, "height": 568.9999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "23ccnkPotC3bYzjgIL1xe7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d8E0IVjPlAbbCr0dZJxSPq"}, {"__type__": "cc.Node", "_name": "Footer", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 340}], "_active": true, "_components": [{"__id__": 368}, {"__id__": 370}, {"__id__": 372}, {"__id__": 374}], "_prefab": {"__id__": 376}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -400, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "_parent": {"__id__": 339}, "_children": [{"__id__": 341}, {"__id__": 349}], "_active": true, "_components": [{"__id__": 359}, {"__id__": 361}, {"__id__": 363}, {"__id__": 365}], "_prefab": {"__id__": 367}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Node", "_name": "active", "_objFlags": 0, "_parent": {"__id__": 340}, "_children": [], "_active": true, "_components": [{"__id__": 342}, {"__id__": 344}, {"__id__": 346}], "_prefab": {"__id__": 348}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 343}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "375c3bf8-b3a8-42ab-8752-23ae3e43edb7@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7quUtxLdOxIQfV8NOJ3FX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 345}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "28xoWgbxdIyb0EKYOok6RE"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 341}, "_enabled": true, "__prefab": {"__id__": 347}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "25gPEGiiNF7phm61ilqK/9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2f986R/lCjYrPZkGm1HNG"}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "_parent": {"__id__": 340}, "_children": [], "_active": true, "_components": [{"__id__": 350}, {"__id__": 352}, {"__id__": 354}, {"__id__": 356}], "_prefab": {"__id__": 358}, "_lpos": {"__type__": "cc.Vec3", "x": 0.78, "y": 1.56, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 351}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_useOriginalSize": true, "_string": "1", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 0, "_fontSize": 46.65, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 46.65, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "40af9ae3-3068-40aa-bb3b-7b32227ffa78"}, "_isSystemFontUsed": false, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_id": "", "_enableShadow": true, "_shadowBlur": 2, "_shadowOffset": {"__type__": "cc.Vec2", "x": 0, "y": -3}, "_shadowColor": {"__type__": "cc.Color", "r": 37, "g": 37, "b": 37, "a": 255}}, {"__type__": "cc.CompPrefabInfo", "fileId": "96geZD1wFM0o9OCPyY78rD"}, {"__type__": "cc.LabelShadow", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 353}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cyj54KkZEJKcLIlQY45I6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 355}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "32jmh6OdtHBpzk+/LXMk0A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 349}, "_enabled": true, "__prefab": {"__id__": 357}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 25.94, "height": 57.96}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "6dqVYrBOdNHYwPpdw3LKWx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "984CZiL85DC4my8aE6TKmH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 360}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "034a2c35-1347-4b4e-9050-480abd7a9b1e@f9941"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c77FcMRABKH49A3ruVm9A7"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 362}, "clickEvents": [], "_interactable": true, "_transition": 2, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "034a2c35-1347-4b4e-9050-480abd7a9b1e@f9941"}, "_hoverSprite": {"__uuid__": "375c3bf8-b3a8-42ab-8752-23ae3e43edb7@f9941"}, "_pressedSprite": {"__uuid__": "375c3bf8-b3a8-42ab-8752-23ae3e43edb7@f9941"}, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "pressedSprite": {"__uuid__": "497eb08d-ef88-4443-a532-452faff7caca"}, "hoverSprite": {"__uuid__": "497eb08d-ef88-4443-a532-452faff7caca"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "98IqpMVYFAsrqZt9JS5dH9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 364}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7UllRHmdAaZBBY/PNeKqN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 340}, "_enabled": true, "__prefab": {"__id__": 366}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "cevaZs9H5B7IE3e1Hf47o8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "18ukwXFxpFi5fDlpw/L8x+"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 369}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 84.5, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daIYEb3zRDq7BI1tD8FZ/p"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 371}, "_resizeMode": 1, "_N$layoutType": 0, "_N$padding": 0, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_affectedByScale": false, "_layoutSize": {"__type__": "cc.Size", "width": 82, "height": 0}, "_layoutType": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "628cfrcNlHvYbs7noUVcB0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 373}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0J033zUlCWKVaT+yCpO3I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 375}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fSa/ExoZLQqv15urcUl1P"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "52Xom20VBPPYmETU2pAi8b"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 378}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8"}, "_visFlags": 0, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4e7be35d-e637-4f0d-9c84-32a878c2f439@f9941"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52BPp1jsVBfag80lmf+Tsm"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 380}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "20Ncn7AmdPYLdL+JFwYJc7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 382}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1517, "height": 969}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cwFF2MulI+6C9tpd9z6Gq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8097uA33FGDKGgczOU6TDz"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 385}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6eOzDxfJIn4bchm9LhqQk"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 387}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87d2CEKcdKsLMQUgrkNPy1"}, {"__type__": "03f10sDB/NLhJEWtE5Egnku", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 12}, "activeButtons": [{"__id__": 177}, {"__id__": 133}, {"__id__": 155}], "listContentNode": {"__id__": 270}, "itemContentNode": {"__id__": 271}, "listPageNode": {"__id__": 339}, "itemPageNode": {"__id__": 340}, "header3Play": {"__id__": 225}, "header3Exchange": {"__id__": 237}, "_id": "", "__prefab": {"__id__": 389}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cjIkkYtpBurUxmwoE1YNo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 391}, "_opacity": 255}, {"__type__": "cc.CompPrefabInfo", "fileId": "18M6wmaA1O6oRcYQZi8LE/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 393}, "_priority": 0, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}}, {"__type__": "cc.CompPrefabInfo", "fileId": "9clpSeog5CNb294yQqVq0M"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "efYQnUX99AiYKAUvlyvFgJ"}]