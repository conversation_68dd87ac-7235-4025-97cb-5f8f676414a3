import {_decorator, Label, Node, SpriteFrame, Sprite, instantiate, tween, Tween, Toggle, Color, v3} from "cc";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;


@ccclass('TaiXiuJPPopupDetailSession')
@menu("TaiXiuJP/PopupDetailSession")
export default class PopupDetailSession extends Dialog {
    @property(Label)
    lblSession: Label = null;
    @property(Label)
    lblSessionDate: Label = null;
    @property(Label)
    lblTotalBetTai: Label = null;
    @property(Label)
    lblTotalRefundTai: Label = null;
    @property(Label)
    lblTotalBetXiu: Label = null;
    @property(Label)
    lblTotalRefundXiu: Label = null;
    @property(Node)
    itemsTai: Node = null;
    @property(Node)
    itemsXiu: Node = null;
    @property(Node)
    itemTai: Node = null;
    @property(Node)
    itemXiu: Node = null;
    @property([SpriteFrame])
    sfDices: SpriteFrame[] = [];
    @property(Sprite)
    sprDice1: Sprite = null;
    @property(Sprite)
    sprDice2: Sprite = null;
    @property(Sprite)
    sprDice3: Sprite = null;
    @property(Label)
    labelSumDices: Label = null;
    @property(Node)
    nodeTai: Node = null;
    @property(Node)
    nodeXiu: Node = null;

    private gameID: number = 1; // 1: HK, 2: LVG, 3: MC
    private betType: number = 1; // 1: Gold, 2: Xu
    private sessionID: number = 0;
    private type: number = 1; // 0: cổng 88, 1: cổng 11, 2: xu
    private detailSessions: any[] = [];

    showDetail(session: number, gameID: number, detailSessions: any[]) {
        this.sessionID = session;
        this.gameID = gameID;

        if (!detailSessions || detailSessions.length === 0) {
            return;
        }

        this.detailSessions = detailSessions;

        this.show();
    }

    onChangeType() {
        this.type = (this.type + 1) % 3;
        this.loadData();
    }

    onChangeBetType(toggle: Toggle, data: number) {
        const target = toggle.node;
        if (data == 1) {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('xu').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        } else {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('tipzo').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        }
        if (toggle.isChecked === false) return;
        if (data == 1 && this.betType == 1) return;
        if (data == 0 && this.betType == 2) return;
        this.betType = this.betType === 1 ? 2 : 1;
        this.loadData();
    }

    show() {
        super.show();

        this.loadData();
    }

    private loadData() {
        this.lblSession.string = App.instance.getTextLang('txt_session') + ": #" + this.sessionID;

        this.itemsTai.removeAllChildren();
        this.itemsXiu.removeAllChildren();
        this.sprDice1.node.active = false;
        this.sprDice2.node.active = false;
        this.sprDice3.node.active = false;
        this.lblTotalBetTai.string = "";
        this.lblTotalRefundTai.string = "";
        this.lblTotalBetXiu.string = "";
        this.lblTotalRefundXiu.string = "";

        App.instance.showLoading(true);
        let totalBetTai = 0;
        let totalRefundTai = 0;
        let totalBetXiu = 0;
        let totalRefundXiu = 0;

        const sessionData = this.detailSessions.find((item) => item.GameSessionID == this.sessionID);
        this.sprDice1.spriteFrame = this.sfDices[sessionData.Dice1 - 1];
        this.sprDice2.spriteFrame = this.sfDices[sessionData.Dice2 - 1];
        this.sprDice3.spriteFrame = this.sfDices[sessionData.Dice3 - 1];
        this.labelSumDices.string = sessionData.Dice1 + sessionData.Dice2 + sessionData.Dice3 + "";
        this.sprDice1.node.active = true;
        this.sprDice2.node.active = true;
        this.sprDice3.node.active = true;
        this.lblSessionDate.node.active = false;
        Tween.stopAllByTarget(this.nodeTai);
        Tween.stopAllByTarget(this.nodeXiu);
        var nodeResult = sessionData.LocationIDWin === 2 ? this.nodeTai : this.nodeXiu;
        tween(nodeResult)
            .repeatForever(
                tween()
                    .to(0.25, {scale: v3(1.25, 1.25, 1.25)}, {easing: "quadOut"})
                    .to(0.2, {scale: v3(0.9, 0.9, 0.9)}, {easing: "quadIn"})
                    .to(0.15, {scale: v3(1.1, 1.1, 1.1)}, {easing: "sineOut"})
                    .to(0.1, {scale: v3(1, 1, 1)}, {easing: "sineInOut"})
            )
            .start();

        const url = Configs.App.DOMAIN_CONFIG['GetBetDetailsLuckyDice'] + "?currencyID=" + Configs.Login.CurrencyID + "&gameID=" + this.gameID + "&betType=" + this.betType + "&sessionID=" + this.sessionID + "&type=" + this.type;

        Http.post(url, {}, (_status, res) => {
            res.d.l.forEach((item: any, index: number) => {
                if (index == 0) {
                    this.lblSessionDate.node.active = true;
                    this.lblSessionDate.string = Utils.formatDatetime(item.CreatedTime, 'dd/MM/yyyy');
                }

                const node = instantiate(item.LocationID == 2 ? this.itemTai : this.itemXiu);
                node.active = true;
                const bg1 = node.getChildByName("bg1");
                const bg2 = node.getChildByName("bg2");

                if (bg1 && bg2) {
                    bg1.active = index % 2 === 0;
                    bg2.active = index % 2 !== 0;
                }

                const content = node.getChildByName("content");

                content.getChildByName("Time").getComponent(Label).string = Utils.formatDatetime(item.CreatedTime, 'HH:mm:ss');
                content.getChildByName("Account").getComponent(Label).string = item.Nickname;
                content.getChildByName("Refund").getComponent(Label).string = Utils.formatNumber(item.RefundValue);
                content.getChildByName("Bet").getComponent(Label).string = Utils.formatNumber(item.BetValue);

                if (item.LocationID == 2) {
                    totalBetTai += item.BetValue;
                    totalRefundTai += item.RefundValue;
                    this.itemsTai.addChild(node);
                } else {
                    totalBetXiu += item.BetValue;
                    totalRefundXiu += item.RefundValue;
                    this.itemsXiu.addChild(node);
                }
            });

            this.lblTotalBetTai.string = Utils.formatNumber(totalBetTai);
            this.lblTotalRefundTai.string = Utils.formatNumber(totalRefundTai);
            this.lblTotalBetXiu.string = Utils.formatNumber(totalBetXiu);
            this.lblTotalRefundXiu.string = Utils.formatNumber(totalRefundXiu);
        });

        App.instance.showLoading(false);
    }

    public actNextSession() {
        this.sessionID++;

        const data = this.detailSessions.find((item: any) => item.GameSessionID == this.sessionID);
        if (!data) {
            this.sessionID--;
            return;
        }

        this.loadData();
    }

    public actPrevSession() {
        this.sessionID--;

        const data = this.detailSessions.find((item: any) => item.GameSessionID == this.sessionID);
        if (!data) {
            this.sessionID++;
            return;
        }

        this.loadData();
    }
}
