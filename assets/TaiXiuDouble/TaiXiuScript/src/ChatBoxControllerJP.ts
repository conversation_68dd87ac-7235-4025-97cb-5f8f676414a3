import {_decorator, Component, Node, ScrollView, EditBox, instantiate, RichText, Button} from "cc";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";

const {ccclass, property, menu} = _decorator;

@ccclass('ChatBoxControllerJP')
@menu("TaiXiuJP/ChatBox")
export class ChatBoxControllerJP extends Component {
    CHAT_CHANNEL: string = "game_taixiu";
    @property(Node)
    itemChatTemplate: Node | null = null;
    @property(ScrollView)
    scrMessage: ScrollView | null = null;
    @property(EditBox)
    edbMessage: EditBox | null = null;

    onEnable() {
        this.scrMessage.content.removeAllChildren();
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);

        ChatHubSignalRClient.getInstance().registerChat(this.CHAT_CHANNEL, (response) => {
            if (this.edbMessage == null) return;
            if (response.c < 0) {
                this.edbMessage.placeholderLabel.string = App.instance.getTextLang('me50') + ` ${Utils.formatNumber(response.p[0])}`;
                this.edbMessage.placeholderLabel.enableWrapText = true;
                this.edbMessage.enabled = false;
                this.edbMessage.node.getComponentInChildren(Button).enabled = false;

                return;
            }

            this.edbMessage.placeholderLabel.string = App.instance.getTextLang("TLN_ENTER_CHAT");
            this.edbMessage.enabled = true;
            this.edbMessage.node.getComponentInChildren(Button).enabled = true;
        });
    }

    onDisable() {
        ChatHubSignalRClient.getInstance().unRegisterChat(this.CHAT_CHANNEL, (_response) => {});
    }

    start() {
        this.itemChatTemplate.active = false;
        this.edbMessage.node.on("editing-return", () => {
            this.sendChat();
        });

        ChatHubSignalRClient.getInstance().receiveChat((response) => {
            response.forEach((item: any) => {
                if (item.i !== this.CHAT_CHANNEL) {
                    return;
                }

                this.addMessage(item);
            });
        });

        ChatHubSignalRClient.getInstance().receiveLastMessages((response) => {
            this.scrMessage.content.removeAllChildren();
            response.forEach((item: any) => {
                if (item.i !== this.CHAT_CHANNEL) {
                    return;
                }

                this.addMessage(item);
            });
        });
    }

    show() {
        this.node.active = true;
    }

    hide() {
        this.node.active = false;
    }

    addMessage(data: any) {
        if (this.scrMessage.content.children.length >= 50) {
            this.scrMessage.content.children[0].destroy();
        }
        let item = instantiate(this.itemChatTemplate);
        item.parent = this.scrMessage.content;
        if (data.v >= 6) {
            item.getComponent(RichText).string = `<color=#ffffff>${data.c}</color>`;
        } else if (`${data.a}:${data.p}` === `${Configs.Login.AccountID}:${Configs.Login.PortalID}`) {
            item.getComponent(RichText).string = `<color=#fff600>${data.n}: </c><color=#ffffff>${data.c}</color>`;
        } else {
            item.getComponent(RichText).string = `<color=#3c91e6>${data.n}: </c><color=#ffffff>${data.c}</color>`;
        }

        item.active = true;
        this.scrollToBottom();
    }

    sendChat() {
        let msg = this.edbMessage.string.trim();
        if (msg.length == 0) {
            return;
        }
        this.edbMessage.string = "";

        ChatHubSignalRClient.getInstance().sendChat(this.CHAT_CHANNEL, msg, (_response) => {
            this.edbMessage.focus();
        });
    }

    scrollToBottom() {
        this.scrMessage.scrollToBottom(0.2);
    }
}
