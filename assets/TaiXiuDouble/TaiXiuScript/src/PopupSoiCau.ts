import { _decorator, Node, Label, Sprite, SpriteFrame, instantiate, v2, v3, UITransform, Color } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
const { ccclass, property, menu } = _decorator;


@ccclass('PopupSoiCau')
@menu("TaiXiuJP/PopupSoiCau")
export class PopupSoiCau extends Dialog {
    @property(Node)
    lineTemplate: Node = null;

    @property(Node)
    iconXX1Template: Node = null;
    @property(Node)
    iconXX2Template: Node = null;
    @property(Node)
    iconXX3Template: Node = null;
    @property(Node)
    xx123TaiTemplate: Node = null;
    @property(Node)
    xx123XiuTemplate: Node = null;

    @property(Label)
    lblLastSession: Label = null;
    @property(Node)
    xx1Draw: Node = null;
    @property(Node)
    xx2Draw: Node = null;
    @property(Node)
    xx3Draw: Node = null;
    @property(Node)
    xx123Draw: Node = null;

    // Page1
    @property(Node)
    containerPage1: Node = null;
    @property(Node)
    iconTaiPage1: Node = null;
    @property(Node)
    iconXiuPage1: Node = null;
    @property(Label)
    lblTaiPage1: Label = null;
    @property(Label)
    lblXiuPage1: Label = null;

    // Page2
    @property(Node)
    containerPage2: Node = null;
    @property(Node)
    iconPage2: Node = null;
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame = null;
    @property(Label)
    lblTaiPage2: Label = null;
    @property(Label)
    lblXiuPage2: Label = null;

    private gameID: number = 1; // 1: HK, 2: LVG, 3: MC

    showDetail(gameID: number) {
        this.gameID = gameID;
        this.show();
    }

    show() {
        super.show();

        this.loadData();

        this.scheduleOnce(() => {
            this.loadData();
        }, 60);
    }

    loadData() {
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['GetStatisticalLuckyDice'] + `?GameID=${this.gameID}`, {}, (status, res) => {
            if (status !== 200) {
                return;
            }

            this.drawG88Page1(res.d.Type1, res.d.Type1Location1, res.d.Type1Location2);
            this.drawG88Page2(res.d.Type2, res.d.Type2Location1, res.d.Type2Location2);

            App.instance.showLoading(false);
        });
    }

    drawG88Page1(Type1: any[], Type1Location1: number, Type1Location2: number) {
        this.containerPage1.removeAllChildren();
        let posX = 18;
        let posY = -18;
        let spacingX = 35;
        let spacingY = -35;

        this.lblXiuPage1.string = App.instance.getTextLang('tx44') + ": " + Type1Location1;
        this.lblTaiPage1.string = App.instance.getTextLang('tx43') + ": " + Type1Location2;

        var cleanData = [];
        for (let i = 0; i < Type1.length; i++) {
            if (Type1[i].DiceSum > 0) {
                cleanData.push(Type1[i]);
            }
        }

        this.drawG88Page3(cleanData);
        this.drawG88Page4(cleanData);

        let x = 0;
        let xTemp = 0;
        let yCounter = 0;
        let currentLocationIDWin = cleanData.length > 0 ? cleanData[0].LocationIDWin : null;

        cleanData.forEach((item) => {
            const node: Node = instantiate(item.LocationIDWin === 1 ? this.iconXiuPage1 : this.iconTaiPage1);

            if (item.LocationIDWin !== currentLocationIDWin) {
                if (yCounter != 0) {
                    x++;
                }
                xTemp = x;
                yCounter = 0;
                currentLocationIDWin = item.LocationIDWin;
            }

            const y = ((x - xTemp) % 2 === 0) ? yCounter : 5 - yCounter;

            node.setPosition(v3(posX + x * spacingX, posY + y * spacingY, 0));
            node.getChildByName("text").getComponent(Label).string = item.DiceSum;
            this.containerPage1.addChild(node);

            if (++yCounter === 6) {
                x++;
                yCounter = 0;
            }
        });
    }

    drawG88Page2(Type2: any[], Type2Location1: number, Type2Location2: number) {
        this.containerPage2.removeAllChildren();
        let posX = 32;
        let posY = -7;
        let spacingX = 45;
        let spacingY = -32;

        this.lblXiuPage2.string = App.instance.getTextLang('tx44') + ": " + Type2Location1;
        this.lblTaiPage2.string = App.instance.getTextLang('tx43') + ": " + Type2Location2;

        var cleanData = [];
        for (let i = 0; i < Type2.length; i++) {
            if (Type2[i].DiceSum > 0) {
                cleanData.push(Type2[i]);
            }
        }

        cleanData.slice(0, 112).forEach((item, index) => {
            var node = instantiate(this.iconPage2);

            let x = Math.floor(index / 7);
            var y: number;

            if (x % 2 === 0) {
                y = (index % 7);
            } else {
                y = 6 - (index % 7);
            }

            node.setPosition(v3(posX + x * spacingX, posY + y * spacingY));
            if (item.LocationIDWin === 1) {
                node.getComponent(Sprite).spriteFrame = this.sprFrameXiu;
            } else {
                node.getComponent(Sprite).spriteFrame = this.sprFrameTai;
            }

            this.containerPage2.addChild(node);
        });
    }

    drawG88Page3(data: any[]) {
        if (data.length > 20) {
            data.splice(0, data.length - 20);
        }

        if (data.length === 0) {
            this.lblLastSession.string = App.instance.getTextLang('NO_DATA');
            return;
        }

        var last = data[data.length - 1];
        const sessionText = `(#${last.GameSessionID}) - ${last.LocationIDWin == 1 ? App.instance.getTextLang('tx2') : App.instance.getTextLang('tx1')}`;
        const diceText = `${last.DiceSum} (${last.Dice1} - ${last.Dice2} - ${last.Dice3})`;

        this.lblLastSession.string = `${App.instance.getTextLang('txt_taixiu_last_session')} ${sessionText}  ${diceText}`;

        let posX = 35;
        let posY = 0;
        var spacingX = 36;
        var spacingY = 35;
        this.xx123Draw.removeAllChildren();
        data.forEach((item, index) => {
            let startPosXX123 = v2(posX + index * spacingX, posY + (item.DiceSum - 3) * (spacingY / 3));

            var iconXX123: Node;
            if (item.LocationIDWin === 1) {
                iconXX123 = instantiate(this.xx123XiuTemplate);
            } else {
                iconXX123 = instantiate(this.xx123TaiTemplate);
            }

            iconXX123.getChildByName("text").getComponent(Label).string = item.DiceSum + "";

            iconXX123.parent = this.xx123Draw;
            iconXX123.setPosition(v3(startPosXX123.x, startPosXX123.y, 0));

            if (index === data.length - 1) {
                return;
            }

            var nextItem = data[index + 1];

            let endPosXX123 = v2(posX + (index + 1) * spacingX, posY + (nextItem.DiceSum - 3) * (spacingY / 3));

            let line = instantiate(this.lineTemplate);
            line.parent = this.xx123Draw;
            line.getComponent(UITransform).width = Utils.v2Distance(startPosXX123, endPosXX123);
            line.setPosition(v3(startPosXX123.x, startPosXX123.y, 0));
            line.angle = Utils.v2Degrees(startPosXX123, endPosXX123);
            line.getComponent(Sprite).color = new Color().fromHEX("#ffffff");
            line.setSiblingIndex(-50)
        });
    }

    drawG88Page4(data: any[]) {
        let posX = 22;
        let posY = 25;
        this.xx1Draw.removeAllChildren();
        this.xx2Draw.removeAllChildren();
        this.xx3Draw.removeAllChildren();

        // get 20 items last of data
        if (data.length > 20) {
            data.splice(0, data.length - 20);
        }

        if (data.length === 0) {
            return;
        }

        var spacingX = 36;
        var spacingY = 25;
        data.forEach((item, index) => {
            let startPosXX1 = v2(posX + index * spacingX, posY + (item.Dice1 - 1) * spacingY);
            let startPosXX2 = v2(posX + index * spacingX, posY + (item.Dice2 - 1) * spacingY);
            let startPosXX3 = v2(posX + index * spacingX, posY + (item.Dice3 - 1) * spacingY);

            let iconXX1 = instantiate(this.iconXX1Template);
            iconXX1.parent = this.xx1Draw;
            iconXX1.setPosition(v3(startPosXX1.x, startPosXX1.y, 0));

            let iconXX2 = instantiate(this.iconXX2Template);
            iconXX2.parent = this.xx2Draw;
            iconXX2.setPosition(v3(startPosXX2.x, startPosXX2.y, 0));

            let iconXX3 = instantiate(this.iconXX3Template);
            iconXX3.parent = this.xx3Draw;
            iconXX3.setPosition(v3(startPosXX3.x, startPosXX3.y, 0));

            if (index === data.length - 1) {
                return;
            }

            var nextItem = data[index + 1];

            let endPosXX1 = v2(posX + (index + 1) * spacingX, posY + (nextItem.Dice1 - 1) * spacingY);
            let endPosXX2 = v2(posX + (index + 1) * spacingX, posY + (nextItem.Dice2 - 1) * spacingY);
            let endPosXX3 = v2(posX + (index + 1) * spacingX, posY + (nextItem.Dice3 - 1) * spacingY);

            let line = instantiate(this.lineTemplate);
            line.parent = this.xx1Draw;
            line.getComponent(UITransform).width = Utils.v2Distance(startPosXX1, endPosXX1);
            line.setPosition(v3(startPosXX1.x, startPosXX1.y, 0));
            line.angle = Utils.v2Degrees(startPosXX1, endPosXX1);
            line.getComponent(Sprite).color = new Color().fromHEX("#f0ff00");
            line.setSiblingIndex(0);

            line = instantiate(this.lineTemplate);
            line.parent = this.xx2Draw;
            line.getComponent(UITransform).width = Utils.v2Distance(startPosXX2, endPosXX2);
            line.setPosition(v3(startPosXX2.x, startPosXX2.y, 0));
            line.angle = Utils.v2Degrees(startPosXX2, endPosXX2);
            line.getComponent(Sprite).color = new Color().fromHEX("#ff0096");
            line.setSiblingIndex(0);

            line = instantiate(this.lineTemplate);
            line.parent = this.xx3Draw;
            line.getComponent(UITransform).width = Utils.v2Distance(startPosXX3, endPosXX3);
            line.setPosition(v3(startPosXX3.x, startPosXX3.y, 0));
            line.angle = Utils.v2Degrees(startPosXX3, endPosXX3);
            line.getComponent(Sprite).color = new Color().fromHEX("#00f0ff");
            line.setSiblingIndex(0);
        });
    }

    dismiss() {
        super.dismiss();
        // this.page1.active = false;
        // this.page2.active = false;
    }

    toggleLinePage4(event: any, data: any) {
        if (data == "1") {
            this.xx1Draw.active = !this.xx1Draw.active;
        }

        if (data == "2") {
            this.xx2Draw.active = !this.xx2Draw.active;
        }

        if (data == "3") {
            this.xx3Draw.active = !this.xx3Draw.active;
        }
    }
}
