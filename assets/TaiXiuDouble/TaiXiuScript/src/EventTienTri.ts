import {_decorator, Node, Toggle, Label, Color, instantiate, EditBox, Sprite} from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";

const {ccclass, property, menu} = _decorator;

@ccclass('EventTienTri')
@menu("TaiXiuJP/EventTienTri")
export class EventTienTri extends Dialog {
    @property(Toggle)
    toggleDay: Toggle | null = null;
    @property(Toggle)
    toggleFinal: Toggle | null = null;
    @property(Toggle)
    toggleRule: Toggle | null = null;
    @property(Toggle)
    toggleWin: Toggle | null = null;
    @property(Toggle)
    toggleLost: Toggle | null = null;
    @property(Node)
    contentData: Node | null = null;
    @property(Node)
    contentRule: Node | null = null;
    @property(Node)
    headerWin: Node | null = null;
    @property(Node)
    headerLost: Node | null = null;
    @property(Node)
    listContent: Node | null = null;
    @property(Node)
    itemContent: Node | null = null;
    @property(Node)
    dropboxSelectDate: Node | null = null;
    @property(Node)
    containerBoxCalenderResults: Node | null = null;
    @property(Node)
    dayItemPrefab: Node | null = null;
    @property([Node])
    rows: Node[] = [];
    @property(Label)
    labelTitleTime: Label | null = null;
    @property(Label)
    labelTitleTimeBox: Label | null = null;
    @property(Label)
    labelTabResultTab: Label | null = null;
    @property(Color)
    selectedColor: Color = new Color().fromHEX('#8043DB');
    @property(Color)
    normalColor: Color = new Color().fromHEX('#cdbee4');
    private gameID: number = 1; // 1: HK, 2: LVG, 3: MC
    private isDay: boolean = true;
    private eventType: number = 1;
    private isShowContainerBoxCalenderResult: boolean = false;
    private currentDate: Date = new Date();
    private selectedNode: Node | null = null;
    private selectedDay: number = new Date().getDate();
    private selectedDate: string = "";

    start() {
        this.generateCalendar();
        let today = new Date();

        this.labelTitleTime.string = Utils.formatDatetime(today.toString(), "dd/MM/yyyy");
        this.labelTitleTimeBox.string = Utils.formatDatetime(today.toString(), "dd/MM/yyyy");
    }

    showDetail(gameID: number) {
        this.gameID = gameID;

        this.show();
    }

    show() {
        super.show();

        this.toggleDay.isChecked = true;
        this.isDay = true;
    }

    selectedToggleTabs(toggle: Toggle, dataStr: string) {
        var data = parseInt(dataStr);
        if (data == 2) {
            this.contentData.active = false;
            this.contentRule.active = true;

            this.updateToggleTextColor(this.toggleRule, true);
            this.updateToggleTextColor(this.toggleDay, false);
            this.updateToggleTextColor(this.toggleFinal, false);

            return;
        }

        this.updateToggleTextColor(this.toggleRule, false);

        if (data == 0) {
            this.updateToggleTextColor(this.toggleDay, true);
            this.updateToggleTextColor(this.toggleFinal, false);
        } else {
            this.updateToggleTextColor(this.toggleDay, false);
            this.updateToggleTextColor(this.toggleFinal, true);
        }

        if (toggle.isChecked == false) return;

        this.contentData.active = true;
        this.contentRule.active = false;

        this.isDay = data == 0;
        if (this.toggleWin.isChecked) {
            this.loadDataAPI();
        } else {
            this.toggleWin.isChecked = true;
        }
    }

    onToggleWinLost(toggle: Toggle, dataStr: string) {
        if (dataStr === "0") {
            this.updateToggleTextColor(this.toggleWin, true);
            this.updateToggleTextColor(this.toggleLost, false);
            this.headerWin.active = true;
            this.headerLost.active = false;
        } else {
            this.updateToggleTextColor(this.toggleWin, false);
            this.updateToggleTextColor(this.toggleLost, true);
            this.headerWin.active = false;
            this.headerLost.active = true;
        }

        if (toggle.isChecked) {
            this.loadDataAPI();
        }
    }

    private loadDataAPI() {
        if (this.isDay) {
            this.eventType = this.toggleWin.isChecked ? 1 : 2;
        } else {
            this.eventType = this.toggleWin.isChecked ? 3 : 4;
        }

        var date = new Date();
        if (this.selectedDate !== "") {
            date = new Date(this.selectedDate);
        }
        const params = {
            currencyID: Configs.Login.CurrencyID,
            GameID: this.gameID,
            eventType: this.eventType,
            eventDate: date.toDateString().replace(/ /g, "%20"),
            topCount: 10
        };

        this.listContent.removeAllChildren();

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopEventLuckyDice"], params, (status, res) => {
            if (status !== 200) {
                App.instance.showLoading(false);
                return;
            }

            if (!res.d) {
                App.instance.showLoading(false);
                return;
            }

            var data = res.d;
            for (let i = 0; i < res.d.length; i++) {
                let itemNode = instantiate(this.itemContent);
                const content = itemNode.getChildByName("content");
                const giftNode = content.getChildByName("gift");
                if (this.isDay) {
                    if (data[i].Rank === 1) {
                        giftNode.getComponent(Label).string = "2.000.000";
                    }

                    if (data[i].Rank === 2) {
                        giftNode.getComponent(Label).string = "1.000.000";
                    }

                    if (data[i].Rank === 3) {
                        giftNode.getComponent(Label).string = "500.000";
                    }
                }

                if (this.eventType === 3 && data[i].Rank === 1) {
                    giftNode.getComponent(Label).string = "Iphone 16 Pro 256GB";
                }

                if (this.eventType === 4 && data[i].Rank === 1) {
                    giftNode.getComponent(Label).string = "Iphone 16 Pro Max 256GB";
                }

                const bg1 = itemNode.getChildByName("bg1");
                const bg2 = itemNode.getChildByName("bg2");

                if (bg1 && bg2) {
                    bg1.active = i % 2 === 0;
                    bg2.active = i % 2 !== 0;
                }

                content.getChildByName("stt").getComponent(Label).string = data[i].Rank;
                content.getChildByName("accountName").getComponent(Label).string = data[i].Nickname;
                content.getChildByName("continual").getComponent(Label).string = data[i].ContinuousCount;
                content.getChildByName("total").getComponent(Label).string = Utils.formatNumber(data[i].TotalBet);
                content.getChildByName("session").getComponent(Label).string = '#' + data[i].GameSessionID;
                itemNode.active = true;
                this.listContent.addChild(itemNode);
            }

            App.instance.showLoading(false);
        });
    }

    private updateToggleTextColor(toggle: Toggle, isActive: boolean) {
        toggle.node.getChildByName("text").getComponent(Label).color = isActive ? new Color(255, 240, 0) : new Color(255, 255, 255);
    }

    _onDismissed() {
        var edits = this.node.getComponentsInChildren(EditBox);

        for (var i = 0; i < edits.length; i++) {
            edits[i].tabIndex = -1;
        }
        this.node.active = false;
    }

    showBoxCalender() {
        this.isShowContainerBoxCalenderResult = !this.isShowContainerBoxCalenderResult;

        this.containerBoxCalenderResults.active = this.isShowContainerBoxCalenderResult;
        this.generateCalendar();
    }

    generateCalendar() {
        if (!this.dayItemPrefab || this.rows.length < 5) {
            return;
        }


        this.rows.forEach(row => row.removeAllChildren());

        let year = this.currentDate.getFullYear();
        let month = this.currentDate.getMonth();
        let totalDays = new Date(year, month + 1, 0).getDate();

        let rowIndex = 0;
        this.selectedNode = null;

        for (let i = 1; i <= totalDays; i++) {
            let dayItem = instantiate(this.dayItemPrefab);
            let label = dayItem.getChildByName("text").getComponent(Label);

            if (!label) {
                return;
            }

            label.string = i.toString();

            if (i === this.selectedDay) {
                dayItem.getComponent(Sprite).color = this.selectedColor;
                this.selectedNode = dayItem;
            } else {
                dayItem.getComponent(Sprite).color = this.normalColor;
            }

            dayItem.on(Node.EventType.TOUCH_END, () => this.onDaySelected(dayItem, i), this);

            this.rows[rowIndex].addChild(dayItem);

            if ((i % 7 === 0) && rowIndex < this.rows.length - 1) {
                rowIndex++;
            }
        }
    }

    onDaySelected(dayNode: Node, day: number) {
        if (this.selectedNode) {
            this.selectedNode.getComponent(Sprite).color = this.normalColor;
        }

        this.selectedDay = day;
        this.selectedNode = dayNode;
        this.selectedNode.getComponent(Sprite).color = this.selectedColor;

        let dayStr = ("0" + day).slice(-2);
        let monthStr = ("0" + (this.currentDate.getMonth() + 1)).slice(-2);
        let yearStr = this.currentDate.getFullYear().toString();

        let currentDate = `${dayStr}/${monthStr}/${yearStr}`;

        this.labelTitleTime.string = currentDate;
        this.labelTitleTimeBox.string = currentDate;
        // this.labelTabResultTab.string = `${dayStr}-${monthStr}-${yearStr}`;
        this.containerBoxCalenderResults.active = false;
        this.isShowContainerBoxCalenderResult = !this.isShowContainerBoxCalenderResult;

        this.selectedDate = `${yearStr}-${monthStr}-${dayStr}`;

        this.loadDataAPI();
    }

    updateCalendarAfterMonthChange() {
        let newTotalDays = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0).getDate();
        if (this.selectedDay > newTotalDays) {
            this.selectedDay = newTotalDays;
        }

        this.selectedNode = null;
        this.generateCalendar();

        let dayStr = ('0' + this.selectedDay).slice(-2);
        let monthStr = ('0' + (this.currentDate.getMonth() + 1)).slice(-2);
        let yearStr = this.currentDate.getFullYear().toString();

        this.labelTitleTime.string = `${dayStr}/${monthStr}/${yearStr}`;
        this.labelTitleTimeBox.string = `${dayStr}/${monthStr}/${yearStr}`;
    }

    onNextMonth() {
        let month = this.currentDate.getMonth();
        let year = this.currentDate.getFullYear();

        if (month === 11) {
            this.currentDate.setFullYear(year + 1);
            this.currentDate.setMonth(0);
        } else {
            this.currentDate.setMonth(month + 1);
        }

        this.updateCalendarAfterMonthChange();
    }

    onPreviousMonth() {
        let month = this.currentDate.getMonth();
        let year = this.currentDate.getFullYear();

        if (month === 0) {
            this.currentDate.setFullYear(year - 1);
            this.currentDate.setMonth(11);
        } else {
            this.currentDate.setMonth(month - 1);
        }

        this.updateCalendarAfterMonthChange();
    }
}
