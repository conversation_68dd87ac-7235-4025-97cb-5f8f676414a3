import { _decorator, Node, ScrollView, Toggle, Label, instantiate, Color } from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
const {ccclass, property, menu} = _decorator;

@ccclass('TaiXiuJPPopupHistory')
@menu("TaiXiuJP/PopupHistory")
export class PopupHistory extends Dialog {
    @property(Node)
    toggleContainer: Node = null;
    @property(Node)
    prefab: Node = null;
    @property(ScrollView)
    scroll: ScrollView = null;

    private isGold = true;
    private gameID = 2; // 1: HK, 2: LVG, 3: MC
    private gameNames = ["HK", "LVG", "MC"];

    private page: number = 1;
    private per_page: number = 10;

    showHistory(gameID: number) {
        this.gameID = gameID;
        this.scroll.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        super.show();
        var toggle = this.toggleContainer.getChildByName(this.gameNames[this.gameID - 1]).getComponent(Toggle);
        if (toggle.isChecked === false) {
            toggle.isChecked = true;
        } else {
            this.page = 1;
            this.loadData();
        }
    }

    dismiss() {
        super.dismiss();
        this.scroll.content.removeAllChildren();
    }

    onChangeType(toggle: Toggle, data: number) {
        const target = toggle.node;
        if (data == 1) {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('xu').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        } else {
            target.getChildByName('text').getComponent(Label).color = new Color().fromHEX('#FFF000');
            target.parent.getChildByName('tipzo').getChildByName('text').getComponent(Label).color = new Color().fromHEX('#CDBEE4');
        }
        if (toggle.isChecked === false) return;

        if (data == 1 && this.isGold === true) return;
        if (data == 2 && this.isGold === false) return;
        this.scroll.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        this.scroll.content.removeAllChildren();
        this.isGold = !this.isGold;
        this.page = 1;
        this.loadData();
    }

    onChangeGameID(toggle: Toggle, data: any) {
        if (toggle.isChecked === false) return;
        this.gameID = parseInt(data);
        this.page = 1;
        this.scroll.node.on('scroll-to-bottom', this.onScrollToBottom, this);
        this.scroll.content.removeAllChildren();
        this.loadData();
    }

    private loadData() {
        App.instance.showLoading(true);

        var topCount = this.per_page * this.page;
        const params = {
            currencyID: Configs.Login.CurrencyID,
            gameID: this.gameID,
            betType: this.isGold ? 1 : 2,
            topCount: topCount,
        }

        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountHistoryLuckyDice'], params, (status, res) => {
            if (status === 200) {
                var data = res.d;
                var useBG1 = true;
                let toggleBG = (node: Node, useBG1: boolean): boolean => {
                    const bg1 = node.getChildByName("bg1");
                    const bg2 = node.getChildByName("bg2");
                    bg1.active = useBG1;
                    bg2.active = !useBG1;
                    return !useBG1;
                };

                if (topCount > data.length) {
                    this.scroll.node.off('scroll-to-bottom', this.onScrollToBottom, this);
                }

                for (let i = this.page - 1; i < data.length; i++) {
                    const item = data[i];

                    const node = instantiate(this.prefab);
                    useBG1 = toggleBG(node, useBG1);
                    this.setItemData(node.getChildByName("content"), item);
                    node.active = true;
                    this.scroll.content.addChild(node);

                    if (item.JackPotValue > 0 && this.isGold) {
                        const nodeJP = instantiate(node);
                        const nodeContentJP = nodeJP.getChildByName("content");
                        useBG1 = toggleBG(nodeJP, useBG1);

                        nodeContentJP.getChildByName("lblResult").getComponent(Label).string = App.instance.getTextLang("sl33");
                        nodeContentJP.getChildByName("lblWin").getComponent(Label).string = Utils.formatNumber(item.JackPotValue);
                        nodeJP.active = true;
                        this.scroll.content.addChild(nodeJP);
                    }
                }
            }

            App.instance.showLoading(false);
        });
    }

    setItemData(item: Node, data: any) {
        item.getChildByName("lblSession").getComponent(Label).string = "#" + data.GameSessionID;
        item.getChildByName("lblTime").getComponent(Label).string = Utils.formatDatetime(data.StartTime, 'dd/MM/yyyy HH:mm:ss');
        item.getChildByName("lblBetDoor").getComponent(Label).string = data.LocationName == 1 ? App.instance.getTextLang("tx44") : App.instance.getTextLang("tx43");
        item.getChildByName("lblResult").getComponent(Label).string = data.Result;
        item.getChildByName("lblBet").getComponent(Label).string = Utils.formatNumber(data.TotalBetValue);
        item.getChildByName("lblRefund").getComponent(Label).string = Utils.formatNumber(data.RefundValue);
        item.getChildByName("lblWin").getComponent(Label).string = Utils.formatNumber(data.PrizeValue);
    }

    onScrollToBottom() {
        this.page++;
        this.loadData();
    }
}
