import { _decorator, Node, Prefab, v3, Component, instantiate } from 'cc';
import Configs from "db://assets/Lobby/scripts/common/Config";
import App from "db://assets/Lobby/scripts/common/App";
import MiniGameTXSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXSignalRClient";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import {MiniGame} from "db://assets/Lobby/scripts/common/MiniGame";
import TaiXiuMiniController from "db://assets/TaiXiuDouble/TaiXiuScript/src/TaiXiuMiniController";
import TaiXiuLVGController from "db://assets/TaiXiuDouble/TaiXiuScript/src/TaiXiuLVGController";
import {PopupSoiCau} from "db://assets/TaiXiuDouble/TaiXiuScript/src/PopupSoiCau";
import {EventTienTri} from "db://assets/TaiXiuDouble/TaiXiuScript/src/EventTienTri";
import {EventSH} from "db://assets/TaiXiuDouble/TaiXiuScript/src/EventSH";
import {PopupHonors} from "db://assets/TaiXiuDouble/TaiXiuScript/src/TaiXiuJP.PopupHonors";
import {PopupHistory} from "db://assets/TaiXiuDouble/TaiXiuScript/src/TaiXiuJP.PopupHistory";
import {PopupHistoryJackpot} from "db://assets/TaiXiuDouble/TaiXiuScript/src/PopupHistoryJackpot";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import Config from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
const { ccclass, property } = _decorator;

@ccclass('TaiXiuDoubleController')
export default class TaiXiuDoubleController extends MiniGame {
    static instance: TaiXiuDoubleController = null;
    @property(Node)
    background: Node | null = null;
    // 1: Gold 2: Xu
    private isBetTypeGold = true;
    @property(Node)
    private labelHeaderCenterTX1: Node | null = null;
    @property(Node)
    private labelHeaderCenterTX2: Node | null = null;
    @property(Node)
    private labelHeaderCenterTX3: Node | null = null;
    @property(Node)
    private containerLeftTX: Node | null = null;
    @property(Node)
    private labelHeaderLeftTX1: Node | null = null;
    @property(Node)
    private labelHeaderLeftTX2: Node | null = null;
    @property(Node)
    private labelHeaderLeftTX3: Node | null = null;
    @property(Node)
    private containerRightTX: Node | null = null;
    @property(Node)
    private labelHeaderRightTX1: Node | null = null;
    @property(Node)
    private labelHeaderRightTX2: Node | null = null;
    @property(Node)
    private labelHeaderRightTX3: Node | null = null;
    @property(Node)
    private containerCenterMC: Node | null = null;
    @property(Node)
    private containerCenterHK: Node | null = null;
    @property(Node)
    private containerCenterLVG: Node | null = null;
    @property(Node)
    public backgroundLayoutBet: Node | null = null;
    // Chat Box
    @property(Node)
    chatBox: Node | null = null;
    // Popup Container
    @property(Node)
    popupContainer: Node | null = null;
    @property(Prefab)
    popupEventTienTriPrefab: Prefab | null = null;
    @property(Prefab)
    popupEventSoiCauPrefab: Prefab | null = null;
    @property(Prefab)
    popupEventSHPrefab: Prefab | null = null;
    @property(Prefab)
    popupEventJackpotPrefab: Prefab | null = null;
    @property(Prefab)
    popupGuidePrefab: Prefab | null = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab | null = null;
    @property(Prefab)
    popupEventHonorPrefab: Prefab | null = null;
    private popupEventTienTri: any = null;
    private popupSoiCau: any = null;
    private popupEventSH: any = null;
    private popupEventJackpot: any = null;
    private popupHonor: any = null;
    private popupHistory: any = null;
    private popupGuide: any = null;
    private containerCenterTX = [];
    private nameTX = ["HK", "LVG", "MC"];
    private offscreenPos = v3(-2000, -2000, 0);
    private onscreenPos = v3(0, 0, 0);
    public onLoad() {
        super.onLoad();
        TaiXiuDoubleController.instance = this;
        this.containerCenterTX = [this.containerCenterHK, this.containerCenterLVG, this.containerCenterMC];
        this.setupTouchEvents();
        this.setInitialVisibility();
    }

    private setupTouchEvents() {
        const centerLabels = [this.labelHeaderCenterTX1, this.labelHeaderCenterTX2, this.labelHeaderCenterTX3];
        const leftLabels = [this.labelHeaderLeftTX1, this.labelHeaderLeftTX2, this.labelHeaderLeftTX3];
        const rightLabels = [this.labelHeaderRightTX1, this.labelHeaderRightTX2, this.labelHeaderRightTX3];

        const updateUI = (centerIdx: number, otherIdx: number, labels: Node[], isLeft: boolean = true) => {
            centerLabels.forEach((label, i) => label.active = i === otherIdx);
            labels.forEach((label, i) => label.active = i === centerIdx);

            this.containerCenterTX.forEach((container, index) => {
                container.position = index === otherIdx ? this.onscreenPos : this.offscreenPos;
                container.getComponent(TaiXiuLVGController).hideLayoutBet();
            });

            const containerMini = isLeft ? this.containerLeftTX : this.containerRightTX;
            this.nameTX.forEach((name, index) => {
                containerMini.getChildByName(name).position = index === centerIdx ? this.onscreenPos : this.offscreenPos;
            });

            this.backgroundLayoutBet.active = false;
        };

        this.containerLeftTX.on(Node.EventType.TOUCH_END, () => {
            const centerIdx = centerLabels.findIndex(label => label.active);
            const leftIdx = leftLabels.findIndex(label => label.active);
            if (centerIdx !== -1 && leftIdx !== -1) updateUI(centerIdx, leftIdx, leftLabels);
        });

        this.containerRightTX.on(Node.EventType.TOUCH_END, () => {
            const index = centerLabels.findIndex(label => label.active);
            const rightIdx = rightLabels.findIndex(label => label.active);
            if (index !== -1 && rightIdx !== -1) updateUI(index, rightIdx, rightLabels, false);
        });
    }

    private setInitialVisibility() {
        this.containerCenterTX.forEach((container, index) => {
            container.active = true;
            container.position = index === 0 ? this.onscreenPos : this.offscreenPos;
        });

        this.nameTX.forEach(name => {
            this.containerLeftTX.getChildByName(name).active = true;
            this.containerLeftTX.getChildByName(name).position = name === "MC" ? this.onscreenPos : this.offscreenPos;
        });

        this.nameTX.forEach(name => {
            this.containerRightTX.getChildByName(name).active = true;
            this.containerRightTX.getChildByName(name).position = name === "LVG" ? this.onscreenPos : this.offscreenPos;
        });
    }

    private initializeHubs() {
        this.containerCenterTX.forEach((container, index) => {
            container.getComponent(TaiXiuLVGController).initHubs(index + 1);
        });

        this.nameTX.forEach((name, index) => {
            this.containerLeftTX.getChildByName(name).getComponent(TaiXiuMiniController).initHubs(index + 1);
            this.containerRightTX.getChildByName(name).getComponent(TaiXiuMiniController).initHubs(index + 1);
        });
    }

    onEnable() {
        this.initializeHubs();
    }

    onDisable() {
        if (Utils.getStorageKey("opened_tx_jp_live") === "true") return;
        MiniGameTXSignalRClient.getInstance().send("HideLD", [{GameID: 1}], () => {});
        MiniGameTXSignalRClient.getInstance().send("HideLD", [{GameID: 2}], () => {});
        MiniGameTXSignalRClient.getInstance().send("HideLD", [{GameID: 3}], () => {});
    }

    public show() {
        super.show();
        this.initHubs();
    }

    onDestroy() {
        MiniGameTXSignalRClient.getInstance().dontReceive();
        ChatHubSignalRClient.getInstance().dontReceive();
    }

    public initHubs() {
        MiniGameTXSignalRClient.getInstance().send("GetCurrentRoomsLD", [{GameID: 1, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        MiniGameTXSignalRClient.getInstance().send("GetCurrentRoomsLD", [{GameID: 2, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        MiniGameTXSignalRClient.getInstance().send("GetCurrentRoomsLD", [{GameID: 3, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
    }

    actSwitchCoinXu() {
        var isAllow = true;
        this.containerCenterTX.forEach((container) => {
            if (container.getComponent(TaiXiuLVGController).isAllowSwitchBetType == false) {
                isAllow = false;
            }
        });

        if (!isAllow) return;
        this.isBetTypeGold = !this.isBetTypeGold;
        this.initHubs();
        this.containerCenterTX.forEach((container) => {
            container.getComponent(TaiXiuLVGController).actSwitchCoin();
        });
    }

    toggleChatBox() {
        this.chatBox.active = !this.chatBox.active;
    }

    toggleLight(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');

        on.active = !on.active;
        off.active = !off.active;

        this.background.active = off.active;
    }

    getCurrentGameID(): number {
        if (this.labelHeaderCenterTX1.active) {
            return 1;
        } else if (this.labelHeaderCenterTX2.active) {
            return 2;
        } else {
            return 3;
        }
    }

    actPopupEventTienTri() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventTienTri == null) {
            this.popupEventTienTri = instantiate(this.popupEventTienTriPrefab).getComponent(EventTienTri);
            this.popupEventTienTri.node.parent = this.popupContainer;
            this.popupEventTienTri.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventTienTri.showDetail(gameID);
        }
    }

    actPopupEventSoiCau() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupSoiCau == null) {
            this.popupSoiCau = instantiate(this.popupEventSoiCauPrefab).getComponent(PopupSoiCau);
            this.popupSoiCau.node.parent = this.popupContainer;
            this.popupSoiCau.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupSoiCau.showDetail(gameID);
        }
    }

    actPopupEventSH() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventSH == null) {
            this.popupEventSH = instantiate(this.popupEventSHPrefab).getComponent(EventSH);
            this.popupEventSH.node.parent = this.popupContainer;
            this.popupEventSH.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventSH.showDetail(gameID);
        }
    }

    actPopupEventJackpot() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventJackpot == null) {
            this.popupEventJackpot = instantiate(this.popupEventJackpotPrefab).getComponent(PopupHistoryJackpot);
            this.popupEventJackpot.node.parent = this.popupContainer;
            this.popupEventJackpot.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventJackpot.showDetail(gameID);
        }
    }

    actPopupHonor() {
        if (this.popupHonor == null) {
            this.popupHonor = instantiate(this.popupEventHonorPrefab).getComponent(PopupHonors);
            this.popupHonor.node.parent = this.popupContainer;
            this.popupHonor.showHonor(this.getCurrentGameID());
            App.instance.showLoading(false);
        } else {
            this.popupHonor.showHonor(this.getCurrentGameID());
        }
    }

    actPopupHistory() {
        if (this.popupHistory == null) {
            this.popupHistory = instantiate(this.popupHistoryPrefab).getComponent(PopupHistory);
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.showHistory(this.getCurrentGameID());
            App.instance.showLoading(false);
        } else {
            this.popupHistory.showHistory(this.getCurrentGameID());
        }
    }

    actPopupGuide() {
        if (this.popupGuide == null) {
            this.popupGuide = instantiate(this.popupGuidePrefab).getComponent(Dialog);
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
            App.instance.showLoading(false);
        } else {
            this.popupGuide.show();
        }
    }

    actGoToTaiXiuJPLive() {
        App.instance.openGame(Config.InGameIds.TaiXiuLive);
    }
}
