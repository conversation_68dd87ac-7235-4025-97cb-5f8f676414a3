import { _decorator, Component, Node, Label, SpriteFrame, instantiate, Sprite, tween, v3, Tween } from 'cc';
import SignalRClient from "db://assets/Lobby/scripts/common/networks/Network.SignalRClient";
import MiniGameTXSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXSignalRClient";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
const {ccclass, property, menu} = _decorator;

@ccclass('TaiXiuMiniController')
@menu("TaiXiuJP/TaiXiuMiniController")
export default class TaiXiuMiniController extends Component {
    static instance: TaiXiuMiniController = null;
    @property(Node)
    remainTime: Node | null = null;
    @property(Node)
    waitingTime: Node | null = null;
    @property(Label)
    @property(Label)
    lblTotalBetTai: Label | null = null;
    @property(Label)
    lblTotalBetXiu: Label | null = null;
    // @property(sp.Skeleton)
    // bgSpine: sp.Skeleton = null;
    @property(Node)
    tai: Node | null = null;
    @property(Node)
    xiu: Node | null = null;
    @property(Node)
    score: Node | null = null;
    
    private gameID: number = 1;
    private hub: SignalRClient = null;
    @property(Node)
    private historyList: Node | null = null;
    @property(Node)
    private historyItem: Node | null = null;
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame | null = null;
    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    @property(Node)
    winTextNode: Node | null = null;
    
    onLoad() {
        TaiXiuMiniController.instance = this;
    }

    start() {
        this.tai.active = false;
        this.xiu.active = false;
        this.score.active = false;
        this.lblTotalBetTai.string = "0";
        this.lblTotalBetXiu.string = "0";
        this.waitingTime.active = false;
        this.remainTime.active = false;
    }

    initHubs(gameID: number) {
        this.gameID = gameID;
        this.hub = MiniGameTXSignalRClient.getInstance();

        this.hub.receive("currentSessionLD", (res) => {
            if (res.GameID != this.gameID) {
                return;
            }

            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);

            if (res.GameStatus == 1) {
                if (res.RemainBetting == 60) {
                    this.tai.active = false;
                    this.xiu.active = false;
                    this.score.active = false;
                    this.lblTotalBetTai.string = "0";
                    this.lblTotalBetXiu.string = "0";
                    this.waitingTime.active = false;
                }

                this.remainTime.active = true;

                let secondRemainBetting = res.RemainBetting;
                this.unschedule(this.countdownRemainTime);
                this.schedule(this.countdownRemainTime = () => {
                    try {
                        if (secondRemainBetting < 0) {
                            this.unschedule(this.countdownRemainTime);
                            return;
                        }

                        this.remainTime.getComponent(Label).string = secondRemainBetting < 10 ? "0" + secondRemainBetting : "" + secondRemainBetting;


                        secondRemainBetting--;
                    } catch (e: any) {
                        this.unschedule(this.countdownRemainTime);
                    }
                }, 1);

            } else {
                this.remainTime.active = false;
                this.waitingTime.active = true;

                let secondRemainWaiting = res.RemainWaiting;
                this.unschedule(this.countdownWaitingTime);
                this.schedule(this.countdownWaitingTime = () => {
                    try {
                        if (secondRemainWaiting < 0) {
                            this.unschedule(this.countdownWaitingTime);
                            return;
                        }

                        this.waitingTime.getChildByName("time").getComponent(Label).string = secondRemainWaiting < 10 ? "0" + secondRemainWaiting : "" + secondRemainWaiting;

                        secondRemainWaiting--;
                    } catch (e: any) {
                        this.unschedule(this.countdownWaitingTime);
                    }
                }, 1);
            }
        });

        this.hub.receive("currentResultLD", (res) => {
            if (res.GameID != this.gameID) {
                return;
            }

            this.score.active = true;
            this.score.getComponent(Label).string = res.Dice1 + res.Dice2 + res.Dice3 + "";
            Tween.stopAllByTarget(this.tai);
            Tween.stopAllByTarget(this.xiu);

            var nodeResult = null;
            if (res.LocationIDWin == 1) {
                this.tai.active = false;
                this.xiu.active = true;
                nodeResult = this.xiu;
            } else if (res.LocationIDWin == 2) {
                this.tai.active = true;
                this.xiu.active = false;
                nodeResult = this.tai;
            }

            tween(nodeResult)
                .repeatForever(
                    tween()
                        .to(0.25, {scale: v3(0.4, 0.4, 0.4)}, {easing: "quadOut"})
                        .to(0.2, {scale: v3(0.315, 0.315, 0.315)}, {easing: "quadIn"})
                        .to(0.15, {scale: v3(0.385, 0.385, 0.385)}, {easing: "sineOut"})
                        .to(0.1, {scale: v3(0.35, 0.35, 0.35)}, {easing: "sineInOut"})
                )
                .start();

        });

        this.hub.receive("currentRoomsInfoLD", (res) => {
            for (var i = 0; i < res.length; i++) {
                if (res[i].GameID != this.gameID) {
                    continue;
                }

                this.lblTotalBetTai.string = Utils.formatMoney(res[i].TotalBetValue2, true);
                this.lblTotalBetXiu.string = Utils.formatMoney(res[i].TotalBetValue1, true);
            }
        });

        this.hub.receive("gameHistoryLD", (res) => {
            if (res == null || res.length == 0) {
                return;
            }

            if (res[0].GameID == this.gameID) {
                this.historyList.removeAllChildren();
            } else {
                return;
            }

            for (var i = res.length - 1; i >= 0; i--) {
                if (res[i].GameID != this.gameID) {
                    continue;
                }

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;

                this.historyList.addChild(item);
            }
        });

        this.hub.receive("resultOfAccountLD", (res) => {
            let totalPrize = 0;

            res.forEach((item: any) => {
                if (item.GameID === this.gameID && item.PrizeValue > 0) {
                    totalPrize += item.PrizeValue;
                }
            });

            if (totalPrize <= 0) {
                this.winTextNode.active = false;
                return;
            }

            this.winTextNode.active = true;
            this.winTextNode.getComponent(Label).string = '+ ' + Utils.formatNumber(totalPrize);
            this.winTextNode.position = v3(this.winTextNode.x, -50);
            tween(this.winTextNode)
                .to(1, {position: v3(this.winTextNode.x, 20)})
                .call(() => {
                    this.winTextNode.active = false;
                })
                .start();
        });
    }
}
