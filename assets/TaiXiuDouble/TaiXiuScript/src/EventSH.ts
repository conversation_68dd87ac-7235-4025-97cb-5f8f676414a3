import {_decorator, Node, Toggle, SpriteFrame, Sprite, instantiate, Label, EditBox, Tween, color, tween, v3} from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;


@ccclass('EventSH')
@menu("TaiXiuJP/EventSH")
export class EventSH extends Dialog {
    @property([Node])
    tabContentsEventSH: Node[] = [];
    @property([Toggle])
    tabTogglesEventSH: Toggle[] = [];
    private currentTabEventSH: number = 0;
    // Account
    @property(Node)
    listAccount: Node | null = null;
    @property(Node)
    itemAccount: Node | null = null;
    // Top
    @property(Node)
    listTop: Node | null = null;
    @property(Node)
    itemTop: Node | null = null;
    @property(SpriteFrame)
    sprFrameBox1: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameBox2: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameBox3: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameBox4: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameBox5: SpriteFrame | null = null;
    @property(SpriteFrame)
    BG1: SpriteFrame | null = null;
    @property(SpriteFrame)
    BG2: SpriteFrame | null = null;
    @property(Node)
    selectedBoxes: Node | null = null;
    private gameID: number = 0;
    private prizeIDTop: number = 1;
    private serverNames: string[] = ['HongKong', 'LasVegas', 'Macau'];
    private countdownIntervals= [];

    showDetail(gameID: number) {
        this.gameID = gameID;

        this.show();
    }

    show() {
        super.show();

        this.loadDataAPIAccount();
        this.loadDataAPITop(null, this.prizeIDTop);
    }

    onEnable() {
        this.listAccount.removeAllChildren();
        this.listTop.removeAllChildren();
    }

    selectedToggleTabs(toggle: Toggle, index: string) {
        let tabIndex = parseInt(index);

        if (this.currentTabEventSH === tabIndex) {
            return;
        }
        this.showTab(tabIndex);

        if (toggle.isChecked == false) return;

        if (tabIndex === 0) {
            this.loadDataAPIAccount();
        } else if (tabIndex === 1) {
            this.loadDataAPITop(null, this.prizeIDTop);
        }
    }

    showTab(tabIndex: number) {
        this.currentTabEventSH = tabIndex;

        this.tabContentsEventSH.forEach((tab, i) => {
            tab.active = i === tabIndex;
        });

        this.tabTogglesEventSH.forEach((toggle, i) => {
            this.updateToggleTextColor(toggle, i === tabIndex);
        });
    }

    loadDataAPIAccount() {
        App.instance.showLoading(true);

        Http.get(Configs.App.DOMAIN_CONFIG['GetEventChestAccountInfo'], {
            currencyID: Configs.Login.CurrencyID,
            GameID: this.gameID,
            onlyNew: false
        }, (_status, res) => {
            this.listAccount.removeAllChildren();

            if (res.c < 0) {
                App.instance.showLoading(false);
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            if (res.d.ListChest.length === 0) {
                App.instance.showLoading(false);
                return;
            }

            res.d.ListChest.forEach((item: any, index: any) => {
                if (item.GameID != this.gameID) {
                    return;
                }

                var node = instantiate(this.itemAccount);
                node.getComponent(Sprite).spriteFrame = (index % 2 === 0) ? this.BG1 : this.BG2;

                var status: string;
                var award: string = "";

                if (item.PrizeValue === 0 && item.SpecialGift === 0) {
                    if (item.ExpireTimeCount < 0) {
                        status = App.instance.getTextLang('ev45');
                    } else {
                        status = App.instance.getTextLang('ev42');
                    }
                } else {
                    if (item.PrizeValue > 0) {
                        award = Utils.formatNumber(item.PrizeValue) + ' TIPZO';
                    } else {
                        if (item.SpecialGift == 1) {
                            award = "SH";
                        } else if (item.SpecialGift == 2) {
                            award = "IPhone";
                        }
                    }
                    var awardTime = item.AwardTimeCount;

                    if (awardTime > 0) {
                        status = App.instance.getTextLang('ev43');
                        clearInterval(this.countdownIntervals[index]);
                        this.countdownIntervals[index] = setInterval(() => {
                            if (awardTime < 0) {
                                clearInterval(this.countdownIntervals[index]);
                                node.getChildByName("award").getComponent(Label).string = award + "\n(00:00)";
                                return;
                            }

                            var minutes = Math.floor(awardTime / 60);
                            var seconds = awardTime % 60;
                            node.getChildByName("award").getComponent(Label).string = award + `\n(${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds})`;
                            awardTime--;
                        }, 1000);

                    } else {
                        status = App.instance.getTextLang('ev44');
                    }
                }

                node.getChildByName("id").getComponent(Label).string = `${item.ChestID}\n${this.serverNames[item.GameID - 1]}`;
                node.getChildByName("session").getComponent(Label).string = item.BetsData.map((bet: any) => `#${bet.GameSessionID}`).join('\n');
                node.getChildByName("betValue").getComponent(Label).string = item.BetsData.map((bet: any) => `${Utils.formatNumber(bet.BetValue)}`).join('\n');
                node.getChildByName("time").getComponent(Label).string = item.AwardTimeCount > 0 ? Utils.formatDatetime(item.AwardTime, 'dd/MM/yyyy HH:mm:ss') : Utils.formatDatetime(item.ExpireTime, 'dd/MM/yyyy HH:mm:ss');
                node.getChildByName("gift").getChildByName("prize").getComponent(Sprite).spriteFrame = this[`sprFrameBox${item.PrizeID}`];
                node.getChildByName("status").getComponent(Label).string = status;
                node.getChildByName("award").getComponent(Label).string = award;
                node.active = true;
                this.listAccount.addChild(node);
            });

            App.instance.showLoading(false);
        });
    }

    loadDataAPITop(_event: any, prizeID: any = "1") {
        App.instance.showLoading(true);
        this.prizeIDTop = parseInt(prizeID);

        this.selectedBoxes.children.forEach((box: Node, index: number) => {
            if (index + 1 !== this.prizeIDTop) {
                Tween.stopAllByTarget(box);
            } else {
                tween(box)
                    .repeatForever(
                        tween()
                            .to(0.4, {scale: v3(1.15, 1.15, 1.15)}, {easing: 'sineOut'})
                            .to(0.2, {scale: v3(0.95, 0.95, 0.95)}, {easing: 'sineIn'})
                            .to(0.3, {scale: v3(1.05, 1.05, 1.05)}, {easing: 'sineOut'})
                            .to(0.5, {scale: v3(1.0, 1.0, 1.0)}, {easing: 'sineInOut'})
                    )
                    .start();
            }
        });

        Http.get(Configs.App.DOMAIN_CONFIG['GetTopEventChest'], {
            currencyID: Configs.Login.CurrencyID,
            GameID: this.gameID,
            prizeID: this.prizeIDTop,
            topCount: 20
        }, (_status, res) => {
            this.listTop.removeAllChildren();
            if (res.c < 0) {
                App.instance.showLoading(false);
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }
            res.d.forEach((item: any, index: any) => {
                var node = instantiate(this.itemTop);
                const bg1 = node.getChildByName("bg1");
                const bg2 = node.getChildByName("bg2");
                const content = node.getChildByName("content");
                if (bg1 && bg2) {
                    bg1.active = index % 2 === 0;
                    bg2.active = index % 2 !== 0;
                }
                var award: string;
                if (item.PrizeValue === 0 && item.SpecialGift === 0) {
                    award = "";
                } else {
                    if (item.PrizeValue > 0) {
                        award = Utils.formatNumber(item.PrizeValue);
                    } else {
                        if (item.SpecialGift == 1) {
                            award = "SH";
                        } else if (item.SpecialGift == 2) {
                            award = "Iphone";
                        } else {
                            award = "";
                        }
                    }
                }

                content.getChildByName("time").getComponent(Label).string = Utils.formatDatetime(item.AwardTime, 'dd/MM/yyyy HH:mm:ss');
                content.getChildByName("account").getComponent(Label).string = item.Nickname;
                content.getChildByName("gift").getChildByName("prize").getComponent(Sprite).spriteFrame = this[`sprFrameBox${item.PrizeID}`];
                content.getChildByName("award").getComponent(Label).string = award;

                this.listTop.addChild(node);
            });

            App.instance.showLoading(false);
        });
    }

    private updateToggleTextColor(toggle: Toggle, isActive: boolean) {
        let textNode = toggle.node.getChildByName("text");
        if (textNode) {
            let label = textNode.getComponent(Label);
            if (label) {
                label.color = isActive ? color(255, 240, 0) : color(255, 255, 255);
            }
        }
    }

    _onDismissed() {
        var edits = this.node.getComponentsInChildren(EditBox);

        for (var i = 0; i < edits.length; i++) {
            edits[i].tabIndex = -1;
        }
        this.node.active = false;
    }
}
