import {_decorator, Node, Label, instantiate, UIOpacity, Button} from 'cc';
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;


@ccclass('PopupHistoryJackpot')
@menu("TaiXiuJP/PopupHistoryJackpot")
export class PopupHistoryJackpot extends Dialog {
    @property(Label) lblSession: Label = null;
    @property(Label) lblSessionDate: Label = null;
    @property(Label) lblTotalAccounts: Label = null;
    @property(Node) containerDetail: Node = null;
    @property(Node) itemContainer: Node = null;
    @property(Node) itemPrefab: Node = null;
    @property(Label) lblTotalJackpotValue: Label = null;
    @property(Node) itemTemplate: Node = null;
    @property(Node)
    btnNext: Node = null;
    @property(Node)
    btnPrev: Node = null;

    private gameID: number = 1;
    private sessionList: any[] = [];
    private currentSessionIndex: number = 0;

    showDetail(gameID: number) {
        this.gameID = gameID;
        this.show();
    }

    show() {
        super.show();
        this.itemPrefab.active = false;
        this.lblTotalAccounts.string = '0';
        this.lblSession.string = App.instance.getTextLang('txt_session') + `: #0`;
        this.lblSessionDate.string = '';
        this.containerDetail.active = false;
        this.lblTotalJackpotValue.string = App.instance.getTextLang('txt_jackpot_prize_jackpot') + ' 0';
        this.getDataSession();
        this.btnNext.getComponent(UIOpacity).opacity = 128;
        this.btnNext.getComponent(Button).interactable = false;
    }

    private getDataSession() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceJackPotHistory'], {
            gameID: this.gameID,
            topCount: 50
        }, (_status, res) => {
            App.instance.showLoading(false);
            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            this.sessionList = res.d;
            this.currentSessionIndex = 0;
            this.populateSession();
        });
    }

    private populateSession() {
        if (!this.sessionList || this.sessionList.length === 0) {
            return;
        }

        this.containerDetail.active = true;
        const session = this.sessionList[this.currentSessionIndex];
        this.lblSession.string = App.instance.getTextLang('txt_session') + `: #${session.GameSessionID}`;
        this.lblSessionDate.string = Utils.formatDatetime(session.FinishedTime, 'dd/MM/yyyy');
        this.lblTotalAccounts.string = session.TotalAccounts.toString();
        this.containerDetail.getChildByName('tx-t').active = session.LocationJackpot === 2;
        // this.containerDetail.getChildByName('t').active = session.GameResults === '6,6,6';
        this.containerDetail.getChildByName('tx-x').active = session.LocationJackpot === 1;
        // this.containerDetail.getChildByName('x').active = session.GameResults === '1,1,1';
        this.lblTotalJackpotValue.string = App.instance.getTextLang('txt_jackpot_prize_jackpot') + ' ' + Utils.formatNumber(session.TotalJackpotAward);
        this.getJackpotAccount(session.GameSessionID);
    }

    private getJackpotAccount(sessionID: number) {
        App.instance.showLoading(true);
        this.itemContainer.removeAllChildren();
        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceJackPotAccount'], {
            gameID: this.gameID,
            GameSessionID: sessionID
        }, (_status, res) => {
            App.instance.showLoading(false);
            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            res.d.forEach((item: any, index: any) => {
                const itemNode = instantiate(this.itemPrefab);
                const bg1 = itemNode.getChildByName("bg1");
                const bg2 = itemNode.getChildByName("bg2");
                const content = itemNode.getChildByName("content");
                if (bg1 && bg2) {
                    bg1.active = index % 2 === 0;
                    bg2.active = index % 2 !== 0;
                }

                content.getChildByName("account").getComponent(Label).string = item.Nickname;
                content.getChildByName("bet").getComponent(Label).string = Utils.formatNumber(item.BetValue);
                content.getChildByName("prize").getComponent(Label).string = Utils.formatNumber(item.JackpotValue);
                itemNode.active = true;
                itemNode.parent = this.itemContainer;
            });
        });
    }


    public nextSession(event?: Event) {
        this.currentSessionIndex = Math.max(this.currentSessionIndex - 1, 0);
        this.populateSession();
        this.updateButtonState();
    }

    public prevSession() {
        this.currentSessionIndex = Math.min(this.currentSessionIndex + 1, this.sessionList.length - 1);
        this.populateSession();
        this.updateButtonState();
    }

    private updateButtonState() {
        const isFirst = this.currentSessionIndex === 0;
        const isLast = this.currentSessionIndex === this.sessionList.length - 1;

        if (this.btnNext) {
            const uiOpacityNext = this.btnNext.getComponent(UIOpacity);
            if (uiOpacityNext) uiOpacityNext.opacity = isFirst ? 128 : 255;

            const buttonNext = this.btnNext.getComponent(Button);
            if (buttonNext) buttonNext.interactable = !isFirst;
        }

        if (this.btnPrev) {
            const uiOpacityPrev = this.btnPrev.getComponent(UIOpacity);
            if (uiOpacityPrev) uiOpacityPrev.opacity = isLast ? 128 : 255;

            const buttonPrev = this.btnPrev.getComponent(Button);
            if (buttonPrev) buttonPrev.interactable = !isLast;
        }
    }
}
