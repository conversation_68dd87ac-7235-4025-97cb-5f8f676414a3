import {_decorator, Node, SpriteFrame, instantiate, Label, Sprite, Toggle} from "cc";
import Dialog from "db://assets/Lobby/scripts/common/Dialog";
import App from "db://assets/Lobby/scripts/common/App";
import Configs from "db://assets/Lobby/scripts/common/Config";
import Http from "db://assets/Lobby/scripts/common/Http";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass('TaiXiuJPPopupHonors')
@menu("TaiXiuJP/PopupHonors")
export class PopupHonors extends Dialog {
    @property(Node)
    item_top1: Node | null = null;
    @property(Node)
    item_top2: Node | null = null;
    @property(Node)
    item_top3: Node | null = null;
    @property(Node)
    item: Node | null = null;
    @property(Node)
    items: Node | null = null;
    @property(SpriteFrame)
    sprFrameGold: SpriteFrame | null = null;
    @property(SpriteFrame)
    sprFrameCoin: SpriteFrame | null = null;
    @property([Toggle])
    toggleContainer: Toggle[] = [];
    private isGold = true;
    private gameID = 2; // 1: HK, 2: LVG, 3: MC
    private page: number = 1;
    private per_page: number = 10;

    showHonor(gameID: number) {
        this.gameID = gameID;
        super.show();
        if (this.toggleContainer[this.gameID - 1].isChecked === false) {
            this.toggleContainer[this.gameID - 1].isChecked = true;
        }
    }

    onChangeGameID(toggle: Toggle, data: any) {
        if (toggle.isChecked === false) return;
        this.gameID = parseInt(data);
        this.page = 1;
        this.loadData();
    }

    onChangeBetType() {
        this.isGold = !this.isGold;
        this.page = 1;

        this.loadData();
    }

    private loadData() {
        App.instance.showLoading(true);
        this.items.removeAllChildren();

        const params = {
            currencyID: Configs.Login.CurrencyID,
            gameID: this.gameID,
            betType: this.isGold ? 1 : 2,
            topCount: 50,
        };

        Http.get(Configs.App.DOMAIN_CONFIG['GetTopDailyWinnersLuckyDice'], params, (status, res) => {
            if (status === 200) {
                var data = res.d;
                for (var i = 0; i < data.length; i++) {
                    var node = instantiate(this.item);

                    if (i == 0) {
                        node = instantiate(this.item_top1);
                    } else if (i == 1) {
                        node = instantiate(this.item_top2);
                    } else if (i == 2) {
                        node = instantiate(this.item_top3);
                    } else {
                        node.getChildByName("rank").getComponent(Label).string = (i + 1).toString();
                    }

                    node.getChildByName("label2").getComponent(Label).string = data[i].Nickname;
                    node.getChildByName("label3").getChildByName("text").getComponent(Label).string = Utils.formatNumber(data[i].PrizeValue);
                    node.getChildByName("label3").getChildByName("coin").getComponent(Sprite).spriteFrame = this.isGold ? this.sprFrameGold : this.sprFrameCoin;

                    this.items.addChild(node);
                }
            }

            App.instance.showLoading(false);
        });
    }
}
