[{"__type__": "cc.Prefab", "_name": "PopupEventSH", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "PopupEventSH", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}], "_active": true, "_components": [{"__id__": 611}, {"__id__": 613}, {"__id__": 615}, {"__id__": 617}, {"__id__": 619}], "_prefab": {"__id__": 621}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 5}, {"__id__": 7}, {"__id__": 9}], "_prefab": {"__id__": 11}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 4}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7dlJPpRvJENoLo667tC/Th"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 6}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6YDupKD5MN7LMa2REihyj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 8}, "_opacity": 128, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78motHivBEtKgf6rQfKMrD"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 10}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eJ3EGdqtBU7PL2B8ue44U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7dJ0/i/U5JXb+OZcSM6wYL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 24}, {"__id__": 34}, {"__id__": 127}, {"__id__": 315}, {"__id__": 540}], "_active": true, "_components": [{"__id__": 604}, {"__id__": 606}, {"__id__": 608}], "_prefab": {"__id__": 610}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}, {"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": 733.435, "y": 399.33, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7a782e51-5907-4722-b5aa-e93a8b6e57cc@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c1cu0bTBAZrBHnrGOjqn/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "clickEvents": [{"__id__": 18}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 13}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92MrmPTztM+7lP4VHhqPqY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 20}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbt+Ur/GZAZIenFkCyhRAa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 22}, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aarViqP89Lr4JiCRKMgCAE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "28kY2qHLJE476tWcNSNVYA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Tittle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}, {"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": -0.622, "y": 397.23, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "SỰ KIỆN CHƠI HAY NHẬN NGAY SH", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36.348958333333336, "_fontSize": 35, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35lGAUhmVNDa+1Vlju2DHh"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 28}, "id": "txt_event_play_and_win", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fCtb+ziFIjLAXuRqb2El4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 30}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49IkfYaaVJaoLktnS8JZOT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 564.6628557547642, "height": 50.400000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9a/+JkvflLWaz8HvQKdiXa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dgOQxg+JANayJEP2fkiSK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "SwitchType", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 35}, {"__id__": 62}, {"__id__": 89}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 126}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 278.744, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BXH Ngay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [{"__id__": 36}, {"__id__": 44}], "_active": true, "_components": [{"__id__": 54}, {"__id__": 57}, {"__id__": 59}], "_prefab": {"__id__": 61}, "_lpos": {"__type__": "cc.Vec3", "x": -276.66666666666663, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "button 4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "_prefab": {"__id__": 43}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.92, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 38}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d5635258-7fa5-4243-becf-21dbba1d2387@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eGFoCwNpDz7i7h8XV5L/Q"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 40}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eFEiHtaJBRLg+qKZKyorN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 36}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfSpGzPrRNaazHbzOnsWBe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b6LwIDH8tGEYrYXGixAUdL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 35}, "_children": [], "_active": true, "_components": [{"__id__": 45}, {"__id__": 47}, {"__id__": 49}, {"__id__": 51}], "_prefab": {"__id__": 53}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 46}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 240, "b": 0, "a": 255}, "_string": "LỊCH SỬ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 29.079166666666666, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cLLQVQ49FGLhlkPwtUmSN"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 48}, "id": "pr1", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "803oUFcNdNHLzNDmK5RvY1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 50}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cawEgkRItMHrvW4i4Ow5Oa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 44}, "_enabled": true, "__prefab": {"__id__": 52}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 44.1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1PViQfdhCr7zGeABahG8C"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "72bW55wkNPjqAKT0XF3MYV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 55}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 35}, "checkEvents": [{"__id__": 56}], "_isChecked": true, "_checkMark": {"__id__": 37}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9b74DLJeZLWJDiJFopODMG"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "selected<PERSON>oggleTabs", "customEventData": "0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 58}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdiorSv/dJyYUm45bdRPzb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 35}, "_enabled": true, "__prefab": {"__id__": 60}, "_contentSize": {"__type__": "cc.Size", "width": 276.6666666666667, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05zcvDteRAbJYE4z9PO/5Z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfHxbY4otCfbzPUIAzHlEV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "BXH CHUNGCUOC", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [{"__id__": 63}, {"__id__": 71}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 84}, {"__id__": 86}], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": 5.684341886080802e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "button 4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 62}, "_children": [], "_active": false, "_components": [{"__id__": 64}, {"__id__": 66}, {"__id__": 68}], "_prefab": {"__id__": 70}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.92, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 65}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d5635258-7fa5-4243-becf-21dbba1d2387@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5ExWJFaNODqMFj9r4EIfV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 67}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3da4bQVy1NiLN7LQ0RqQMM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 69}, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "332Zx3zhpA86H7KD2hBzdU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f9zqTd3mFOXqUEYdOujib6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 62}, "_children": [], "_active": true, "_components": [{"__id__": 72}, {"__id__": 74}, {"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 73}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "THỐNG KÊ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 29.079166666666666, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4eP4B8padJTLlIXXCE62c3"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 75}, "id": "txt_statistics", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d18NtRSz5AO4W5X1YJYHB+"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 77}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33qJnJTw5I7qbeiDJod7XX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 71}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 134.5708022505015, "height": 44.099999999999994}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95I0QjAzVBrKFfccb0Hyox"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b0bxMbPgJIL5gkafTTmAL8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 82}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 62}, "checkEvents": [{"__id__": 83}], "_isChecked": false, "_checkMark": {"__id__": 64}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abl4KZE1BLoaeWoRwO8Z6R"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "selected<PERSON>oggleTabs", "customEventData": "1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 85}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffDQYtZF5Aa4zHV53KqB/E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 87}, "_contentSize": {"__type__": "cc.Size", "width": 276.6666666666667, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89NoQsPD1NQo8lcYvGuSGn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96HXvmb7ZLkYmDnMJlH5QR", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "THELE", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 34}, "_children": [{"__id__": 90}, {"__id__": 98}], "_active": true, "_components": [{"__id__": 108}, {"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 276.66666666666674, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "button 4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 89}, "_children": [], "_active": false, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 95}], "_prefab": {"__id__": 97}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -2.92, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 92}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d5635258-7fa5-4243-becf-21dbba1d2387@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40GRCFh5dLaoTZxif//Fv8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 94}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f07qIKvHpMKJbpM02ArSLX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 90}, "_enabled": true, "__prefab": {"__id__": 96}, "_contentSize": {"__type__": "cc.Size", "width": 298, "height": 93}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6enAHbqHBKLIT3ux/jLzhS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0c51AJ3QtLZJBoqoBDTITJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "text", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 89}, "_children": [], "_active": true, "_components": [{"__id__": 99}, {"__id__": 101}, {"__id__": 103}, {"__id__": 105}], "_prefab": {"__id__": 107}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 100}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "THỂ LỆ", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 29.079166666666666, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 35, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bDztgNc5CXqP2BND2vhIv"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 102}, "id": "txt_rules", "isUpperCase": true, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44SaPoPLpHNbX4G3NVV5kG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 104}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41QM8W3g5PQZQFpWtxRQqK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 98}, "_enabled": true, "__prefab": {"__id__": 106}, "_contentSize": {"__type__": "cc.Size", "width": 90.05555239938566, "height": 44.099999999999994}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "995k2shw9PhJjVDnEmncov"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04wh9/0KlNSqMv5OQEh8Oy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 109}, "clickEvents": [], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 89}, "checkEvents": [{"__id__": 110}], "_isChecked": false, "_checkMark": {"__id__": 91}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6aCZcjsCpL5pwRuOHb+thU"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "selected<PERSON>oggleTabs", "customEventData": "2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 112}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0h8Bg8U5KS7bR7ZxIVSpt"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 114}, "_contentSize": {"__type__": "cc.Size", "width": 276.6666666666667, "height": 70}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "299I7awh1BHKuLXgO5zeVk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62dNzWa7pLwpg8raueCQ9/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 117}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f60f007e-19a2-460d-9806-dae431363ada@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a80ARwUsNKKKbkgmLf8eqj"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 119}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fj80qn3BPF6h2uRRvAAKy"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 121}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e4z2QTJaJJj5YpH1mMGqVl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 123}, "_contentSize": {"__type__": "cc.Size", "width": 830, "height": 72}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "880TN7gHtBCZuoKfOfNj4Z"}, {"__type__": "cc.ToggleContainer", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 125}, "_allowSwitchOff": false, "checkEvents": [], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6b+ltQhdhPq6lwoGAprIBX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1fd32JCjpKR5Gvxos+4biW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ContentHistory", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 128}, {"__id__": 206}], "_active": true, "_components": [{"__id__": 308}, {"__id__": 310}, {"__id__": 312}], "_prefab": {"__id__": 314}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 16.278999999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 127}, "_children": [{"__id__": 129}, {"__id__": 137}, {"__id__": 147}, {"__id__": 157}, {"__id__": 167}, {"__id__": 177}, {"__id__": 187}], "_active": true, "_components": [{"__id__": 197}, {"__id__": 199}, {"__id__": 201}, {"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 151.509, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "STT", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": -656.5714285714286, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ID", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cZrAws3ZM0rPxUhHLwNS9"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18q5Zvk0JH+rDqhZI4ElMh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 135}, "_contentSize": {"__type__": "cc.Size", "width": 28.***************, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3dDFQh5hOLbFMAOFjwxLK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "316hw94IRNP43zKKXsos7/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ACCOUNT", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 138}, {"__id__": 140}, {"__id__": 142}, {"__id__": 144}], "_prefab": {"__id__": 146}, "_lpos": {"__type__": "cc.Vec3", "x": -437.**************, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 139}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdyupYuS9LDoNTVeg5CdaJ"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 141}, "id": "txt_session", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1EqwqX5ZHIZZYs3rmFkfg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 143}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7g8rpEaZCCbfCdjsxtgCy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 145}, "_contentSize": {"__type__": "cc.Size", "width": 77.28426000658224, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afN+H480xAs5BZGkT6R9kv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cdM0oCmDBPWqidGQfsa7CL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TOTALWIN", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}], "_prefab": {"__id__": 156}, "_lpos": {"__type__": "cc.Vec3", "x": -218.85714285714278, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 149}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Tổng đặt (Khớp)", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "42NpOTqTtNkIS2ZQJh+p/g"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 151}, "id": "txt_total_bet_matched", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08NxcxhfVOLru0x5g/WMix"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 153}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6537yvOc9GB4GzYOJykTgV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 147}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 216.97296479281593, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0BWILHE1JJL3w3AQnyDQf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dhnxAfKxEB7i/Kl3UAP4Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TOTAL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 158}, {"__id__": 160}, {"__id__": 162}, {"__id__": 164}], "_prefab": {"__id__": 166}, "_lpos": {"__type__": "cc.Vec3", "x": 8.526512829121202e-14, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 159}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ddGYeY8VAArvNKbi8oIMU"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 161}, "id": "txt_time", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82criOx0RBALVUXUvUGsRt"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 163}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90wrN7EV9D2L6XmmipKroN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 157}, "_enabled": true, "__prefab": {"__id__": 165}, "_contentSize": {"__type__": "cc.Size", "width": 124.95532985675777, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9eLF6BNNHI6/hMZDh5bRH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecouqK8INP9ZCH55U0Jy1g", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PHIEN", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 168}, {"__id__": 170}, {"__id__": 172}, {"__id__": 174}], "_prefab": {"__id__": 176}, "_lpos": {"__type__": "cc.Vec3", "x": 218.85714285714295, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 169}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Thưởng hộp", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58K1z4VzRJmYuBF3u+sDgf"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 171}, "id": "ev38", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32m6ngnVNNwZS9cnogWWQs"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 173}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cAuGvlRxP3atNpX8Cn9FS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 175}, "_contentSize": {"__type__": "cc.Size", "width": 160.69031802438565, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90niRD37hLvrEtJqCaoUyE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1bJvgWELZMdogm08BF3gmV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TrangThai", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 178}, {"__id__": 180}, {"__id__": 182}, {"__id__": 184}], "_prefab": {"__id__": 186}, "_lpos": {"__type__": "cc.Vec3", "x": 437.71428571428584, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 179}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> thái", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69tuNKUkpFJ7eu/oD/Fq4Z"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 181}, "id": "txt_status", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaAF0uJ2RLtJ7QeemiqwJg"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 183}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5315uY/31C748HTDrZr2Na"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 177}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 135.4561585146063, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1f8GueOMtLMo94IIgBkobM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38LeX1egRKa4xJ1dwfIJIC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GIAITHUONG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 188}, {"__id__": 190}, {"__id__": 192}, {"__id__": 194}], "_prefab": {"__id__": 196}, "_lpos": {"__type__": "cc.Vec3", "x": 656.5714285714287, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 189}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Giải thưởng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 31.15625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dc6ulHnuxMQ7bdgI0Lda9L"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 191}, "id": "tp59", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4dmbSepA5OfrFBLIDh3bCD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 193}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92X5YLjxdFq5YuuIPr0X3u"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 187}, "_enabled": true, "__prefab": {"__id__": 195}, "_contentSize": {"__type__": "cc.Size", "width": 155.43260151548395, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9ITGo1wNA4o9vr6r83Bc1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eaxjo5OfVJYaAdo0O+dDGW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 198}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f42f5ee0-2b69-46f6-ad92-be4264ff0a1e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91siRw5ElKObbo8pT6WG0M"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 200}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f91XBza5NB9IutSUZWX6Nu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 202}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71jSUvwXpH36JSbWzESKQW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 204}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbJesr0mdElrIO5V/GgGM7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "08A3W222xBZK66zAgCp/JS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 127}, "_children": [{"__id__": 207}], "_active": true, "_components": [{"__id__": 301}, {"__id__": 303}, {"__id__": 305}], "_prefab": {"__id__": 307}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 120, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 206}, "_children": [{"__id__": 208}], "_active": true, "_components": [{"__id__": 290}, {"__id__": 292}, {"__id__": 294}, {"__id__": 296}, {"__id__": 298}], "_prefab": {"__id__": 300}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.7199999999999704, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Items", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [{"__id__": 209}], "_active": true, "_components": [{"__id__": 281}, {"__id__": 283}, {"__id__": 285}, {"__id__": 287}], "_prefab": {"__id__": 289}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ItemAccountTemplate", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [{"__id__": 210}, {"__id__": 218}, {"__id__": 226}, {"__id__": 234}, {"__id__": 242}, {"__id__": 256}, {"__id__": 264}], "_active": true, "_components": [{"__id__": 272}, {"__id__": 274}, {"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": -30, "y": -48.01, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "id", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 211}, {"__id__": 213}, {"__id__": 215}], "_prefab": {"__id__": 217}, "_lpos": {"__type__": "cc.Vec3", "x": -626.36, "y": -2.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 212}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "1000\nMacau", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32eQIHZ41FgbsxG24unUJx"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 214}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cQiCRdYpIz7raWgZxCrC7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 216}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 61.019999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ae2ai9+jdJmoIo59Wo/xs8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47J1jUTDhDG5D1jNSnb+Lc", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "session", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 219}, {"__id__": 221}, {"__id__": 223}], "_prefab": {"__id__": 225}, "_lpos": {"__type__": "cc.Vec3", "x": -408.36, "y": 11, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 220}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "#2222323", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1euPuDtJMDbZCOIzOVJ5B"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 222}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1seWIy91FcpkKifgtROEl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 224}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 34.019999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcfwddDZdNNID60miZ8N++"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6ZZq8+VJDjJMBo2yFwQNU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "betValue", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 227}, {"__id__": 229}, {"__id__": 231}], "_prefab": {"__id__": 233}, "_lpos": {"__type__": "cc.Vec3", "x": -190.36, "y": 11, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 226}, "_enabled": true, "__prefab": {"__id__": 228}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 0, "a": 255}, "_string": "2.000K", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "beF7EwhhpD1bU0I9LaAYQV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 226}, "_enabled": true, "__prefab": {"__id__": 230}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9Fh2Bmi1J/oY50qs+u+Yc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 226}, "_enabled": true, "__prefab": {"__id__": 232}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 34.019999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8b/yrowUdKe5NW4vGiOXWd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7fry5qz5RINLrXhIuKR0/v", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 235}, {"__id__": 237}, {"__id__": 239}], "_prefab": {"__id__": 241}, "_lpos": {"__type__": "cc.Vec3", "x": 33.639999999999986, "y": 12.89, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 236}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "10/10/2024 00:00:00", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63O2kdD6JPkrJsJkukQC2C"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 238}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b9r+UjWdlA0JuiqyLbgjww"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 234}, "_enabled": true, "__prefab": {"__id__": 240}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcXOSA0M5CpY6vByGMQHSA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b7092fNhRGPIJiNEBxJJFf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "gift", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [{"__id__": 243}], "_active": true, "_components": [{"__id__": 251}, {"__id__": 253}], "_prefab": {"__id__": 255}, "_lpos": {"__type__": "cc.Vec3", "x": 252.14, "y": 12.89, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "prize", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 242}, "_children": [], "_active": true, "_components": [{"__id__": 244}, {"__id__": 246}, {"__id__": 248}], "_prefab": {"__id__": 250}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.65, "y": 0.65, "z": 0.65}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 245}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "162bc419-8184-4003-b4eb-08d4802958d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1fnshvwpFTq9vlwUE7SfV"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 247}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb1pY+LGBG64ywqefhPwGo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 243}, "_enabled": true, "__prefab": {"__id__": 249}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "382cHAn4tM0oeDiiUrAc/6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84LJA+CwxLtbU1oun6utuX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": {"__id__": 252}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2b3sa/7p5MUq50yCGuSBKd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 242}, "_enabled": true, "__prefab": {"__id__": 254}, "_contentSize": {"__type__": "cc.Size", "width": 207, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6xOXXhIBOgLvvvroVjmiR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5RkVi1x9Pqq/3FTf43wb2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "status", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 259}, {"__id__": 261}], "_prefab": {"__id__": 263}, "_lpos": {"__type__": "cc.Vec3", "x": 464.64, "y": 12.89, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 258}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 0, "a": 255}, "_string": "...", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dfxCKuw1pCHo+2MK/DYePI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 260}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aMHuMhdhCTYW6qWR+mexw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 262}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "616/hev45MUYP0fHeie5Ou"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "89QJDVaLtOFocXGa9Jz6M3", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [], "_active": true, "_components": [{"__id__": 265}, {"__id__": 267}, {"__id__": 269}], "_prefab": {"__id__": 271}, "_lpos": {"__type__": "cc.Vec3", "x": 682.64, "y": 10.999999999999993, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 266}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24.925, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 27, "_overflow": 3, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bV5+0EFNPUaHwJpWB9Kuk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 268}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d85W+OpJpD1L/E24Dlg99s"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 264}, "_enabled": true, "__prefab": {"__id__": 270}, "_contentSize": {"__type__": "cc.Size", "width": 218, "height": 34.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6feTA961DRpkZyuWycGQk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8fWJuypFpEGbXLUGvu2oW9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 273}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 20, "_paddingBottom": 15, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bCXzBiYpJspWXIv9K8mv9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 275}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7425a770-af0e-4a6e-aea3-1d96b37dea37@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1Egu247FKZIQE9WGopINO"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 277}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44TtVZq2hLTLhW4PKhWufL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 279}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 96.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.48, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "806rvscOlMObV17katuR1g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ee/5bbMNpIQrZ/AujlE/63", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 282}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 684, "height": 40.7}, "_startAxis": 1, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23OstbK45CKr4M8Fv9ajNf"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 284}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 500, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 684, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7brAYcmuZCSaNKnTUfDEaH"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 286}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36tSMj3I5Fl5lc+H6+UFpu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 288}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 96.02}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cHYCRB0hNWZ1nbQdCxLXA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcmZTDKE1I14KwxJ9bPH4x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 291}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2gdQKHPlM05QxVOVGpeSx"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 293}, "_alignFlags": 45, "_target": null, "_left": 3.6415315207705135e-14, "_right": -3.6415315207705135e-14, "_top": -2.719999999999969, "_bottom": 42.7199999999999, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4HcszBHdC7J6B6Cdahkwu"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 295}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bp3Ve+KRETYj1CEHIizuN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 510.00000000000006}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "276dN7yDtDbpK+7aaq8Gdl"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 299}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21K7NwAmtDfrZ8gKAxDp8G"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00hcZKn5xN9J0XwD3cGI8b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 302}, "bounceDuration": 0.23, "brake": 0.75, "elastic": false, "inertia": false, "horizontal": false, "vertical": true, "cancelInnerEvents": false, "scrollEvents": [], "_content": {"__id__": 208}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0e9QA4s9BP8LNhMQj4mZAI"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 304}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aFeaVgR1FPqPj9vtZ8XS4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 206}, "_enabled": true, "__prefab": {"__id__": 306}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34CzPcAUxBO4L01Q3Pa/9I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7JixBssZAW6/qR+kzbfCa", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 309}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -16.279000000000014, "_bottom": 16.279000000000014, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2LOZe1nlPEreSVweE9b7m"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 311}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0KyoVW31IfoaTiDlKOdO0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 313}, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1836l2bexMNa39/pdPK/Ih"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5arKptbBtC7rbw3MPqGloq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ContentThongKe", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 316}, {"__id__": 379}, {"__id__": 429}], "_active": false, "_components": [{"__id__": 533}, {"__id__": 535}, {"__id__": 537}], "_prefab": {"__id__": 539}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 16.278999999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "SelectBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [{"__id__": 317}, {"__id__": 328}, {"__id__": 339}, {"__id__": 350}, {"__id__": 361}], "_active": true, "_components": [{"__id__": 372}, {"__id__": 374}, {"__id__": 376}], "_prefab": {"__id__": 378}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 170, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 318}, {"__id__": 320}, {"__id__": 323}, {"__id__": 325}], "_prefab": {"__id__": 327}, "_lpos": {"__type__": "cc.Vec3", "x": -185, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 319}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "162bc419-8184-4003-b4eb-08d4802958d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "882zGmxHdCIZNuEoQBi2qS"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 321}, "clickEvents": [{"__id__": 322}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58B8+FDHhKcJkgBoYZC7li"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "loadDataAPITop", "customEventData": "1"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 324}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2f0WA8l+lEUJc3oegfCBWz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 317}, "_enabled": true, "__prefab": {"__id__": 326}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1UfiuMQxIMZQ9afMjVoBZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2eXNfLIi9GUpHEYvCNK0kE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 329}, {"__id__": 331}, {"__id__": 334}, {"__id__": 336}], "_prefab": {"__id__": 338}, "_lpos": {"__type__": "cc.Vec3", "x": -92, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 330}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b5e5956a-f8ef-41d6-acdd-181a80813f5d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83aM7oHjZGcooKHeKipGbU"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 332}, "clickEvents": [{"__id__": 333}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45D/sEGVFFlKINgEWQQfkp"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "loadDataAPITop", "customEventData": "2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 335}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cALJEHblBrquX0i/b4L47"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91LR97hHJEyIljW/cgRb/E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbDDaBLVBHVpXoCSLkDHZk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 340}, {"__id__": 342}, {"__id__": 345}, {"__id__": 347}], "_prefab": {"__id__": 349}, "_lpos": {"__type__": "cc.Vec3", "x": -1, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 341}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1016579f-cdc1-4ccb-8b87-41c6995dfc9d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3wr0amyVBvYL/BUjytYOT"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 343}, "clickEvents": [{"__id__": 344}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78F/rN+d9JPJmEdCPxnd3Q"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "loadDataAPITop", "customEventData": "3"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 346}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caKv+EhVlA1LOcoArpHxVM"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 339}, "_enabled": true, "__prefab": {"__id__": 348}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c56GHdvTlGJZaWejdLOvUH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8a6asnUMBDVqsxoMMtMtX+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 351}, {"__id__": 353}, {"__id__": 356}, {"__id__": 358}], "_prefab": {"__id__": 360}, "_lpos": {"__type__": "cc.Vec3", "x": 91, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 352}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ee57112d-facf-4204-9ea2-171fdcda76d1@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24wF1Tc1hA/K+kDtQp/9TL"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 354}, "clickEvents": [{"__id__": 355}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2qfatHINDDYIYnG5NVIOm"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "loadDataAPITop", "customEventData": "4"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 357}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "223NZ3FBtEFKmr3AomIjc6"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 350}, "_enabled": true, "__prefab": {"__id__": 359}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4gPst2QNGkrGqiiM+Ahkf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "580/q2AyFBbJeH8CxheEy+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 316}, "_children": [], "_active": true, "_components": [{"__id__": 362}, {"__id__": 364}, {"__id__": 367}, {"__id__": 369}], "_prefab": {"__id__": 371}, "_lpos": {"__type__": "cc.Vec3", "x": 185, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 363}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ff274d53-41e4-4a55-868f-1b2e78ba2f11@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0HEtdtzlLfY6x9b91F5jq"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 365}, "clickEvents": [{"__id__": 366}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7H++kEw9PT7louoHYcwqn"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "66036QKeiJH271Imzo1nyOv", "handler": "loadDataAPITop", "customEventData": "5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 368}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fi97LKYFBVb4iy1lVfzqi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 370}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 67}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3G87BIZRPA5mjR7/gK8UX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "48Mzcmd65Ls42G0u4G3/ow", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 316}, "_enabled": true, "__prefab": {"__id__": 373}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 30, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64gnfcGpRJG5CM7ItHHd5s"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 316}, "_enabled": true, "__prefab": {"__id__": 375}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "25YZ7uiTBJ27EXer0DRp5z"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 316}, "_enabled": true, "__prefab": {"__id__": 377}, "_contentSize": {"__type__": "cc.Size", "width": 433, "height": 65}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aqXbaK2xCOK/y2LwX6WJ/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c2qhsUqb1AKZcEKlBlO4HB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Header", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [{"__id__": 380}, {"__id__": 390}, {"__id__": 400}, {"__id__": 410}], "_active": true, "_components": [{"__id__": 420}, {"__id__": 422}, {"__id__": 424}, {"__id__": 426}], "_prefab": {"__id__": 428}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 100, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TIME", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 381}, {"__id__": 383}, {"__id__": 385}, {"__id__": 387}], "_prefab": {"__id__": 389}, "_lpos": {"__type__": "cc.Vec3", "x": -574.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 382}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON><PERSON> gian", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34.0625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58mPCzAlhIDpFQTA2duf+v"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 384}, "id": "sl35", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "90IyyJcIZB8ZMs4NE64D8V"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 386}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08VzY5L+ZEW6X5TpRkZkjN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 380}, "_enabled": true, "__prefab": {"__id__": 388}, "_contentSize": {"__type__": "cc.Size", "width": 124.**************, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcPOZ7YedByaDhysuekGIx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcOFbfA6FF7JAjWmf0+Yk5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ACCOUNT", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 391}, {"__id__": 393}, {"__id__": 395}, {"__id__": 397}], "_prefab": {"__id__": 399}, "_lpos": {"__type__": "cc.Vec3", "x": -191.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 392}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "<PERSON><PERSON><PERSON>", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34.0625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a62b1iAXFHHovMYsBMbVCu"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 394}, "id": "sl37", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ef31kLcspGLIige7CEcGLD"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 396}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aapy9qXlJMhJ/4oCCqBKW8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 390}, "_enabled": true, "__prefab": {"__id__": 398}, "_contentSize": {"__type__": "cc.Size", "width": 130.2589592889908, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9edOhaqCpK5ZgRKpK+u+DS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c1gNNRaStP/qK3y0+/y2s/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GIFT", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 401}, {"__id__": 403}, {"__id__": 405}, {"__id__": 407}], "_prefab": {"__id__": 409}, "_lpos": {"__type__": "cc.Vec3", "x": 191.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 402}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Thưởng hộp", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34.0625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27JBY+0kBAaL4VQ68eKpFH"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 404}, "id": "ev38", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41EnBynFxFma39fbvsPFs6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 406}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edO8vl0PJFLrBKhtzYomIG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 400}, "_enabled": true, "__prefab": {"__id__": 408}, "_contentSize": {"__type__": "cc.Size", "width": 160.71078967173165, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49kd1MdflLrLZpTYB6hnMD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24ABvLqRpKJqeQtKGnZmqI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "GIAITHUONG", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 379}, "_children": [], "_active": true, "_components": [{"__id__": 411}, {"__id__": 413}, {"__id__": 415}, {"__id__": 417}], "_prefab": {"__id__": 419}, "_lpos": {"__type__": "cc.Vec3", "x": 574.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 412}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Giải thưởng", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 34.0625, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "99236d26-2bab-4fc5-9643-a72aa776b536", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9iW3GftJISa7MGXp+MN/k"}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 414}, "id": "ev40", "isUpperCase": false, "useCustomFont": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dUMdZcShEdZqSJyWwWPcK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 416}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daoVIrVG9ISoJLr/UDXkt3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 410}, "_enabled": true, "__prefab": {"__id__": 418}, "_contentSize": {"__type__": "cc.Size", "width": 155.45239033830273, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05tsQnvI9HVZTFO7SyUhqt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57GM08totPKYde63cSW7GJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 379}, "_enabled": true, "__prefab": {"__id__": 421}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f42f5ee0-2b69-46f6-ad92-be4264ff0a1e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24OxpTmZlJga+cQYoM31KP"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 379}, "_enabled": true, "__prefab": {"__id__": 423}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "14Bn1wiHVI4o08CtQRogt6"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 379}, "_enabled": true, "__prefab": {"__id__": 425}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cEmK63N1LdrPW6O5Z/sF0"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 379}, "_enabled": true, "__prefab": {"__id__": 427}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fUlfIDfdEOJPNdsSWXuox"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58fcGxmvZIYoip8ilyHQd7", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 315}, "_children": [{"__id__": 430}], "_active": true, "_components": [{"__id__": 526}, {"__id__": 528}, {"__id__": 530}], "_prefab": {"__id__": 532}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 70, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 429}, "_children": [{"__id__": 431}], "_active": true, "_components": [{"__id__": 515}, {"__id__": 517}, {"__id__": 519}, {"__id__": 521}, {"__id__": 523}], "_prefab": {"__id__": 525}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -5.684341886080802e-14, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "items", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 430}, "_children": [{"__id__": 432}], "_active": true, "_components": [{"__id__": 506}, {"__id__": 508}, {"__id__": 510}, {"__id__": 512}], "_prefab": {"__id__": 514}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "item", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 431}, "_children": [{"__id__": 433}, {"__id__": 443}, {"__id__": 453}], "_active": true, "_components": [{"__id__": 499}, {"__id__": 501}, {"__id__": 503}], "_prefab": {"__id__": 505}, "_lpos": {"__type__": "cc.Vec3", "x": -0.807, "y": -30, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 432}, "_children": [], "_active": true, "_components": [{"__id__": 434}, {"__id__": 436}, {"__id__": 438}, {"__id__": 440}], "_prefab": {"__id__": 442}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 435}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "7425a770-af0e-4a6e-aea3-1d96b37dea37@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eCA9asLdJWpRvQdl9G/BY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 437}, "_alignFlags": 40, "_target": null, "_left": 2.842170943040401e-14, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1623, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffRn7VETpMZINT5v03kLY8"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 439}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "461JzuxWBBGJ+FXUcULp0S"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 433}, "_enabled": true, "__prefab": {"__id__": 441}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4cO4CJGhxN67AkgRVcDZNz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bb8o9b/ZNW7/Alxc7Qj3m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bg2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 432}, "_children": [], "_active": true, "_components": [{"__id__": 444}, {"__id__": 446}, {"__id__": 448}, {"__id__": 450}], "_prefab": {"__id__": 452}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 445}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "635c926b-56f8-442c-a142-1a048aa02a68@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fxDI17htPspNWURnBbM8q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 447}, "_alignFlags": 40, "_target": null, "_left": 2.842170943040401e-14, "_right": -2.842170943040401e-14, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1623, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41IhImm1RA6rzCjXxAeFXJ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 449}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adr5iUladK4bGD7HZR71vJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 451}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19ZxqPaOZJzIWGUfqanyCk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "560ZoEZcVM4quoL6buU8yN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 432}, "_children": [{"__id__": 454}, {"__id__": 462}, {"__id__": 470}, {"__id__": 484}], "_active": true, "_components": [{"__id__": 492}, {"__id__": 494}, {"__id__": 496}], "_prefab": {"__id__": 498}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "time", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 455}, {"__id__": 457}, {"__id__": 459}], "_prefab": {"__id__": 461}, "_lpos": {"__type__": "cc.Vec3", "x": -574.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 456}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "08:20 10/10/2024", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27.25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fBURezWFLoYPLxNVV9a50"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 458}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddHZYMk5xGKIdqI0vtgUJJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 454}, "_enabled": true, "__prefab": {"__id__": 460}, "_contentSize": {"__type__": "cc.Size", "width": 195.********, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6bSQYqlo5K8KER9/GeduLm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "514Mb8hL5MM6KJdfV0SI7O", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "account", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 463}, {"__id__": 465}, {"__id__": 467}], "_prefab": {"__id__": 469}, "_lpos": {"__type__": "cc.Vec3", "x": -191.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 464}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "[TIPZO]colonhiue12", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27.25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1lmwRURBJE5VkUOT/+Dls"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 466}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "earR5LRhFLGom9tnqQYGrA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 462}, "_enabled": true, "__prefab": {"__id__": 468}, "_contentSize": {"__type__": "cc.Size", "width": 212.30859375, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55sdEbpaFB1JXp5G2tNzEZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56lZnXuv9GL6cakYWHlB8Y", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "gift", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [{"__id__": 471}], "_active": true, "_components": [{"__id__": 479}, {"__id__": 481}], "_prefab": {"__id__": 483}, "_lpos": {"__type__": "cc.Vec3", "x": 191.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "prize", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 470}, "_children": [], "_active": true, "_components": [{"__id__": 472}, {"__id__": 474}, {"__id__": 476}], "_prefab": {"__id__": 478}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.65, "y": 0.65, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 473}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "162bc419-8184-4003-b4eb-08d4802958d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93qp7h3l5IPL6L9oNEkwWf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 475}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87nCNmJ6JJeow3DLmMdZnS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 471}, "_enabled": true, "__prefab": {"__id__": 477}, "_contentSize": {"__type__": "cc.Size", "width": 63, "height": 63}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22UvGUOMNNMpKAGP5S6EUk"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98bg0UQ19DS6yu2xidhnHK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": {"__id__": 480}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21mmdKI4NOEbtCynLOIQzY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 470}, "_enabled": true, "__prefab": {"__id__": 482}, "_contentSize": {"__type__": "cc.Size", "width": 383, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daxFqAr6VFh5plkHMuRAKr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4c6YTvtHlOrZhlVOb/oRMd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "award", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 453}, "_children": [], "_active": true, "_components": [{"__id__": 485}, {"__id__": 487}, {"__id__": 489}], "_prefab": {"__id__": 491}, "_lpos": {"__type__": "cc.Vec3", "x": 574.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 486}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 243, "b": 0, "a": 255}, "_string": "SH 150i", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 27.25, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 24, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 0, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "87xghiNz1Nequ5GL4NhI94"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 488}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12tIR3UxFBQasGkJ4bb9R9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 490}, "_contentSize": {"__type__": "cc.Size", "width": 85.27734375, "height": 30.24}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38hgNArSdC5Ln93nqhRO6U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22vn8GE31G/5TN1Ni9rOY5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 493}, "_resizeMode": 2, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58AuBh0SRCaIV6SxS/okzG"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 495}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dbRHVCa3RE1ab/5y+TBSO2"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 453}, "_enabled": true, "__prefab": {"__id__": 497}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdcpZrU99PirkUFg9QOHAB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1ZqUjBFtKw5jhjOEBQ9x+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 500}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8f29ef7b-1fbd-4618-b24f-c8e399fe1996@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28GSexMKxMX4lesUPlEz9N"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 502}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4JrzMpYJJBbX0vrxkMNv9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 432}, "_enabled": true, "__prefab": {"__id__": 504}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11quXkm0dN0Y6D0DWL8Ce/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c4+30g4zNIm67unjQtNy+b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 507}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 684, "height": 40.7}, "_startAxis": 1, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "888lLkC5lK96WQer5/CfZK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 509}, "_alignFlags": 41, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 500, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 684, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcGXIZKshGx7XnlEzMWH2A"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 511}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94uP26iJlE7rNskn0IS/W+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 431}, "_enabled": true, "__prefab": {"__id__": 513}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7c27TQJJJBYZJZr2DSlTL3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8dVM7OihdExZvNa/veJQPA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 516}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c0MdFcowtCV4LdsHKRKBX7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 518}, "_alignFlags": 45, "_target": null, "_left": 3.6415315207705135e-14, "_right": -3.6415315207705135e-14, "_top": 5.81756864903582e-14, "_bottom": 49.99999999999994, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bkOFmh/lPI5aTucKHxFJb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 520}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7oX13RLdK6KLCjQc0MW/A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 522}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37p4V5pbhD+qfxltQxrH0b"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 430}, "_enabled": true, "__prefab": {"__id__": 524}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f52RzwD0RPSrwLuIyn+QdN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebtzgJMF5CMpRfZsUMpZ6N", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 527}, "bounceDuration": 0.23, "brake": 0.75, "elastic": false, "inertia": false, "horizontal": false, "vertical": true, "cancelInnerEvents": false, "scrollEvents": [], "_content": {"__id__": 431}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1em2oGANhPNYT44oj1KQtb"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 529}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17GMXMDw5P2bXDBa85VhJU"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 429}, "_enabled": true, "__prefab": {"__id__": 531}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 550}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02eIjBMNRDDqyZFCuLqv3g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3fu1+2xKRLxb7in2Gowssp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 534}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -16.279000000000014, "_bottom": 16.279000000000014, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "12Ul3zlr1PerRbHkAS8baj"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 536}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04fpI9fxRPJrbeWAAXbAws"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 315}, "_enabled": true, "__prefab": {"__id__": 538}, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95qaysiodO+7RabmjO7tU7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5eGq/mNgtHDbGPQOviNjVN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ContentRule", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 541}], "_active": false, "_components": [{"__id__": 597}, {"__id__": 599}, {"__id__": 601}], "_prefab": {"__id__": 603}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 16.278999999999996, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 540}, "_children": [{"__id__": 542}], "_active": true, "_components": [{"__id__": 590}, {"__id__": 592}, {"__id__": 594}], "_prefab": {"__id__": 596}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 200, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 541}, "_children": [{"__id__": 543}], "_active": true, "_components": [{"__id__": 581}, {"__id__": 583}, {"__id__": 585}, {"__id__": 587}], "_prefab": {"__id__": 589}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 2.7199999999999704, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 542}, "_children": [{"__id__": 544}, {"__id__": 554}, {"__id__": 564}], "_active": true, "_components": [{"__id__": 574}, {"__id__": 576}, {"__id__": 578}], "_prefab": {"__id__": 580}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "layout1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 543}, "_children": [], "_active": true, "_components": [{"__id__": 545}, {"__id__": 547}, {"__id__": 549}, {"__id__": 551}], "_prefab": {"__id__": 553}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 546}, "_lineHeight": 36, "_string": "<color=#ffff00>1. <PERSON><PERSON><PERSON><PERSON> gian</c>\n<PERSON><PERSON> kiện “Chơi hay nhận ngay SH”\n\n<color=#ffff00>2. <PERSON><PERSON><PERSON> tượ<PERSON></c>\nTất cả người chơi game Tài Xỉu trên cổng TIPZO bàn TIPZO\n\n<color=#ffff00>3. <PERSON><PERSON><PERSON> l<PERSON></c>\nNgười chơi tham gia game Tài Xỉu nếu đạt đủ điều kiện sẽ được tặng 5 loại hộp quà chứa nhiều phần thưởng rất giá trị.\nNgười chơi có nhiều ván thắng liên thắng liên tiếp (không cần phải các phiên liên tiếp nhau) và có tổng đặt (khớp lệnh) đạt mức tối thiểu sẽ nhận được các loại hộp tương ứng như sau:", "_horizontalAlign": 0, "_verticalAlign": 0, "_fontSize": 24, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 1520, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2Kb+I8HtOmaT+dpLAizO5"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 548}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0Z3c6iZxL8aGUuzq7QrW/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 550}, "_contentSize": {"__type__": "cc.Size", "width": 1520, "height": 369.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63Xyct5phLu7tQwX8SPxTm"}, {"__type__": "50d0eh1/MBP9bsRMPNN7Xbr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 544}, "_enabled": true, "__prefab": {"__id__": 552}, "id": "tx_event_sh_rule_1", "isUpperCase": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15IhXpYwxOc6gi8/3smSi2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9e+lb8xwNHvqmvBG3y/Mjf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 543}, "_children": [], "_active": true, "_components": [{"__id__": 555}, {"__id__": 557}, {"__id__": 559}, {"__id__": 561}], "_prefab": {"__id__": 563}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -389.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 556}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "0f816333-15c4-44c6-88b4-5ce1826b3329@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7Kwd7uPVHJ6Z8ERgMyBRS"}, {"__type__": "d12e0gfdzJKtpZd8H4K9/SU", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 558}, "viet": {"__uuid__": "0f816333-15c4-44c6-88b4-5ce1826b3329@f9941", "__expectedType__": "cc.SpriteFrame"}, "eng": {"__uuid__": "cfa10f59-c987-48c2-9176-cf1c3f8e951d@f9941", "__expectedType__": "cc.SpriteFrame"}, "thai": {"__uuid__": "cfa10f59-c987-48c2-9176-cf1c3f8e951d@f9941", "__expectedType__": "cc.SpriteFrame"}, "indo": {"__uuid__": "cfa10f59-c987-48c2-9176-cf1c3f8e951d@f9941", "__expectedType__": "cc.SpriteFrame"}, "cam": {"__uuid__": "cfa10f59-c987-48c2-9176-cf1c3f8e951d@f9941", "__expectedType__": "cc.SpriteFrame"}, "china": {"__uuid__": "cfa10f59-c987-48c2-9176-cf1c3f8e951d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cMJ7hT4RAs5RHDUO5S4qT"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 560}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22X3HrJ0BBLJS2xVZu+0ch"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 554}, "_enabled": true, "__prefab": {"__id__": 562}, "_contentSize": {"__type__": "cc.Size", "width": 1500, "height": 296}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73bU6lpItPUK1oDQGtqYy4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a6rSJJrARCD4PRSfFTw30u", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "layout3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 543}, "_children": [], "_active": true, "_components": [{"__id__": 565}, {"__id__": 567}, {"__id__": 569}, {"__id__": 571}], "_prefab": {"__id__": 573}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -1034.04, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 566}, "_lineHeight": 36, "_string": "- Những ván không khớp lệnh (hoàn trả 100% số tiền đặt) thì hệ thống sẽ không ghi nhận và bất chấp kết quả của ván chơi đó.\n- Nếu trong chuỗi thắng, người chơi đồng thời đạt điều kiện được tặng thưởng 2 hộp quà thì hệ thống sẽ tặng cho người chơi hộp quà có giá trị nhỏ nhất và kết thúc chuỗi thắng này để bắt đầu ghi nhận chuỗi thắng mới.\n\nVí dụ: người chơi thắng ván thứ nhất 1M, thắng ván thứ hai 3M, thắng ván thứ ba 5M thì người chơi cùng đạt điều kiện nhận hòm quà Vàng và Bạch Kim nhưng hệ thống sẽ trao thưởng cho người chơi hòm quà Vàng và kết thúc chuỗi thắng này để bắt đầu ghi nhận chuỗi thắng mới.\n\n- Người chơi có 4 vị trí để chứa quà, trong trường hợp đã 4 vị trí đã đủ 4 hộp quà (hoặc quà) thì sẽ không thể nhận thêm hộp quà cho dù đã đạt đủ điều kiện chuỗi thắng khác.\nMỗi hộp quà có thời hạn 24 tiếng tính từ thời điểm nhận hộp quà. Nếu người chơi không mở trong 24 tiếng sau khi nhận, hộp quà sẽ biến mất.\n- Sau khi mở hộp quà, người chơi sẽ nhận được ngẫu nhiên một trong nhiều phần quà giá trị và thời gian tương ứng để chờ nhận thưởng.\n\n<color=#ffff00>4. Lưu ý</c>\n- Giải thưởng M-Win sẽ cộng trực tiếp vào tài khoản ngay sau khi hết thời gian chờ nhận thưởng\n- Giải thưởng vật được trao trong vòng 1 tuần tính từ thời điểm nhận, giải thưởng vật lý không bao gồm các phụ phí liên quan.\n- Giải thưởng vật lý có thể quy đổi thành M-Win\n- Không hạn chế số lượng quà của 1 tài khoản\n- Trong thường hợp có tranh chấp, quyết định của ban tổ chức là quyết định cuối cùng.", "_horizontalAlign": 0, "_verticalAlign": 0, "_fontSize": 24, "_fontColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_maxWidth": 1522, "_fontFamily": "<PERSON><PERSON>", "_font": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_userDefinedFont": {"__uuid__": "8195f5b5-bd59-496e-9001-98a9da2fb73f", "__expectedType__": "cc.TTFFont"}, "_cacheMode": 0, "_imageAtlas": null, "_handleTouchEvent": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bd800nJa9MJYJxFTTy5Xmk"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 568}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82PUOGx+BNiKiJyeFxIOtS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 570}, "_contentSize": {"__type__": "cc.Size", "width": 1522, "height": 657.36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4du0CUjXFPOboxyA6etcQj"}, {"__type__": "50d0eh1/MBP9bsRMPNN7Xbr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 564}, "_enabled": true, "__prefab": {"__id__": 572}, "id": "tx_event_sh_rule_2", "isUpperCase": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bgoGyH8VJ75+S+lW9vBYz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5CM7age9DbYUj9OgKaKGT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 575}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 20, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "32+FJKqcJNR4s2HgclvDW2"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 577}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46LZKm2adIG6YzLw0vm66C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 543}, "_enabled": true, "__prefab": {"__id__": 579}, "_contentSize": {"__type__": "cc.Size", "width": 1500, "height": 1500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bf3ts141BrorgdioM76bc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0dkA02W2ZJsYq/VLnqE3SU", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 542}, "_enabled": true, "__prefab": {"__id__": 582}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0c0AOZpSVIVLEdtLMeD+nQ"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 542}, "_enabled": true, "__prefab": {"__id__": 584}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eoSzNup1E0Zd2o0oDmDgh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 542}, "_enabled": true, "__prefab": {"__id__": 586}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 600}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c64xqaln5NRKplBatQAoSV"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 542}, "_enabled": true, "__prefab": {"__id__": 588}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cAgfukTlDD5e8wf7Ygn4U"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b3fDwWohxI3KJ5e4IShKLo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 591}, "bounceDuration": 0.23, "brake": 0.75, "elastic": false, "inertia": false, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 543}, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82hCjTZIdD/KVSB4qLVxE0"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 593}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0+wZV2UlFNoafqeb+lC1E"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 541}, "_enabled": true, "__prefab": {"__id__": 595}, "_contentSize": {"__type__": "cc.Size", "width": 1532, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bbXXZX4xNSYM0oL7MpTxK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "feksoof8NEprJxVxjK8ZBe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": {"__id__": 598}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": -16.279000000000014, "_bottom": 16.279000000000014, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9cNsX+G2lK7b2ujk4qcRDN"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": {"__id__": 600}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "851a2X4EVAJJcjmnqTGVSL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 540}, "_enabled": true, "__prefab": {"__id__": 602}, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3tOD4PbZAEZf5TMiaVSCI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38SpaLw+ZFj79EomzFxfy9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 605}, "_customMaterial": {"__uuid__": "fda095cb-831d-4601-ad94-846013963de8", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8041cda3-8425-4139-808a-1abde955f27c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5MyvfiVhGLoZEJypC1CQK"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 607}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0kXFU+l9DFo16N7w5NYPb"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 609}, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 955}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57/DhFTRBEt6LZBNKjZWtd"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8097uA33FGDKGgczOU6TDz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 612}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03xBmSAHhE/rO6D3ynupw9"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 614}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3sAGa/2pBjYpptV/J0Krc"}, {"__type__": "66036QKeiJH271Imzo1nyOv", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 616}, "bg": {"__id__": 2}, "container": {"__id__": 12}, "tabContentsEventSH": [{"__id__": 127}, {"__id__": 315}, {"__id__": 540}], "tabTogglesEventSH": [{"__id__": 54}, {"__id__": 81}, {"__id__": 108}], "listAccount": {"__id__": 208}, "itemAccount": {"__id__": 209}, "listTop": {"__id__": 431}, "itemTop": {"__id__": 432}, "sprFrameBox1": {"__uuid__": "162bc419-8184-4003-b4eb-08d4802958d3@f9941", "__expectedType__": "cc.SpriteFrame"}, "sprFrameBox2": {"__uuid__": "b5e5956a-f8ef-41d6-acdd-181a80813f5d@f9941", "__expectedType__": "cc.SpriteFrame"}, "sprFrameBox3": {"__uuid__": "1016579f-cdc1-4ccb-8b87-41c6995dfc9d@f9941", "__expectedType__": "cc.SpriteFrame"}, "sprFrameBox4": {"__uuid__": "ee57112d-facf-4204-9ea2-171fdcda76d1@f9941", "__expectedType__": "cc.SpriteFrame"}, "sprFrameBox5": {"__uuid__": "ff274d53-41e4-4a55-868f-1b2e78ba2f11@f9941", "__expectedType__": "cc.SpriteFrame"}, "BG1": {"__uuid__": "7425a770-af0e-4a6e-aea3-1d96b37dea37@f9941", "__expectedType__": "cc.SpriteFrame"}, "BG2": {"__uuid__": "635c926b-56f8-442c-a142-1a048aa02a68@f9941", "__expectedType__": "cc.SpriteFrame"}, "selectedBoxes": {"__id__": 316}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fTYxAl5dNjbmHAETq0fvf"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 618}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38BoP1Y9dPGqq6+eJ8+aXL"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 620}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "938svOu1xAp6Hdj4GDRYhs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f7reQqcNFFpIT5aVyD/EAx", "instance": null, "targetOverrides": null}]