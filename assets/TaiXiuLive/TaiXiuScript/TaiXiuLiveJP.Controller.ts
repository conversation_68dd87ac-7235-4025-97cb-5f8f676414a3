import { _decorator, Component, Node, Sprite, Label, Prefab, instantiate, Event, Button, EditBox, ToggleContainer, SpriteFrame, Vec2, v2, v3, tween, WebView, Animation, EventTouch, Tween, UIOpacity } from 'cc';
import SignalRClient from '../../Lobby/scripts/common/networks/Network.SignalRClient';
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import ChatHubSignalRClient from "db://assets/Lobby/scripts/common/networks/ChatHubSignalRClient";
import MiniGameTXSignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXSignalRClient";
import ButtonMiniGame from "db://assets/Lobby/scripts/common/ButtonMiniGame";
import Config from "db://assets/Lobby/scripts/common/Config";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("TaiXiuLiveJP/Controller")
export default class TaiXiuLiveJPController extends Component {

    static instance: TaiXiuLiveJPController = null;

    @property(Sprite)
    avatar: Sprite = null;
    @property(Label)
    labelBalance: Label = null;
    @property(Label)
    labelNickname: Label = null;

    // 1: Gold 2: Xu
    private isBetTypeGold = true;

    // Box
    @property(Node)
    chatBox: Node = null;
    @property(Node)
    settingBox: Node = null;
    @property(Node)
    statisticalBox: Node = null;

    // Popup Container
    @property(Node)
    popupContainer: Node = null;
    @property(Prefab)
    popupEventTienTriPrefab: Prefab = null;
    @property(Prefab)
    popupEventSHPrefab: Prefab = null;
    @property(Prefab)
    popupEventJackpotPrefab: Prefab = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab = null;
    @property(Prefab)
    popupEventHonorPrefab: Prefab = null;
    @property(Prefab)
    popupDetailSessionPrefab: Prefab = null;

    private popupEventTienTri: any = null;
    private popupEventSH: any = null;
    private popupEventJackpot: any = null;
    private popupHonor: any = null;
    private popupHistory: any = null;
    private popupGuide: any = null;
    private popupDetailSession = null;

    private currentGameIds = [1, 2, 3]; // center, left, right
    private nameTX = ["HK", "LVG", "MC"];

    @property(Node)
    leftContainer: Node = null;
    @property(Node)
    rightContainer: Node = null;
    @property(Node)
    centerLabels: Node = null;

    // GAME FIELDS
    @property(SpriteFrame)
    sprFrameTai: SpriteFrame = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame = null;
    @property(Label)
    lblSession: Label = null;
    @property(Node)
    containerTimer: Node = null;
    @property(Label)
    lblRemainTime: Label = null;
    @property(Label)
    lblRemainWaiting: Label = null;
    @property(Label)
    lblSumDices: Label = null;
    @property(Label)
    lblTotalBetTaiCurrent: Label = null;
    @property(Label)
    lblTotalBetXiuCurrent: Label = null;
    @property(Label)
    lblBetTai: Label = null;
    @property(Label)
    lblBetXiu: Label = null;
    @property(Label)
    lblUserTai: Label = null;
    @property(Label)
    lblUserXiu: Label = null;

    @property(Node)
    dicesContainer: Node = null;
    @property([SpriteFrame])
    listSprDice: SpriteFrame[] = [];
    @property(Node)
    gateTaiWin: Node = null;
    @property(Node)
    nodeTaiWin: Node = null;
    @property(Node)
    gateXiuWin: Node = null;
    @property(Node)
    nodeXiuWin: Node = null;
    @property(Node)
    bowl: Node = null;
    @property(Button)
    buttonNan: Button = null;
    @property(Label)
    lblToast: Label = null;
    @property(Node)
    historyList: Node = null;
    @property(Node)
    historyItem: Node = null;
    detailSessions = [];

    // JACKPOT
    @property(Node)
    nodeWheelSpin: Node = null;
    @property(Node)
    nodeJackPot: Node = null;
    @property(Node)
    jackpotLocationTai: Node = null;
    @property(Node)
    jackpotLocationXiu: Node = null;
    @property(Label)
    lblJackPot: Label = null;
    @property(Node)
    wheelNode: Node = null;

    // LIVE
    @property(Node)
    liveNode: Node = null;
    @property(WebView)
    webView: WebView = null;
    @property(Node)
    liveNodeMini: Node = null;
    @property(WebView)
    webViewMini: WebView = null;
    private streamURL: string = "";

    private sessionIds: number[] = [];
    private isCoinGold: boolean = true;
    private betLogs: any[] = [];
    private isBetting = false;
    private isNans: boolean[] = [];
    private lastLocationIDWin = 0;
    private lastScore = 0;
    private readonly bowlStartPos = v2(0, 25);

    @property(EditBox)
    editBoxBetTai: EditBox = null;
    @property(EditBox)
    editBoxBetXiu: EditBox = null;
    @property(Node)
    confirmBetButton: Node = null;
    @property(Node)
    cancelBetButton: Node = null;
    @property(Button)
    x2BetButton: Button = null;
    @property(Node)
    hideButton: Node = null;
    @property(Node)
    chipContainer: Node = null;

    private gameID: number;
    private hub: SignalRClient = null;
    private betTypeLocation: number = 0;
    private betTypeSelected: number = 0;
    private selectedChipValue: number = 0;

    @property([Node])
    boxes: Node[] = [];
    @property([SpriteFrame])
    sprFrameBoxes: SpriteFrame[] = [];
    @property([SpriteFrame])
    sprFramePrizes: SpriteFrame[] = [];
    @property(SpriteFrame)
    sprFrameBoxDefault: SpriteFrame | null = null;

    @property(Label)
    labelJackpotBalance: Label = null;
    @property(Node)
    winTextTaiNode: Node = null;
    @property(Node)
    winTextXiuNode: Node = null;
    @property(Node)
    jackpotPrizeNode: Node = null;

    @property(Node)
    layoutBetChip: Node = null;
    @property(Node)
    layoutBetNumber: Node = null;

    private countdownSHIntervals = [];
    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    private isOpenBowl = false;
    private isShowWheel = false;
    private jackpotInfo: any = null;
    private readonly STREAM_TEMPLATE = "https://demo.nanocosmos.de/nanoplayer/embed/1.3.3/nanoplayer.html?group.id=STREAM_ID&group.security.jwtoken=JWT_TOKEN";
    private currentStreamID = -1;
    private isShowingResult = false;
    private isProcessingSession = false;

    public onEnable() {
        TaiXiuLiveJPController.instance = this;
    }

    onLoad() {
        const formatBetValue = (editBox: EditBox) => {
            let value = parseInt(editBox.string.replace(/\./g, ""));
            if (isNaN(value) || value <= 0) {
                editBox.string = "";
            } else {
                editBox.string = Utils.formatNumber(value);
            }
        };

        this.editBoxBetTai.node.on("editing-did-began", () => {
            if (this.editBoxBetTai.string == "") return;
            this.editBoxBetTai.string = Utils.formatEditBox(this.editBoxBetTai.string).toString();
        });

        this.editBoxBetXiu.node.on("editing-did-began", () => {
            if (this.editBoxBetXiu.string == "") return;
            this.editBoxBetXiu.string = Utils.formatEditBox(this.editBoxBetXiu.string).toString();
        });

        this.editBoxBetTai.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        this.editBoxBetTai.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            setTimeout(() => {
                if (this.labelBalance) {
                    this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                }
            }, 1000);
        }, this);
    }

    public start() {
        this.settingBox.active = false;
        this.statisticalBox.active = false;
        this.chatBox.active = false;
        this.isNans = [false, false, false];
        this.betLogs = [];

        this.labelNickname.string = Configs.Login.Nickname;
        this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
        this.avatar.spriteFrame = App.instance.getAvatarSpriteFrame(Configs.Login.Avatar);
        this.gameID = this.getCurrentGameID();
        this.changeServer();
        this.bowl.on(
            Node.EventType.TOUCH_MOVE,
            (event: EventTouch) => {
                var pos = this.bowl.getPosition();
                pos.x += event.getDeltaX();
                pos.y += event.getDeltaY();
                this.bowl.position = pos;

                let distance = Utils.v2Distance(
                    new Vec2(pos.x, pos.y),
                    this.bowlStartPos
                );
                if (Math.abs(distance) > 200) {
                    this.showResult();
                    this.isOpenBowl = true;
                    this.scheduleOnce(() => {
                        this.bowl.active = false;
                    }, 2);
                }
            },
            this
        );
        this.bowl.active = false;
    }

    getStreamURL() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetLiveStreamInfo'], {gameId: Configs.InGameIds.TaiXiuMini}, (status, res) => {
            if (res.c < 0 || status !== 200) {
                return;
            }

            var streamObj = res.d.find((item: any) => item.gameID === Configs.InGameIds.TaiXiuMini && item.roomID === this.getCurrentGameID());
            if (streamObj && streamObj.streamID && streamObj.tokenPlayBack) {
                if (this.currentStreamID !== streamObj.streamID) {
                    this.currentStreamID = streamObj.streamID;
                    this.streamURL = this.STREAM_TEMPLATE.replace("STREAM_ID", streamObj.streamID).replace("JWT_TOKEN", streamObj.tokenPlayBack);
                }
            } else {
                this.streamURL = "";
            }
        });
    }

    protected update(_dt: number) {
        if (this.streamURL === "") {
            this.liveNode.active = false;
            this.liveNodeMini.active = false;
            return;
        }

        if (this.webView.url !== this.streamURL) {
            this.webView.url = this.streamURL;
            this.webViewMini.url = this.streamURL;
        }

        let isShowWebView = true;
        let isShowMiniWebView = false;

        for (let child of this.popupContainer.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (isShowWebView) {
            for (let child of App.instance.miniGameNode.children) {
                if (child.active) {
                    isShowWebView = false;
                    isShowMiniWebView = true;
                    break;
                }
            }
        }

        for (let child of App.instance.tipzoJackpotEventX2X6Node.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (ButtonMiniGame.instance.panel.active || App.instance.alertDialog.node.active) {
            isShowWebView = false;
        }

        this.liveNode.active = isShowWebView;
        if (isShowMiniWebView) {
            if (App.instance.tipzoMiniLiveNode.children.length == 0) {
                var mini = instantiate(this.liveNodeMini);
                mini.active = true;
                App.instance.tipzoMiniLiveNode.addChild(mini);
            }
        } else {
            App.instance.tipzoMiniLiveNode.removeAllChildren();
        }
    }

    onDestroy() {
        MiniGameTXSignalRClient.getInstance().dontReceive();
        ChatHubSignalRClient.getInstance().dontReceive();
        App.instance.tipzoMiniLiveNode.removeAllChildren();
        Utils.setStorageValue("last_open_game_id", "");
        Utils.setStorageValue("opened_tx_jp_live", "false");
        this.countdownSHIntervals.forEach((interval => {
            clearInterval(interval);
        }));
    }

    leaveRoom() {
        TaiXiuLiveJPController.instance = null;
        this.node.destroy();
    }

    getCurrentGameID(): number {
        return this.currentGameIds[0];
    }

    changeServer() {
        if (this.hub) {
            this.hub.send("HideLD", [{GameID: this.gameID}], () => {});
        }

        this.unscheduleAllCallbacks();
        this.historyList.removeAllChildren();
        this.getStreamURL();
        this.gameID = this.getCurrentGameID();
        this.hub = MiniGameTXSignalRClient.getInstance();

        this.hub.send("GetCurrentRoomsLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        this.initHubs();
        this.resetForNew();
        this.countdownSHIntervals.forEach((interval) => {
            clearInterval(interval);
        });
        this.boxes.forEach((box) => {
            box.getComponent(Sprite).spriteFrame = this.sprFrameBoxDefault;
            box.getChildByName('time').active = false;
            box.getChildByName('effect').active = false;
        });

        var isNan = this.isNans[this.gameID - 1] ?? false;
        this.isNans[this.gameID - 1] = isNan;
        var on = this.hideButton.getChildByName('on');
        var off = this.hideButton.getChildByName('off');
        on.active = isNan;
        off.active = !isNan;
    }

    onClickLeft() {
        this.currentGameIds = [this.currentGameIds[1], this.currentGameIds[0], this.currentGameIds[2]];
        var leftGameID = this.currentGameIds[1];
        var centerGameID = this.currentGameIds[0];

        this.nameTX.forEach((name, index) => {
            this.leftContainer.getChildByName(name).active = index === leftGameID - 1;
            this.centerLabels.getChildByName(name).active = index === centerGameID - 1;
        })

        this.changeServer();
    }

    onClickRight() {
        this.currentGameIds = [this.currentGameIds[2], this.currentGameIds[1], this.currentGameIds[0]];
        var rightGameID = this.currentGameIds[2];
        var centerGameID = this.currentGameIds[0];

        this.nameTX.forEach((name, index) => {
            this.rightContainer.getChildByName(name).active = index === rightGameID - 1;
            this.centerLabels.getChildByName(name).active = index === centerGameID - 1;
        })

        this.changeServer();
    }

    getLuckyDiceJackPot() {
        this.labelJackpotBalance.string = "0";
        Http.get(Configs.App.DOMAIN_CONFIG['LuckyDiceJackPot'], {gameID: this.gameID}, (status, res) => {
            if (res.c < 0) {
                return;
            }

            this.labelJackpotBalance.string = Utils.formatNumber(res.d);
        });
    }

    initHubs() {
        this.getLuckyDiceJackPot();

        this.hub.receive("currentSessionLD", (res) => {
            if (res.GameID !== this.gameID) return;

            if (this.isProcessingSession) return;
            this.isProcessingSession = true;

            this.lblSession.string = `#${res.GameSessionID}`;
            this.sessionIds[this.gameID] = res.GameSessionID;
            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            if (res.GameStatus === 1) {
                this.isShowingResult = false;
                this.getStreamURL();
                this.handleBettingPhase(res.RemainBetting);
            } else {
                this.handleWaitingPhase(res.RemainWaiting);
            }

            this.loadEventChestCheck();

            setTimeout(() => {
                this.isProcessingSession = false;
            }, 500);
        });

        this.hub.receive("currentResultLD", (res) => {
            if (res.GameID != this.gameID || this.isShowingResult) {
                return;
            }
            this.isShowingResult = true;
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            this.editBoxBetTai.enabled = false;
            this.editBoxBetXiu.enabled = false;
            this.buttonNan.enabled = false;
            const aminResult = this.dicesContainer.getChildByName('anim');
            const resultNode = this.dicesContainer.getChildByName('result');
            resultNode.active = false;
            aminResult.active = true;
            const dice_1 = resultNode.getChildByName('dice_1');
            const dice_2 = resultNode.getChildByName('dice_2');
            const dice_3 = resultNode.getChildByName('dice_3');
            dice_1.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice1 - 1];
            dice_2.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice2 - 1];
            dice_3.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice3 - 1];
            this.isShowWheel = (res.Dice1 == 1 && res.Dice2 == 1 && res.Dice3 == 1) || (res.Dice1 == 6 && res.Dice2 == 6 && res.Dice3 == 6);
            this.jackpotInfo = res.JackPotInfo || null;
            this.dicesContainer.active = true;
            // const anim = aminResult.getComponent(Animation);
            // anim.play();
            const anim1 = aminResult.getChildByName('animDice1').getComponent(Animation);
            const anim2 = aminResult.getChildByName('animDice2').getComponent(Animation);
            const anim3 = aminResult.getChildByName('animDice3').getComponent(Animation);

            anim1.play();
            anim2.play();
            anim3.play();

            this.scheduleOnce(() => {
                anim1.stop();
                anim2.stop();
                anim3.stop();
                aminResult.active = false;

                this.lastLocationIDWin = res.LocationIDWin;
                this.lastScore = res.Dice1 + res.Dice2 + res.Dice3;
                if (this.isNans[this.gameID - 1]) {
                    this.bowl.setPosition(v3(this.bowlStartPos.x, this.bowlStartPos.y, 0));
                    this.bowl.active = true;

                    this.scheduleOnce(() => {
                        if (this.isOpenBowl) {
                            return;
                        }
                        this.isOpenBowl = true;
                        tween(this.bowl)
                            .to(0.5, {position: v3(250, 150, 0)})
                            .call(() => {
                                this.showResult();
                                this.scheduleOnce(() => {
                                    this.bowl.active = false;
                                }, 2);
                            })
                            .start();
                    }, 12);
                } else {
                    this.showResult();
                }
                resultNode.active = true;
            }, 3);
        });

        this.hub.receive("currentRoomsInfoLD", (res) => {
            for (const room of res) {
                if (room == null) continue;
                if (room.GameID !== this.gameID) continue;

                const isGoldRoom = room.BetType === 1;
                const isXuRoom = room.BetType === 2;

                if ((this.isCoinGold && isXuRoom) || (!this.isCoinGold && isGoldRoom)) {
                    continue;
                }

                this.lblUserXiu.string = `(${Utils.formatNumber(room.TotalAccount1)})`;
                this.lblUserTai.string = `(${Utils.formatNumber(room.TotalAccount2)})`;

                this.lblTotalBetXiuCurrent.string = Utils.formatNumber(room.TotalBetValue1);
                this.lblTotalBetTaiCurrent.string = Utils.formatNumber(room.TotalBetValue2);
            }
        });

        this.hub.receive("gameHistoryLD", (res) => {
            if (res == null || res.length == 0) {
                return;
            }
            this.detailSessions = [];
            if (res[0].GameID == this.gameID) {
                this.historyList.removeAllChildren();
            } else {
                return;
            }
            for (let i = res.length - 1; i >= 0; i--) {
                this.detailSessions.push(res[i]);

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;
                item.getChildByName("last").active = i === 0;
                item.on(Node.EventType.TOUCH_END, () => {
                    this.actPopupDetailSession(res[i].GameSessionID);
                });

                if (i === 0) {
                    Tween.stopAllByTarget(item);
                    this.scheduleOnce(() => {
                        const posUp = v3(item.position.x, 5, item.position.z);
                        const posDown = v3(item.position.x, -5, item.position.z);

                        tween(item)
                            .repeatForever(
                                tween()
                                    .to(0.3, { position: posUp })
                                    .to(0.3, { position: posDown })
                            )
                            .start();
                    }, 0);
                }

                this.historyList.addChild(item);
            }
        });

        this.hub.receive("betOfAccountLD", (res) => {
            res.forEach((item: any) => {
                if (item.GameID != this.gameID) {
                    return;
                }

                this.editBoxBetTai.string = '';
                this.editBoxBetXiu.string = '';

                if (item.BetType == 1) {
                    this.betTypeSelected = item.LocationID;
                    const betLocation = item.LocationID == 1 ? this.lblBetXiu : this.lblBetTai;
                    betLocation.string = Utils.formatNumber(item.BetValue);
                }
            })
        });

        this.hub.receive("resultOfAccountLD", (res) => {
            if (this.betTypeSelected === 0) return;
            const winTextNode = this.betTypeSelected === 1 ? this.winTextXiuNode : this.winTextTaiNode;

            let totalPrize = 0;
            let totalRefund = 0;

            res.forEach((item: any) => {
                if (item.GameID === this.gameID) {
                    totalPrize += item.PrizeValue;
                    totalRefund += item.RefundValue;
                }
            });

            if (totalPrize <= 0 && totalRefund <= 0) {
                return;
            }

            if (totalPrize > 0) {
                this.handleWinOrRefundAmount(winTextNode, totalPrize);
            }

            if (totalRefund > 0) {
                this.scheduleOnce(() => {
                    this.handleWinOrRefundAmount(winTextNode, totalRefund);
                }, totalPrize > 0 ? 1 : 0)
            }
            this.updateBalanceTaiXiu();
        });

        this.hub.receive("jackpotOfAccountLD", (jackpotValue) => {
            this.handleWinOrRefundAmount(this.jackpotPrizeNode, jackpotValue, -200, -50);
            this.updateBalanceTaiXiu();
        });
    }

    private handleWinOrRefundAmount(winTextNode: Node, amount: number, from: number = -60, to: number = 60) {
        var textNode = instantiate(winTextNode);
        textNode.parent = winTextNode.parent;
        textNode.active = true;
        textNode.getComponentInChildren(Label).string = '+ ' + Utils.formatNumber(amount);
        textNode.position = v3(textNode.x, from);
        tween(textNode)
            .to(3, {position: v3(textNode.x, to)})
            .call(() => {
                textNode.destroy();
            })
            .start();
    }

    private resetForNew() {
        this.getLuckyDiceJackPot();
        this.updateBalanceTaiXiu();
        this.dicesContainer.active = false;
        this.isOpenBowl = false;
        this.lblBetTai.string = "0";
        this.lblBetXiu.string = "0";
        this.betTypeLocation = 0;
        this.betTypeSelected = 0;
        this.lastScore = 0;
        this.lblSumDices.string = "";
        this.lblRemainTime.string = "";
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
        this.bowl.active = false;
        this.clearChipToggle();
        this.hideResult();
        this.actCancelBet();
        this.isShowingResult = false;
        this.x2BetButton.enabled = this.betLogs.find((item) => item.gameID === this.gameID && item.sessionID === this.sessionIds[this.gameID] - 1) != undefined;
        this.winTextXiuNode.active = false;
        this.winTextTaiNode.active = false;
        this.jackpotPrizeNode.active = false;
    }

    private handleBettingPhase(remainTime: number) {
        this.containerTimer.active = true;

        if (remainTime === 60) {
            this.showToast(App.instance.getTextLang("txt_taixiu_new_session"));
            this.resetForNew();
        }

        if (remainTime < 3) {
            this.isBetting = false;
        }

        this.isBetting = true;
        this.editBoxBetTai.enabled = true;
        this.editBoxBetXiu.enabled = true;
        this.buttonNan.enabled = true;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;

        let secondsLeft = remainTime;
        this.unschedule(this.countdownRemainTime);
        this.schedule(this.countdownRemainTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownRemainTime);
                    return;
                }

                if (secondsLeft <= 5) {
                    this.isBetting = false;
                }

                this.lblRemainTime.string = secondsLeft < 10 ? `0${secondsLeft}` : `${secondsLeft}`;

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownRemainTime);
            }
        }, 1);
    }

    private handleWaitingPhase(waitingTime: number) {
        if (waitingTime > 0) this.isBetting = false;

        if (waitingTime < 19) {
            this.hub.send("GetCurrentResultLD", [{ GameID: this.gameID }], () => {});
        }

        this.x2BetButton.enabled = false;
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
        this.gateXiuWin.active = false;
        this.gateTaiWin.active = false;
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
        this.containerTimer.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;
        let secondsLeft = waitingTime;
        const randomTrigger = Math.floor(Math.random() * 2) + 10;

        if (waitingTime > randomTrigger) {
            setTimeout(() => {
                this.hub?.send("GetAccountResultLD", [{GameID: this.gameID, CurrencyID: Configs.Login.CurrencyID, GameSessionID: this.sessionIds[this.gameID]}], () => {});
            }, (waitingTime - randomTrigger) * 1000);
        }

        this.unschedule(this.countdownWaitingTime);
        this.schedule(this.countdownWaitingTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownWaitingTime);
                    return;
                }

                if (this.isNans[this.gameID - 1] && this.isOpenBowl === false && secondsLeft > 15) {
                    const secondsLeftOpenBowl = secondsLeft - 15;
                    this.lblRemainWaiting.string = `${secondsLeftOpenBowl < 10 ? "0" + secondsLeftOpenBowl : secondsLeftOpenBowl}`;
                    this.lblRemainWaiting.node.parent.active = true;
                    this.lblSumDices.node.parent.active = false;
                } else {
                    this.lblSumDices.node.parent.active = secondsLeft > 12;
                    this.lblRemainWaiting.node.parent.active = secondsLeft <= 12;
                    this.lblRemainWaiting.string = `${secondsLeft < 10 ? "0" + secondsLeft : secondsLeft}`;
                }

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownWaitingTime);
            }
        }, 1);
    }

    actBet(betValue: number) {
        this.hub.send("SetBetLD", [{
            GameID: this.gameID,
            CurrencyID: Configs.Login.CurrencyID,
            BetType: this.isCoinGold ? 1 : 2,
            Location: this.betTypeLocation,
            Amount: betValue
        }], (res) => {
            if (res < 0) {
                this.showToast(App.instance.getTextLang(`me${res}`));
                this.scheduleOnce(() => {
                    this.confirmBetButton.active = true;
                    this.cancelBetButton.active = true;
                }, 1);
                this.actCancelBet();
                return;
            }

            this.betTypeSelected = this.betTypeLocation;
            this.showToast(App.instance.getTextLang("tx3_live").replace('{0}', this.betTypeLocation === 1 ? App.instance.getTextLang("tx44") : App.instance.getTextLang("tx43")));

            var log = this.betLogs.find((item) => item.gameID === this.gameID && item.sessionID === this.sessionIds[this.gameID]);
            if (log) {
                log.betValue += betValue;
            } else {
                this.betLogs.push({
                    gameID: this.gameID,
                    location: this.betTypeLocation,
                    betValue: betValue,
                    sessionID: this.sessionIds[this.gameID]
                })
            }

            this.editBoxBetTai.string = "";
            this.editBoxBetXiu.string = "";
            this.gateXiuWin.active = false;
            this.gateTaiWin.active = false;
            this.betTypeLocation = 0;
            this.clearChipToggle();
            this.updateBalanceTaiXiu();
            this.x2BetButton.enabled = false;
            this.betLogs = this.betLogs.filter(log => {
                return !(log.gameID === this.gameID && log.sessionID === this.sessionIds[this.gameID] - 1);
            });
        });
    }

    x2Bet() {
        var log = this.betLogs.find((item) => item.gameID === this.gameID && item.sessionID === this.sessionIds[this.gameID] - 1);
        if (!this.isBetting || !log) return;

        var edb = log.location == 1 ? this.editBoxBetXiu : this.editBoxBetTai;
        edb.string = Utils.formatNumber((log.betValue * 2));
        this.betTypeLocation = log.location;
        this.gateXiuWin.active = log.location == 1;
        this.gateTaiWin.active = log.location == 2;
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
    }

    updateBalanceTaiXiu() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
            if (status === 200) {
                Configs.Login.GoldBalance = json['d'][0]['goldBalance'];
                Configs.Login.CoinBalance = json['d'][0]['coinBalance'];

                this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
            }
        });
    }

    updateCurrentBetType(_event: Event, type: string) {
        if (!this.isBetting || this.betTypeLocation == parseInt(type)) return;
        this.betTypeLocation = parseInt(type);
        this.gateXiuWin.active = type == "1";
        this.gateTaiWin.active = type == "2";
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    selectedChip(event: any, value: string) {
        if (!this.isBetting) return;

        this.clearChipToggle();
        if (this.betTypeLocation == 0) {
            return;
        }

        var target = event.target;
        target.getChildByName('checkmark').active = true;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = currentAmount + parseInt(value);
        this.setEditBoxValue(editBox, newAmount);
    }

    clearChipToggle() {
        this.chipContainer.children.forEach((item) => {
            item.getChildByName('checkmark').active = false;
        })
    }

    actConfirmBet() {
        if (!this.isBetting) {
            this.showToast(App.instance.getTextLang("me-207"));
            this.actCancelBet();
            return;
        }
        if (this.betTypeLocation == 0) {
            return;
        }

        var editBox = this.betTypeLocation == 1 ? this.editBoxBetXiu : this.editBoxBetTai;
        var betValue = Utils.formatEditBox(editBox.string);
        if (betValue <= 0 || isNaN(betValue)) {
            this.showToast(App.instance.getTextLang("me-60212"));
            return;
        }

        this.actBet(betValue);
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
    }

    actCancelBet() {
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    showResult() {
        var nodeResult: Node;
        this.hideResult();

        if (this.lastLocationIDWin === 1) {
            nodeResult = this.nodeXiuWin;
            this.gateXiuWin.active = true;
            this.gateTaiWin.active = false;
            this.nodeTaiWin.active = false;
        } else if (this.lastLocationIDWin === 2) {
            nodeResult = this.nodeTaiWin;
            this.gateTaiWin.active = true;
            this.gateXiuWin.active = false;
            this.nodeXiuWin.active = false;
        } else {
            return;
        }
        nodeResult.active = true;

        var nodeResultOpacity = nodeResult.getComponent(UIOpacity);
        tween(nodeResultOpacity)
            .repeatForever(
                tween()
                    .to(0.2, { opacity: 100 })
                    .to(0.2, { opacity: 255 })
            )
            .start();

        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = true;
        this.lblSumDices.string = this.lastScore.toString();

        if (!this.isShowWheel) {
            return;
        }

        this.scheduleOnce(() => {
            this.nodeWheelSpin.active = true;
            this.handleJackpotFund(this.jackpotInfo ? this.jackpotInfo.JackpotLocationID : 0);

            this.scheduleOnce(() => {
                this.updateBalanceTaiXiu();
                this.nodeJackPot.active = false;
                this.nodeWheelSpin.active = false;
            }, 12);
        }, 3);
    }

    hideResult() {
        Tween.stopAllByTarget(this.nodeTaiWin);
        Tween.stopAllByTarget(this.nodeXiuWin);
        Tween.stopAllByTarget(this.wheelNode);
        this.nodeTaiWin.active = false;
        this.nodeXiuWin.active = false;
        this.gateTaiWin.active = false;
        this.gateXiuWin.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;
        this.nodeJackPot.active = false;
        this.nodeWheelSpin.active = false;
    }

    handleJackpotFund(locationID: number) {
        let baseAngle = 0;

        if (locationID === 1) {
            baseAngle = 120;
        } else if (locationID === 2) {
            baseAngle = 240;
        }

        Tween.stopAllByTarget(this.wheelNode);
        this.wheelNode.angle = this.wheelNode.angle % 360;
        let totalRounds = 8;
        let targetAngle = 360 * totalRounds + baseAngle;

        tween(this.wheelNode)
            .to(4, {angle: -targetAngle}, {easing: "quartOut"})
            .call(() => {
                if (this.betTypeSelected === 0 || this.betTypeSelected !== locationID) {
                    return;
                }

                this.nodeJackPot.active = true;
                this.lblJackPot.getComponent(Label).string = Utils.formatNumber(this.jackpotInfo.JackpotFund);
                this.nodeJackPot.active = true;
                this.jackpotLocationTai.active = locationID == 2;
                this.jackpotLocationXiu.active = locationID == 1;

                Tween.stopAllByTarget(this.jackpotLocationTai);
                Tween.stopAllByTarget(this.jackpotLocationXiu);
                tween(locationID == 2 ? this.jackpotLocationTai : this.jackpotLocationXiu)
                    .to(0.5, { scale: v3(1.25, 1.25, 1.25) }, { easing: "quadOut" })
                    .to(0.2, { scale: v3(1.0, 1.0, 1.0) }, { easing: "quadIn" })
                    .start();

                Utils.numberTo(this.lblJackPot, this.jackpotInfo.JackpotFund, 2);
                this.updateBalanceTaiXiu();
            })
            .start();
    }

    toggleLayoutBet(event: any) {
        var target = event.target;
        var textOther = target.getChildByName('text');
        var betFast = target.getChildByName('betFast');
        this.layoutBetChip.active = !this.layoutBetChip.active;
        this.layoutBetNumber.active = !this.layoutBetNumber.active;
        betFast.active = !this.layoutBetChip.active;
        textOther.active = !this.layoutBetNumber.active;
        this.actCancelBet();
    }

    private getCurrentEditBox(): EditBox {
        return this.betTypeLocation === 1 ? this.editBoxBetXiu : this.editBoxBetTai;
    }

    private setEditBoxValue(editBox: EditBox, value: number) {
        editBox.string = (isNaN(value) || value === 0) ? "" : Utils.formatNumber(value);
    }

    updateBetAmountCustom(_event: Event, amount: string) {
        if (this.betTypeLocation == 0) return;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = parseInt(currentAmount.toString() + amount);
        this.setEditBoxValue(editBox, newAmount);
    }

    deleteBetAmount() {
        if (this.betTypeLocation == 0) return;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string).toString();
        const newAmount = currentAmount.length > 1 ? parseInt(currentAmount.slice(0, -1)) : 0;
        this.setEditBoxValue(editBox, newAmount);
    }

    toggleNan(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');
        on.active = !on.active;
        off.active = !off.active;

        this.isNans[this.gameID - 1] = !this.isNans[this.gameID - 1];
    }

    toggleChatBox() {
        this.chatBox.active = !this.chatBox.active;
    }

    toggleSettingBox() {
        this.settingBox.active = !this.settingBox.active;
    }

    toggleStatisticalBox() {
        this.statisticalBox.active = !this.statisticalBox.active;
    }

    actPopupEventTienTri() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventTienTri == null) {
            this.popupEventTienTri = instantiate(this.popupEventTienTriPrefab)
                .getComponent("TaiXiuLive.EventTienTri");
            this.popupEventTienTri.node.parent = this.popupContainer;
            this.popupEventTienTri.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventTienTri.showDetail(gameID);
        }
    }

    actPopupDetailSession(session: number) {
        if (this.popupDetailSession == null) {
            this.popupDetailSession = instantiate(this.popupDetailSessionPrefab).getComponent("TaiXiuLive.PopupDetailSession");
            this.popupDetailSession.node.parent = this.popupContainer;
            this.popupDetailSession.showDetail(session, this.getCurrentGameID(), this.detailSessions);
            App.instance.showLoading(false);
        } else {
            this.popupDetailSession.showDetail(session, this.getCurrentGameID(), this.detailSessions);
        }
    }

    actPopupEventSH() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventSH == null) {
            this.popupEventSH = instantiate(this.popupEventSHPrefab)
                .getComponent("TaiXiuLive.EventSH");
            this.popupEventSH.node.parent = this.popupContainer;
            this.popupEventSH.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventSH.showDetail(gameID);
        }
    }

    actPopupEventJackpot() {
        const gameID: number = this.getCurrentGameID();

        if (this.popupEventJackpot == null) {
            this.popupEventJackpot = instantiate(this.popupEventJackpotPrefab)
                .getComponent("TaiXiuLive.PopupHistoryJackpot");
            this.popupEventJackpot.node.parent = this.popupContainer;
            this.popupEventJackpot.showDetail(gameID);
            App.instance.showLoading(false);
        } else {
            this.popupEventJackpot.showDetail(gameID);
        }
    }

    actPopupHonor() {
        if (this.popupHonor == null) {
            this.popupHonor = instantiate(this.popupEventHonorPrefab)
                .getComponent("TaiXiuLive.PopupHonors");
            this.popupHonor.node.parent = this.popupContainer;
            this.popupHonor.showHonor(this.getCurrentGameID());
            App.instance.showLoading(false);
        } else {
            this.popupHonor.showHonor(this.getCurrentGameID());
        }
    }

    actPopupHistory() {
        if (this.popupHistory == null) {
            this.popupHistory = instantiate(this.popupHistoryPrefab)
                .getComponent("TaiXiuLive.PopupHistory");
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.showHistory(this.getCurrentGameID());
        } else {
            this.popupHistory.showHistory(this.getCurrentGameID());
        }
    }

    actPopupGuide() {
        if (this.popupGuide == null) {
            this.popupGuide = instantiate(this.popupGuidePrefab).getComponent("Dialog");
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
            App.instance.showLoading(false);
        } else {
            this.popupGuide.show();
        }
    }

    public showToast(message: string) {
        this.lblToast.string = message;

        const parent = this.lblToast.node.parent!;
        const uiOpacity = parent.getComponent(UIOpacity) || parent.addComponent(UIOpacity);

        Tween.stopAllByTarget(uiOpacity);
        parent.active = true;
        uiOpacity.opacity = 0;

        tween(uiOpacity)
            .to(0.1, { opacity: 255 })
            .delay(2)
            .to(0.2, { opacity: 0 })
            .call(() => {
                parent.active = false;
            })
            .start();
    }

    private loadEventChestCheck() {
        if (Config.Login.IsLogin == false) return;
        Http.get(Configs.App.DOMAIN_CONFIG['GetEventChestAccountInfo'], {GameID: this.getCurrentGameID(), currencyID: Configs.Login.CurrencyID}, (_status, res) => {
            if (res.c < 0) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            if (!res.d) {
                return;
            }

            var shouldShowEffects = [];
            this.boxes.forEach((box, index) => {
                shouldShowEffects[index] = box.getComponent(Sprite).spriteFrame == this.sprFrameBoxDefault || box.getChildByName('time').active;
                box.getComponent(Sprite).spriteFrame = this.sprFrameBoxDefault;
                box.off(Node.EventType.TOUCH_END);
                box.getChildByName('time').active = false;
            });

            res.d.ListChest.forEach((data: any, index: number) => {
                if (data.GameID != this.gameID) {
                    return;
                }

                let awardTime = data.AwardTimeCount;
                if (data.ExpireTimeCount < 0) {
                    return;
                }

                for (var i = 0; i < 4; i++) {
                    let box: Node = this.boxes[i];
                    if (box.getComponent(Sprite).spriteFrame != this.sprFrameBoxDefault) {
                        continue;
                    }

                    if (awardTime > 0) {
                        const prize = data.PrizeValue > 0 ? data.PrizeValue : data.SpecialGift;
                        if (prize <= 0) continue;

                        var targetFrame = this.sprFramePrizes.find(sprFrame => sprFrame.name === `prize-value-${Utils.formatMoney(prize, true)}`);
                        if (targetFrame) {
                            box.getComponent(Sprite).spriteFrame = targetFrame;
                            let timeNode = box.getChildByName('time');
                            timeNode.active = true;

                            clearInterval(this.countdownSHIntervals[index]);
                            this.countdownSHIntervals[index] = setInterval(() => {
                                if (awardTime < 0) {
                                    clearInterval(this.countdownSHIntervals[index]);
                                    timeNode.getComponentInChildren(Label).string = "00:00";
                                    this.loadEventChestCheck();
                                    return;
                                }

                                var minutes = Math.floor(awardTime / 60);
                                var seconds = awardTime % 60;
                                timeNode.getComponentInChildren(Label).string = `${minutes < 10 ? '0' : ''}${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                                awardTime--;
                            }, 1000);
                        }

                        break;
                    }

                    box.getComponent(Sprite).spriteFrame = this.sprFrameBoxes[data.PrizeID - 1];
                    let effect = box.getChildByName('effect');
                    if (effect && shouldShowEffects[index]) {
                        effect.active = true;
                        var opacity = effect.getComponent(UIOpacity) || effect.addComponent(UIOpacity);
                        opacity.opacity = 255;
                        tween(opacity)
                            .repeatForever(
                                tween()
                                    .to(0.5, {opacity: 128}, {easing: "quadIn"})
                                    .to(0.5, {opacity: 255}, {easing: "quadOut"})
                            )
                            .start();

                        setTimeout(() => {
                            effect.active = false;
                        }, 5000);
                    }
                    box.on(Node.EventType.TOUCH_END, () => {
                        if (data.AwardTimeCount > 0) {
                            this.showToast(App.instance.getTextLang("ev43"));
                        }

                        this.openEventChest(box, data.ChestID);
                    });
                    break;
                }
            });
        });
    }

    private openEventChest(_box: Node, ChestID: number) {
        Http.post(Configs.App.DOMAIN_CONFIG['PostEventChestOpen'], {GameID: this.getCurrentGameID(), CurrencyID: Configs.Login.CurrencyID, ChestID: ChestID}, (_status, res) => {
            if (res.c < 0) {
                this.showToast(App.instance.getTextLang(`me${res.c}`));
                return;
            }

            var data = res.d;

            var award: string;
            if (data.PrizeValue === 0 && data.SpecialGift === 0) {
                award = "";
            } else {
                if (data.PrizeValue > 0) {
                    award = Utils.formatNumber(data.PrizeValue);
                } else {
                    if (data.SpecialGift == 1) {
                        award = "SH";
                    } else if (data.SpecialGift == 2) {
                        award = "Iphone";
                    } else {
                        award = "";
                    }
                }
            }

            this.showToast(App.instance.getTextLang("txt_reward") + " " + award);

            this.loadEventChestCheck();
        });
    }
}