import { _decorator, Component, Node, Label, Sprite, Sprite<PERSON>rame, EditBox, ToggleContainer, Button, Prefab, instantiate, v2, Vec2, v3, WebView, Animation, Event, EventTouch, Tween, tween, UIOpacity } from 'cc';
import Configs from "db://assets/Lobby/scripts/common/Config";
import {Utils} from "db://assets/Lobby/scripts/common/Utils";
import App from "db://assets/Lobby/scripts/common/App";
import Http from "db://assets/Lobby/scripts/common/Http";
import MiniGameTXMD5SignalRClient from "db://assets/Lobby/scripts/common/networks/MiniGameTXMD5SignalRClient";
import {BroadcastReceiver} from "db://assets/Lobby/scripts/common/BroadcastListener";
import ButtonMiniGame from "db://assets/Lobby/scripts/common/ButtonMiniGame";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("TaiXiuLiveMD5/Controller")
export default class TaiXiuLiveMD5Controller extends Component {

    static instance: TaiXiuLiveMD5Controller = null;

    GAME_ID: number = 123;

    @property(Sprite)
    avatar: Sprite = null;
    @property(Label)
    labelBalance: Label = null;
    @property(Label)
    labelNickname: Label = null;

    @property(Node)
    leftContainer: Node = null;
    @property(Node)
    rightContainer: Node = null;
    @property(Node)
    centerLabels: Node = null;
    @property(Node)
    JackpotNode: Node = null; // will hide for MD5
    @property(Node)
    MD5ContainerNode: Node = null; // will show for MD5
    @property(Node)
    EventButtonLiveJP: Node = null; // will hide for MD5

    @property(SpriteFrame)
    sprFrameTai: SpriteFrame = null;
    @property(SpriteFrame)
    sprFrameXiu: SpriteFrame = null;
    @property(Node)
    containerTimer: Node = null;
    @property(Label)
    lblSession: Label = null;
    @property(Label)
    lblRemainTime: Label = null;
    @property(Label)
    lblRemainWaiting: Label = null;
    @property(Label)
    lblSumDices: Label = null;
    @property(Label)
    lblTotalBetTaiCurrent: Label = null;
    @property(Label)
    lblTotalBetXiuCurrent: Label = null;
    @property(Label)
    lblBetTai: Label = null;
    @property(Label)
    lblBetXiu: Label = null;
    @property(Label)
    lblUserTai: Label = null;
    @property(Label)
    lblUserXiu: Label = null;

    @property(Node)
    winTextTaiNode: Node = null;
    @property(Node)
    winTextXiuNode: Node = null;
    @property(EditBox)
    editBoxBetTai: EditBox = null;
    @property(EditBox)
    editBoxBetXiu: EditBox = null;
    @property(Node)
    confirmBetButton: Node = null;
    @property(Node)
    cancelBetButton: Node = null;
    @property(Button)
    x2BetButton: Button = null;
    @property(Node)
    chipContainer: Node = null;
    @property(Node)
    layoutBetChip: Node = null;
    @property(Node)
    layoutBetNumber: Node = null;

    @property(Label)
    labelTextMD5: Label = null;

    @property(Node)
    dicesContainer: Node = null;
    @property([SpriteFrame])
    listSprDice: SpriteFrame[] = [];
    @property(Node)
    gateTaiWin: Node = null;
    @property(Node)
    nodeTaiWin: Node = null;
    @property(Node)
    gateXiuWin: Node = null;
    @property(Node)
    nodeXiuWin: Node = null;
    @property(Node)
    bowl: Node = null;
    @property(Button)
    buttonNan: Button = null;
    @property(Label)
    lblToast: Label = null;
    @property(Node)
    historyList: Node = null;
    @property(Node)
    historyItem: Node = null;

    // Box
    @property(Node)
    chatBox: Node = null;
    @property(Node)
    settingBox: Node = null;
    @property(Node)
    statisticalBox: Node = null;

    // Popup Container
    @property(Node)
    popupContainer: Node = null;
    @property(Prefab)
    popupDetailSessionPrefab: Prefab = null;
    @property(Prefab)
    popupHistoryPrefab: Prefab = null;
    @property(Prefab)
    popupHonorsPrefab: Prefab = null;
    @property(Prefab)
    popupGuidePrefab: Prefab = null;

    // LIVE
    @property(Node)
    liveNode: Node = null;
    @property(WebView)
    webView: WebView = null;
    @property(Node)
    liveNodeMini: Node = null;
    @property(WebView)
    webViewMini: WebView = null;
    private streamURL: string = "";
    private currentStreamID = -1;

    private popupDetailSession: any = null;
    private popupHistory: any = null;
    private popupHonors: any = null;
    private popupGuide: any = null;

    private countdownRemainTime: Function = null;
    private countdownWaitingTime: Function = null;
    private isBetting = false;
    private betTypeLocation: number = 0;
    private resetLabels = [];
    private lastLocationIDWin: number = 0;
    private readonly bowlStartPos = v2(0, 25);
    private isOpenBowl = false;
    private lastScore: number = 0;
    private isNan: boolean = false;
    private plainTextMD5: string = "";
    private detailSessions: any[] = [];
    private isBowlMovable: boolean = false;
    private isBetTypeGold = true; // ALWAYS true for LIVE
    private readonly STREAM_TEMPLATE = "https://demo.nanocosmos.de/nanoplayer/embed/1.3.3/nanoplayer.html?group.id=STREAM_ID&group.security.jwtoken=JWT_TOKEN";
    private sessionId: number = 0;
    private betTypeSelected: number = 0;
    private betLogs: any[] = [];

    public onLoad() {
        this.resetLabels = [
            this.lblTotalBetTaiCurrent,
            this.lblTotalBetXiuCurrent,
        ];
        this.lblToast.node.parent.active = false;
        this.bowl.active = true;
        this.labelTextMD5.string = "";
        this.dicesContainer.getChildByName('result').active = false;
        this.leftContainer.active = false;
        this.rightContainer.active = false;
        this.centerLabels.active = false;
        this.JackpotNode.active = false;
        this.EventButtonLiveJP.active = false;
        this.MD5ContainerNode.active = true;

        const formatBetValue = (editBox: EditBox) => {
            let value = parseInt(editBox.string.replace(/\./g, ""));
            if (isNaN(value) || value <= 0) {
                editBox.string = "";
            } else {
                editBox.string = Utils.formatNumber(value);
            }
        };

        this.editBoxBetTai.node.on("editing-did-began", () => {
            if (this.editBoxBetTai.string == "") return;
            this.editBoxBetTai.string = Utils.formatEditBox(this.editBoxBetTai.string).toString();
        });

        this.editBoxBetXiu.node.on("editing-did-began", () => {
            if (this.editBoxBetXiu.string == "") return;
            this.editBoxBetXiu.string = Utils.formatEditBox(this.editBoxBetXiu.string).toString();
        });

        this.editBoxBetTai.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("editing-did-ended", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        this.editBoxBetTai.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetTai);
        });

        this.editBoxBetXiu.node.on("text-changed", () => {
            formatBetValue(this.editBoxBetXiu);
        });

        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            setTimeout(() => {
                if (this.labelBalance) {
                    this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
                }
            }, 1000);
        }, this);
    }

    protected onEnable() {
        TaiXiuLiveMD5Controller.instance = this;
    }

    protected start() {
        this.bowl.on(
            Node.EventType.TOUCH_MOVE,
            (event: EventTouch) => {
                if (this.isBowlMovable === false) {
                    return;
                }
                var pos = this.bowl.getPosition();
                pos.x += event.getDeltaX();
                pos.y += event.getDeltaY();
                this.bowl.position = pos;

                let distance = Utils.v2Distance(
                    new Vec2(pos.x, pos.y),
                    this.bowlStartPos
                );
                if (Math.abs(distance) > 200) {
                    this.showResult();
                    this.isOpenBowl = true;
                    this.scheduleOnce(() => {
                        this.bowl.active = false;
                    }, 2);
                }
            },
            this
        );

        this.getStreamURL();

        this.settingBox.active = false;
        this.statisticalBox.active = false;
        this.chatBox.active = false;

        this.labelNickname.string = Configs.Login.Nickname;
        this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
        this.avatar.spriteFrame = App.instance.getAvatarSpriteFrame(Configs.Login.Avatar);

        MiniGameTXMD5SignalRClient.getInstance().send("GetCurrentRoomsMD5", [{GameID: this.GAME_ID, CurrencyID: Configs.Login.CurrencyID, BetType: this.isBetTypeGold ? 1 : 2}], (_response) => {})
        this.initHubs();
    }

    getStreamURL() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetLiveStreamInfo'], {gameId: Configs.InGameIds.TaiXiuMD5}, (status, res) => {
            if (res.c < 0 || status !== 200) {
                return;
            }

            var streamObj = res.d.find((item: any) => item.gameID === this.GAME_ID);
            if (streamObj && streamObj.streamID && streamObj.tokenPlayBack) {
                if (this.currentStreamID !== streamObj.streamID) {
                    this.currentStreamID = streamObj.streamID;
                    this.streamURL = this.STREAM_TEMPLATE.replace("STREAM_ID", streamObj.streamID).replace("JWT_TOKEN", streamObj.tokenPlayBack);
                }
            } else {
                this.streamURL = "";
            }
        });
    }

    protected update(_dt: number) {
        if (this.streamURL === "") {
            this.liveNode.active = false;
            this.liveNodeMini.active = false;
            return;
        }

        if (this.webView.url !== this.streamURL) {
            this.webView.url = this.streamURL;
            this.webViewMini.url = this.streamURL;
        }

        let isShowWebView = true;
        let isShowMiniWebView = false;

        for (let child of this.popupContainer.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (isShowWebView) {
            for (let child of App.instance.miniGameNode.children) {
                if (child.active) {
                    isShowWebView = false;
                    isShowMiniWebView = true;
                    break;
                }
            }
        }

        for (let child of App.instance.tipzoJackpotEventX2X6Node.children) {
            if (child.active) {
                isShowWebView = false;
                break;
            }
        }

        if (ButtonMiniGame.instance.panel.active || App.instance.alertDialog.node.active) {
            isShowWebView = false;
        }

        this.liveNode.active = isShowWebView;
        if (isShowMiniWebView) {
            if (App.instance.tipzoMiniLiveNode.children.length == 0) {
                var mini = instantiate(this.liveNodeMini);
                mini.active = true;
                App.instance.tipzoMiniLiveNode.addChild(mini);
            }
        } else {
            App.instance.tipzoMiniLiveNode.removeAllChildren();
        }
    }

    leaveRoom() {
        TaiXiuLiveMD5Controller.instance = null;
        this.node.destroy();
    }

    onDestroy() {
        MiniGameTXMD5SignalRClient.getInstance().send("HideDiceMD5", [], (_response) => {});
        MiniGameTXMD5SignalRClient.getInstance().dontReceive();
        App.instance.tipzoMiniLiveNode.removeAllChildren();
        Utils.setStorageValue("last_open_game_id", "");
    }

    private handleDicesAnimationBetting() {
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;
        this.bowl.active = false;
        const animNode = this.dicesContainer.getChildByName('anim');
        animNode.active = true;
        this.dicesContainer.getChildByName('result').active = false;

        const anim1 = animNode.getChildByName('animDice1').getComponent(Animation);
        const anim2 = animNode.getChildByName('animDice2').getComponent(Animation);
        const anim3 = animNode.getChildByName('animDice3').getComponent(Animation);

        anim1.play();
        anim2.play();
        anim3.play();

        this.scheduleOnce(() => {
            anim1.stop();
            anim2.stop();
            anim3.stop();
            animNode.active = false;
            this.bowl.active = true;
            this.bowl.setPosition(v3(this.bowlStartPos.x, this.bowlStartPos.y, 0));
            this.isBowlMovable = false;
            this.lblRemainTime.node.active = true;
        }, 3);
    }

    private handleOpenBowl() {
        this.isOpenBowl = true;
        tween(this.bowl)
            .to(0.5, {position: v3(250, 150, 0)})
            .call(() => {
                this.showResult();
                this.scheduleOnce(() => {
                    this.bowl.active = false;
                }, 2);
            })
            .start();
    }

    private initHubs() {
        MiniGameTXMD5SignalRClient.getInstance().receive("currentSessionMD5", (res) => {
            this.lblSession.string = `#${res.GameSessionID}`;
            this.sessionId = res.GameSessionID;
            this.unschedule(this.countdownRemainTime);
            this.unschedule(this.countdownWaitingTime);
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            if (res.GameStatus === 1) {
                this.getStreamURL();
                this.handleDicesAnimationBetting();
                this.labelTextMD5.string = res.MD5;
                this.handleBettingPhase(res.RemainBetting);
            } else {
                var remainWaiting = res.RemainWaiting;
                this.handleWaitingPhase(remainWaiting);

                if (remainWaiting > 15) {
                    this.scheduleOnce(() => {
                        MiniGameTXMD5SignalRClient.getInstance().send("GetAccountResultMD5", [{GameID: this.GAME_ID, CurrencyID: Configs.Login.CurrencyID, GameSessionID: res.GameSessionID}], () => {});
                    }, remainWaiting - 15);
                }
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("currentResultMD5", (res) => {
            this.isBowlMovable = true;
            this.plainTextMD5 = res.PlainText;
            this.editBoxBetTai.enabled = false;
            this.editBoxBetXiu.enabled = false;
            this.buttonNan.enabled = false;
            this.lblRemainWaiting.node.parent.active = false;
            this.lblSumDices.node.parent.active = false;
            const resultNode = this.dicesContainer.getChildByName('result');
            resultNode.active = true;
            const dice_1 = resultNode.getChildByName('dice_1');
            const dice_2 = resultNode.getChildByName('dice_2');
            const dice_3 = resultNode.getChildByName('dice_3');
            dice_1.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice1 - 1];
            dice_2.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice2 - 1];
            dice_3.getComponent(Sprite).spriteFrame = this.listSprDice[res.Dice3 - 1];
            this.lastLocationIDWin = res.LocationIDWin;
            this.lastScore = res.Dice1 + res.Dice2 + res.Dice3;
            if (this.isNan) {
                this.scheduleOnce(() => {
                    if (this.isOpenBowl) {
                        return;
                    }
                    this.handleOpenBowl();
                }, 15);
            } else {
                this.handleOpenBowl();
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("currentRoomsInfoMD5", (res) => {
            for (const room of res) {
                if (room == null) continue;

                const isGoldRoom = room.BetType === 1;
                const isXuRoom = room.BetType === 2;

                if ((this.isBetTypeGold && isXuRoom) || (!this.isBetTypeGold && isGoldRoom)) {
                    continue;
                }

                const betInfo = room.BetInfo;

                this.lblTotalBetXiuCurrent.string = Utils.formatNumber(betInfo[11].TotalBetValue1);
                this.lblTotalBetTaiCurrent.string = Utils.formatNumber(betInfo[11].TotalBetValue2);
                this.lblUserXiu.string = `(${Utils.formatNumber(betInfo[11].TotalAccount1)})`;
                this.lblUserTai.string = `(${Utils.formatNumber(betInfo[11].TotalAccount2)})`;

                if (Configs.Login.PortalID > 10) {
                    // this.lblTotalBetXiuAll.string = Utils.formatMoney(betInfo[88].TotalBetValue1, true);
                    // this.lblTotalBetTaiAll.string = Utils.formatMoney(betInfo[88].TotalBetValue2, true);
                    this.lblUserXiu.string = `(${Utils.formatNumber(betInfo[11].TotalAccount1 + betInfo[88].TotalAccount1)})`;
                    this.lblUserTai.string = `(${Utils.formatNumber(betInfo[11].TotalAccount2 + betInfo[88].TotalAccount2)})`;
                }
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("gameHistoryMD5", (res) => {
            this.historyList.removeAllChildren();
            if (res == null || res.length == 0) {
                return;
            }
            this.detailSessions = [];
            for (let i = res.length - 1; i >= 0; i--) {
                this.detailSessions.push(res[i]);

                let item = instantiate(this.historyItem);
                item.getComponent(Sprite).spriteFrame = res[i].LocationIDWin == 1 ? this.sprFrameXiu : this.sprFrameTai;
                item.getChildByName("last").active = i === 0;
                item.on(Node.EventType.TOUCH_END, () => {
                    this.actPopupDetailSession(res[i].GameSessionID);
                });

                if (i === 0) {
                    Tween.stopAllByTarget(item);
                    this.scheduleOnce(() => {
                        const posUp = v3(item.position.x, 5, item.position.z);
                        const posDown = v3(item.position.x, -5, item.position.z);

                        tween(item)
                            .repeatForever(
                                tween()
                                    .to(0.3, { position: posUp })
                                    .to(0.3, { position: posDown })
                            )
                            .start();
                    }, 0);
                }

                this.historyList.addChild(item);
            }
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("betOfAccountMD5", (res) => {
            res.forEach((item: any) => {
                this.editBoxBetTai.string = '';
                this.editBoxBetXiu.string = '';

                if (item.BetType == 1) {
                    const betLocation = item.LocationID == 1 ? this.lblBetXiu : this.lblBetTai;
                    betLocation.string = Utils.formatNumber(item.BetValue);
                    this.betTypeSelected = item.LocationID;
                }
            })
        });

        MiniGameTXMD5SignalRClient.getInstance().receive("resultOfAccountMD5", (res) => {
            let totalPrize = 0;

            res.forEach((item: any) => {
                if (item.AccountID === Configs.Login.AccountID && item.PortalID === Configs.Login.PortalID && item.PrizeValue > 0) {
                    totalPrize += item.PrizeValue;
                }
            });

            const winTextNode = this.betTypeSelected === 1 ? this.winTextXiuNode : this.winTextTaiNode;

            if (totalPrize <= 0) {
                winTextNode.active = false;
                return;
            }

            winTextNode.active = true;
            winTextNode.getComponentInChildren(Label).string = '+ ' + Utils.formatNumber(totalPrize);
            winTextNode.position = v3(winTextNode.x, -60);
            tween(winTextNode)
                .to(3, {position: v3(winTextNode.x, 60)})
                .call(() => {
                    winTextNode.active = false;
                })
                .start();

            this.updateBalanceTaiXiu();
        });
    }

    private handleBettingPhase(remainTime: number) {
        if (remainTime === 60) {
            this.showToast(App.instance.getTextLang("txt_taixiu_new_session"));
            this.isOpenBowl = false;
            this.resetSessionLabels();
            this.lblBetTai.string = "0";
            this.lblBetXiu.string = "0";
            this.lastScore = 0;
            this.lblSumDices.string = "";
            this.lblRemainTime.string = "";
            this.confirmBetButton.active = false;
            this.cancelBetButton.active = false;
            this.clearChipToggle();
            this.hideResult();
            this.plainTextMD5 = "";
            this.actCancelBet();
            this.x2BetButton.enabled = this.betLogs.find((item) => item.sessionID === this.sessionId - 1) != undefined;
        }

        if (remainTime < 3) {
            this.isBetting = false;
        }

        this.isBetting = true;
        this.editBoxBetTai.enabled = true;
        this.editBoxBetXiu.enabled = true;
        this.buttonNan.enabled = true;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;

        let secondsLeft = remainTime;
        this.unschedule(this.countdownRemainTime);
        this.schedule(this.countdownRemainTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownRemainTime);
                    return;
                }

                if (secondsLeft <= 5) {
                    this.isBetting = false;
                }

                this.containerTimer.active = true;
                this.lblRemainTime.string = secondsLeft < 10 ? `0${secondsLeft}` : `${secondsLeft}`;

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownRemainTime);
            }
        }, 1);
    }

    private resetSessionLabels() {
        this.resetLabels.forEach(label => label.string = "0");
        this.lblUserTai.string = "(0)";
        this.lblUserXiu.string = "(0)";
    }

    private handleWaitingPhase(waitingTime: number) {
        if (waitingTime > 0) this.isBetting = false;

        if (waitingTime < 19) {
            MiniGameTXMD5SignalRClient.getInstance().send("GetCurrentResultMD5", [{ GameID: this.GAME_ID }], () => {});
        }

        this.x2BetButton.enabled = false;
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
        this.gateXiuWin.active = false;
        this.gateTaiWin.active = false;
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
        this.containerTimer.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;
        let secondsLeft = waitingTime;
        this.unschedule(this.countdownWaitingTime);
        this.schedule(this.countdownWaitingTime = () => {
            try {
                if (secondsLeft < 0) {
                    this.unschedule(this.countdownWaitingTime);
                    return;
                }

                if (this.isOpenBowl === false && secondsLeft > 15) {
                    const secondsLeftOpenBowl = secondsLeft - 15;
                    this.lblRemainWaiting.string = `${secondsLeftOpenBowl < 10 ? "0" + secondsLeftOpenBowl : secondsLeftOpenBowl}`;
                    this.lblRemainWaiting.node.parent.active = true;
                    this.lblSumDices.node.parent.active = false;
                } else {
                    this.lblSumDices.node.parent.active = secondsLeft > 12;
                    this.lblRemainWaiting.node.parent.active = secondsLeft <= 12;
                    this.lblRemainWaiting.string = `${secondsLeft < 10 ? "0" + secondsLeft : secondsLeft}`;
                }

                secondsLeft--;
            } catch (e: any) {
                this.unschedule(this.countdownWaitingTime);
            }
        }, 1);
    }

    actBet(betValue: number) {
        MiniGameTXMD5SignalRClient.getInstance().send("SetBetMd5", [{
            GameID: this.GAME_ID,
            CurrencyID: Configs.Login.CurrencyID,
            BetType: 1, // always gold
            Location: this.betTypeLocation,
            Amount: betValue
        }], (res) => {
            if (res < 0) {
                this.showToast(App.instance.getTextLang(`me${res}`));
                this.actCancelBet();
                return;
            }

            this.betTypeSelected = this.betTypeLocation;
            this.showToast(App.instance.getTextLang("tx3_live").replace('{0}', this.betTypeLocation === 1 ? App.instance.getTextLang("tx44") : App.instance.getTextLang("tx43")));
            var log = this.betLogs.find((item) => item.sessionID === this.sessionId);
            if (log) {
                log.betValue += betValue;
            } else {
                this.betLogs.push({
                    location: this.betTypeLocation,
                    betValue: betValue,
                    sessionID: this.sessionId
                })
            }

            this.editBoxBetTai.string = "";
            this.editBoxBetXiu.string = "";
            this.gateXiuWin.active = false;
            this.gateTaiWin.active = false;
            this.betTypeLocation = 0;
            this.clearChipToggle();
            this.updateBalanceTaiXiu();
            this.x2BetButton.enabled = false;
            this.betLogs = this.betLogs.filter(log => {
                return log.sessionID !== this.sessionId - 1;
            });
        });
    }

    x2Bet() {
        var log = this.betLogs.find((item) => item.sessionID === this.sessionId - 1);
        if (!this.isBetting || !log) return;

        var edb = log.location == 1 ? this.editBoxBetXiu : this.editBoxBetTai;
        edb.string = Utils.formatNumber((log.betValue * 2));
        this.betTypeLocation = log.location;
        this.gateXiuWin.active = log.location == 1;
        this.gateTaiWin.active = log.location == 2;
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
    }

    updateBalanceTaiXiu() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
            if (status === 200) {
                Configs.Login.GoldBalance = json['d'][0]['goldBalance'];
                Configs.Login.CoinBalance = json['d'][0]['coinBalance'];

                this.labelBalance.string = Utils.formatNumber(Configs.Login.GoldBalance);
            }
        });
    }

    updateCurrentBetType(_event: Event, type: string) {
        if (!this.isBetting || this.betTypeLocation == parseInt(type)) return;
        this.betTypeLocation = parseInt(type);
        this.gateXiuWin.active = type == "1";
        this.gateTaiWin.active = type == "2";
        this.confirmBetButton.active = true;
        this.cancelBetButton.active = true;
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    selectedChip(event: any, value: string) {
        if (!this.isBetting) return;

        this.clearChipToggle();
        if (this.betTypeLocation == 0) {
            return;
        }

        var target = event.target;
        target.getChildByName('checkmark').active = true;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = currentAmount + parseInt(value);
        this.setEditBoxValue(editBox, newAmount);
    }

    clearChipToggle() {
        this.chipContainer.children.forEach((item) => {
            item.getChildByName('checkmark').active = false;
        })
    }

    actConfirmBet() {
        if (!this.isBetting) {
            this.showToast(App.instance.getTextLang("me-207"));
            this.actCancelBet();
            return;
        }
        if (this.betTypeLocation == 0) {
            return;
        }

        var editBox = this.betTypeLocation == 1 ? this.editBoxBetXiu : this.editBoxBetTai;
        var betValue = Utils.formatEditBox(editBox.string);
        if (betValue <= 0 || isNaN(betValue)) {
            this.showToast(App.instance.getTextLang("me-60212"));
            return;
        }

        this.actBet(betValue);
        this.confirmBetButton.active = false;
        this.cancelBetButton.active = false;
    }

    actCancelBet() {
        this.editBoxBetTai.string = "";
        this.editBoxBetXiu.string = "";
    }

    showResult() {
        var nodeResult: Node;
        this.hideResult();

        if (this.lastLocationIDWin === 1) {
            nodeResult = this.nodeXiuWin;
            this.gateXiuWin.active = true;
            this.gateTaiWin.active = false;
            this.nodeTaiWin.active = false;
        } else if (this.lastLocationIDWin === 2) {
            nodeResult = this.nodeTaiWin;
            this.gateTaiWin.active = true;
            this.gateXiuWin.active = false;
            this.nodeXiuWin.active = false;
        } else {
            return;
        }
        nodeResult.active = true;
        var nodeResultOpacity = nodeResult.getComponent(UIOpacity);
        tween(nodeResultOpacity)
            .repeatForever(
                tween()
                    .to(0.2, { opacity: 100 })
                    .to(0.2, { opacity: 255 })
            )
            .start();

        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = true;
        this.lblSumDices.string = this.lastScore.toString();
        this.labelTextMD5.string = this.plainTextMD5;
    }

    hideResult() {
        Tween.stopAllByTarget(this.nodeTaiWin);
        Tween.stopAllByTarget(this.nodeXiuWin);
        this.nodeTaiWin.active = false;
        this.nodeXiuWin.active = false;
        this.gateTaiWin.active = false;
        this.gateXiuWin.active = false;
        this.lblRemainWaiting.node.parent.active = false;
        this.lblSumDices.node.parent.active = false;
    }

    toggleLayoutBet(event: any) {
        var target = event.target;
        var textOther = target.getChildByName('text');
        var betFast = target.getChildByName('betFast');
        this.layoutBetChip.active = !this.layoutBetChip.active;
        this.layoutBetNumber.active = !this.layoutBetNumber.active;
        betFast.active = !this.layoutBetChip.active;
        textOther.active = !this.layoutBetNumber.active;
        this.actCancelBet();
    }

    private getCurrentEditBox(): EditBox {
        return this.betTypeLocation === 1 ? this.editBoxBetXiu : this.editBoxBetTai;
    }

    private setEditBoxValue(editBox: EditBox, value: number) {
        editBox.string = (isNaN(value) || value === 0) ? "" : Utils.formatNumber(value);
    }

    updateBetAmountCustom(_event: Event, amount: string) {
        if (this.betTypeLocation == 0) return;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string);
        const newAmount = parseInt(currentAmount.toString() + amount);
        this.setEditBoxValue(editBox, newAmount);
    }

    deleteBetAmount() {
        if (this.betTypeLocation == 0) return;

        const editBox = this.getCurrentEditBox();
        let currentAmount = Utils.formatEditBox(editBox.string).toString();
        const newAmount = currentAmount.length > 1 ? parseInt(currentAmount.slice(0, -1)) : 0;
        this.setEditBoxValue(editBox, newAmount);
    }

    copyTextMD5() {
        Utils.copy(this.labelTextMD5.string);
        this.showToast(App.instance.getTextLang('txt_copied'));
    }

    toggleNan(event: any) {
        var target = event.target;
        var on = target.getChildByName('on');
        var off = target.getChildByName('off');
        on.active = !on.active;
        off.active = !off.active;

        this.isNan = !this.isNan;
    }

    toggleChatBox() {
        this.chatBox.active = !this.chatBox.active;
    }

    toggleSettingBox() {
        this.settingBox.active = !this.settingBox.active;
    }

    toggleStatisticalBox() {
        this.statisticalBox.active = !this.statisticalBox.active;
    }

    public showToast(message: string) {
        this.lblToast.string = message;

        const parent = this.lblToast.node.parent!;
        const uiOpacity = parent.getComponent(UIOpacity) || parent.addComponent(UIOpacity);

        Tween.stopAllByTarget(uiOpacity);
        parent.active = true;
        uiOpacity.opacity = 0;

        tween(uiOpacity)
            .to(0.1, { opacity: 255 })
            .delay(2)
            .to(0.2, { opacity: 0 })
            .call(() => {
                parent.active = false;
            })
            .start();
    }

    actPopupDetailSession(session: number) {
        if (this.popupDetailSession == null) {
            this.popupDetailSession = instantiate(this.popupDetailSessionPrefab).getComponent("TaiXiuLiveMD5.PopupDetailSession");
            this.popupDetailSession.node.parent = this.popupContainer;
            this.popupDetailSession.showDetail(session, this.detailSessions);
            App.instance.showLoading(false);
        } else {
            this.popupDetailSession.showDetail(session, this.detailSessions);
        }
    }

    actPopupHistory() {
        if (this.popupHistory == null) {
            this.popupHistory = instantiate(this.popupHistoryPrefab).getComponent("TaiXiuLiveMD5.PopupHistory");
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.show();
            App.instance.showLoading(false);
        } else {
            this.popupHistory.show();
        }
    }

    actPopupHonors() {
        if (this.popupHonors == null) {
            this.popupHonors = instantiate(this.popupHonorsPrefab).getComponent("TaiXiuLiveMD5.PopupHonors");
            this.popupHonors.node.parent = this.popupContainer;
            this.popupHonors.showDetail(this.isBetTypeGold);
            App.instance.showLoading(false);
        } else {
            this.popupHonors.showDetail(this.isBetTypeGold);
        }
    }

    actPopupGuide() {
        if (this.popupGuide == null) {
            this.popupGuide = instantiate(this.popupGuidePrefab).getComponent("Dialog");
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
            App.instance.showLoading(false);
        } else {
            this.popupGuide.show();
        }
    }
}
