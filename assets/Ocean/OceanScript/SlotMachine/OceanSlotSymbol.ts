import { _decorator, Color, Sprite, SpriteAtlas } from 'cc';
import { BaseSlotSymbol, SymbolState } from "db://assets/Lobby/scripts/common/slot/BaseSlotSymbol";
import { OceanController } from "../OceanController";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Ocean/OceanSlotSymbol")
export default class OceanSlotSymbol extends BaseSlotSymbol {

    @property(SpriteAtlas) room1: SpriteAtlas = null;
    @property(SpriteAtlas) room2: SpriteAtlas = null;
    @property(SpriteAtlas) room3: SpriteAtlas = null;

    protected onLoad() {
        OceanController.getInstance().addObserver(this.node.uuid, {
            onChangeRoom: (sender: OceanController, roomID: number) => {
                this.onRoomChange(roomID);
            }
        });

        this.setStateHandler({
            [SymbolState.INIT]: async () => {
                this.defaultAnimation.node.active = false;
                this.symbol.node.getComponent(Sprite).color = new Color().fromHEX('#FFFFFF');
                return Promise.resolve();
            },
            [SymbolState.HIDE]: async () => {
                this.defaultAnimation.node.active = false;
                this.symbol.node.getComponent(Sprite).color = new Color().fromHEX('#4C4C4C');
                return Promise.resolve();
            },
            [SymbolState.HIGHLIGHT]: async () => {
                this.defaultAnimation.node.active = true;
                this.defaultAnimation.play("Fx_Symbol");
                return new Promise((resolve) => {
                    this.scheduleOnce(() => {
                        resolve();
                    }, 1);
                });
            }
        });
    }

    private onRoomChange(roomID: number): void {
        switch (roomID) {
            case 1:
                this.defaultAtlas = this.room1;
                break;
            case 2:
                this.defaultAtlas = this.room2;
                break;
            case 3:
                this.defaultAtlas = this.room3;
                break;
        }
        super.show();
    }

    protected onDestroy(): void {
        OceanController.getInstance().removeObserver(this.node.uuid);
    }




}
