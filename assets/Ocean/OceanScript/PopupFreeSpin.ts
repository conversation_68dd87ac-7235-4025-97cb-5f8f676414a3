import { _decorator, Component, Node, Label, tween, Tween } from "cc";
import Dialog from "../../Lobby/scripts/common/Dialog";
import { TweenUtils } from "../../Lobby/scripts/common/TweenUtils";
import { Utils } from "../../Lobby/scripts/common/Utils";

const {ccclass, property, menu} = _decorator;

@ccclass
@menu("Ocean/PopupFreeSpin")
export class PopupFreeSpin extends Dialog {
    @property(Label)
    lblReward: Label = null;

    private _resolve: () => void = null;

    public showPopup(reward: number) : Promise<void>{
        return new Promise<void>((resolve) => {
            this._resolve = resolve;
            this.lblReward.string = '';
            Utils.numberTo(this.lblReward, reward, 0.3);
            super.show();
        });
    }

    public dismiss() {
        super.dismiss();
    }

    _onShowed() {
        super._onShowed();
        this.scheduleOnce(() => {
            this.dismiss();
        }, 3);
    }

    _onDismissed() {
        super._onDismissed();
        if (this._resolve) {
            this._resolve();
            this._resolve = null;
        }
    }
}
