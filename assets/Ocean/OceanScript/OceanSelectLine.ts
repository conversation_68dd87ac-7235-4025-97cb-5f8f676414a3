
import { _decorator, Button, Component, Node, Sprite, Sprite<PERSON>rame, Toggle } from "cc";
import AudioManager, { AUDIO_CLIP } from "./AudioManager";

const { ccclass, property, menu } = _decorator;

@ccclass
@menu("Ocean/OceanSelectLine")
export class OceanSelectLine extends Component {
    @property(Node)
    private buttonLines: Node = null;

    @property(Node)
    private btnEven: Node = null;
    @property(Node)
    private btnOdd: Node = null;
    @property(Node)
    private btnAll: Node = null;
    @property(Node)
    private btnNone: Node = null;

    @property(SpriteFrame)
    private pressedSpriteFrame: SpriteFrame = null;

    @property(SpriteFrame)
    private normalSpriteFrame: SpriteFrame = null;

    private _onSelectedChanged: (selectedLines: number[]) => void = null;

    public setOnSelectedChanged(fn: (selectedLines: number[]) => void) {
        this._onSelectedChanged = fn;
    }

    protected onLoad() {
        this.buttonLines.children.forEach((btn, index) => {
            btn.on("toggle", () => {
                this.updateSelectedLines();
            });
        });
        this.updateSelectedLines();
    }

    public setLines(lines: string) {
        let parsedLines = lines.split(",").map(Number);
        this.buttonLines.children.forEach((btn, index) => {
            btn.getComponent(Toggle).isChecked = parsedLines.includes(index + 1);
        });
        this.updateSelectedLines();
    }

    private updateSelectedLines() {
        const selectedLines = this.getSelectedLines();
        this._onSelectedChanged?.(selectedLines);
    }

    public getSelectedLines(): number[] {
        const selectedLines: number[] = [];
        this.buttonLines.children.forEach((btn, index) => {
            if (btn.getComponent(Toggle).isChecked) {
                selectedLines.push(index + 1);
            }
        });
        return selectedLines;
    }

    private toggleLines(condition: (index: number) => boolean) {
        this.buttonLines.children.forEach((btn, index) => {
            btn.getComponent(Toggle).isChecked = condition(index + 1);
        });
        this.updateSelectedLines();
    }

    activeAllLines() {
        this.toggleLines(() => true);
    }

    deactiveAllLines() {
        this.toggleLines(() => false);
    }

    activeEvenLines() {
        this.toggleLines(index => index % 2 === 0);
    }

    activeOddLines() {
        this.toggleLines(index => index % 2 !== 0);
    }

    onClickEven() {
        this.btnEven.getComponent(Sprite).spriteFrame = this.pressedSpriteFrame;
        this.btnOdd.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnAll.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnNone.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.activeEvenLines();
    }

    onClickOdd() {
        this.btnEven.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnOdd.getComponent(Sprite).spriteFrame = this.pressedSpriteFrame;
        this.btnAll.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnNone.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.activeOddLines();
    }

    onClickAll() {
        this.btnEven.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnOdd.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnAll.getComponent(Sprite).spriteFrame = this.pressedSpriteFrame;
        this.btnNone.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.activeAllLines();
    }

    onClickNone() {
        this.btnEven.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnOdd.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnAll.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnNone.getComponent(Sprite).spriteFrame = this.pressedSpriteFrame;
        this.deactiveAllLines();
    }

    deactiveButtons() {
        this.btnEven.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnOdd.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnAll.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
        this.btnNone.getComponent(Sprite).spriteFrame = this.normalSpriteFrame;
    }

    show() {
        this.node.active = true;
    }

    dismiss() {
        this.node.active = false;
    }

}
