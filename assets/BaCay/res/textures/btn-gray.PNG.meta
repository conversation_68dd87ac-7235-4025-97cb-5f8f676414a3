{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "d876edc4-98af-41cd-9f2d-17a769c7bf1c", "files": [".json", ".png"], "subMetas": {"btn-gray": {"ver": "1.0.4", "uuid": "1ddf1283-799d-4349-8d4b-bc74b54d0de9", "rawTextureUuid": "d876edc4-98af-41cd-9f2d-17a769c7bf1c", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": -154, "offsetY": -213, "trimX": 643, "trimY": 698, "width": 326, "height": 110, "rawWidth": 1920, "rawHeight": 1080, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}, "importer": "*", "imported": false, "files": [], "userData": {}, "displayName": "", "id": "", "name": ""}, "6c48a": {"importer": "texture", "uuid": "d876edc4-98af-41cd-9f2d-17a769c7bf1c@6c48a", "displayName": "btn-gray", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "nearest", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "d876edc4-98af-41cd-9f2d-17a769c7bf1c", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "d876edc4-98af-41cd-9f2d-17a769c7bf1c@f9941", "displayName": "btn-gray", "id": "f9941", "name": "spriteFrame", "userData": {"trimThreshold": 1, "rotated": false, "offsetX": -154, "offsetY": -213, "trimX": 643, "trimY": 698, "width": 326, "height": 110, "rawWidth": 1920, "rawHeight": 1080, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-163, -55, 0, 163, -55, 0, -163, 55, 0, 163, 55, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [643, 382, 969, 382, 643, 272, 969, 272], "nuv": [0.33489583333333334, 0.2518518518518518, 0.5046875, 0.2518518518518518, 0.33489583333333334, 0.3537037037037037, 0.5046875, 0.3537037037037037], "minPos": [-163, -55, 0], "maxPos": [163, 55, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "d876edc4-98af-41cd-9f2d-17a769c7bf1c@6c48a", "atlasUuid": "", "trimType": "auto"}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "d876edc4-98af-41cd-9f2d-17a769c7bf1c@6c48a"}}