import BundleControl from "../../Loading/scripts/BundleControl";

export function loadBundleBaCay(cb: () => void) {
  BundleControl.loadBundle("BaCay", () => {
    cb && cb();
  });
}

export function convertObjectToArray(obj: any): any[] {
  if (!obj) return [];

  const result = [];
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      result.push(obj[key]);
    }
  }
  return result;
}
